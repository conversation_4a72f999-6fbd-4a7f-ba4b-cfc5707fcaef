\documentclass[journal]{IEEEtran}
\usepackage{ctex}
\usepackage{amsmath,amsfonts}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{bm}
\usepackage{graphicx}
\usepackage[colorlinks, linkcolor=blue, citecolor=blue]{hyperref}
\usepackage{amsmath}
\usepackage{epstopdf}
\usepackage{booktabs}  
\usepackage{multirow}   
\usepackage{array}     
\usepackage{csquotes}
\usepackage{color}
\usepackage{amssymb}
\usepackage{enumerate}
\usepackage[numbers,sort&compress]{natbib}

\graphicspath{{fig/}}

\usepackage[ruled,linesnumbered]{algorithm2e}
\newtheorem{proposition}{Proposition}
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{corollary}{Corollary}
\newtheorem{remark}{Remark}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}
\newenvironment{proof}{{\quad \it Proof:}}{$\hfill\blacksquare$\par}
\hyphenation{}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
		T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\usepackage{balance}
\setlength{\parindent}{1em}


\begin{document}
	\section{系统模型}
	\label{sec_model}
	\subsection{网络模型}
	我们考虑一个部署在特定区域（例如 100m $\times$ 100m）内的能量收集无线传感器网络 (EH-WSN)，包含集合 $\mathcal{N}{=}\{1,2,\ldots,N\}$ 中的 $N$ 个静态传感器节点和集合 $\mathcal{K} {=}\{1,2,\ldots, K\}$ 中的 $K$ 个汇聚节点。
	   网络拓扑由图 $\mathcal{G} {=} (\mathcal{V}, \mathcal{E})$ 表示，其中 $\mathcal{V}{=}\mathcal{N}{\cup}\mathcal{K}$。传感器节点和汇聚节点的位置在该区域内随机生成，并确保生成的网络拓扑具有连通性。$\mathcal{E}{=}\{1,2,\ldots,E\}{\subseteq}\mathcal{V}{\times}\mathcal{V}$ 表示节点间的潜在通信链路（亦称数据链路）集合，链路的存在取决于节点间距是否小于最大通信范围 $R_{\max}$。
	
	EH-WSN 中的节点感知环境并生成数据，数据通过可能的多跳中继路由至某一汇聚节点。为模拟汇聚节点附近的潜在能量瓶颈，设定每个汇聚节点仅与其最近的传感器节点建立直接入向链路。其他节点到汇聚节点的数据传输必须通过多跳路由完成。我们假设如此构成的网络拓扑保证所有传感器节点至少存在一条到达某一汇聚节点的多跳路径。汇聚节点收集传入的数据，随后通过远程链路转发到远程后端服务器进行进一步处理。
	   
   在这项工作中，我们考虑 EH-WSN 中的时隙周期性数据收集应用，其中每个操作周期分为三个阶段：任务分配、数据感知和数据路由。在任务分配阶段，系统将一组数据收集任务分配给选定的节点。每个任务由四元组 $(src,sink,time,size)$ 定义，其中 $src$ 为任务分配的源节点，$sink$ 为任务数据的目的汇聚节点，$time$ 为任务分配的时隙，$size$ 为待收集的数据量（单位：比特）。在数据感知阶段，节点感知并收集指定量的数据，并将其分组为数据包以备传输。当数据量较大时，可能会生成多个数据包。令 $\mathcal{Q}{=}\{1,2,\ldots,Q\}$ 表示任务集生成的所有数据包的集合。在随后的数据路由阶段，$\mathcal{Q}$ 中准备好的数据包被路由到它们各自的目的地。只有当感知阶段收集的所有数据成功到达其指定的汇聚节点时，任务才被视为完成。本文旨在通过 M-EC 实现数据传输与能量协作的联合优化调度，以最大化网络的长期平均 (LTA) 吞吐量。数据路由阶段的时隙表示为 $\mathcal{T}{=}\{1,2,\ldots,T\}$。为简单起见，我们假设每个时隙具有单位时间长度，允许在后续讨论中互换使用功率和能量。


	\subsection{数据通信模型}
	数据通信遵循以下基本假设：(1) 节点以半双工模式运行，不能同时发送和接收数据；(2) 所有数据通信均为单播传输；(3) 节点不能同时在多个出向或入向链路处理不同数据；(4) 通过采用正交频分复用及允许远距离链路共享信道等机制，可忽略同信道干扰~\cite{Mamat2023}。

    令 $a_{i,k}^t$ 表示节点 $i$ 在时隙 $t$ 生成的、目的地为汇聚节点 $k$ 的数据量。令 $p_{\text{sense}}$ 表示节点感知单位数据所消耗的能量，令 $s_i^t$ 表示节点 $i$ 在时隙 $t$ 用于数据感知的能量消耗。因此，数据感知的总能耗由下式给出：
	\begin{equation}
		\label{eq_s}
		\begin{aligned}
			s_i^t = \sum_{k \in \mathcal{K}}p_\text{sense}\cdot a_{i,k}^t,\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	

    令 $\mathbb{S}^t_D$ 为时隙 $t$ 的活动链路集合，$p_{l}^t$ 为时隙 $t$ 分配给链路 $l$ 的传输功率。节点数据通信的最大和最小传输功率分别用 $p_{\text{max}}$ 和 $p_{\text{min}}$ 表示。每个活动链路的传输功率须满足以下约束：
	\begin{equation}
		\label{eq_p_control}
		\begin{aligned}
			p_\text{min}\leq p_{l}^t\leq p_\text{max}, {\quad}{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	

我们假设所有数据链路遵循莱斯衰落信道模型~\cite{Gao2015}。对于链路 $l(i,j)$，其信道功率增益 $g_{i,j}^t$（或 $g_{l}^t$）的概率密度函数由式~\eqref{eq_g_ij_rician} 给出，其中 $\overline{g}_{i,j}^t$ 表示 $g_{i,j}^t$ 的期望值，$K_{i,j}$ 是莱斯 K 因子，定义为主导视距分量的功率与散射分量总功率之比。变量 $x$ 是从均值为 1 的指数分布中抽取的随机变量，$I_0(\cdot)$ 表示零阶修正贝塞尔函数~\cite{Gao2015}。
    \begin{equation} 
	\label{eq_g_ij_rician}
 \begin{aligned}
	   f_{g_{i,j}^t}(x) = &\frac{K_{i,j}+1}{\overline{g}_{i,j}^t} e^{-x(K_{i,j}+1)/\overline{g}_{i,j}^t - K_{i,j}}\\
    &{\cdot}I_0 \left( \sqrt{\frac{4K_{i,j}(K_{i,j}+1)x}{\overline{g}_{i,j}^t}} \right), \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{aligned}
    \end{equation} 


我们假设，对于链路 $l(i,j)$，期望信道功率增益 $\overline{g}_{i,j}^t$ 遵循调整后的 Friis 自由空间方程（见式~\eqref{eq_g_ij}）。其中，$d_{i,j}$ 为节点 $i$ 和 $j$ 间的距离，$G_i$ 和 $G_j$ 分别为发射与接收天线增益，$L_p$ 为极化损耗，$\lambda$ 为信号波长，$\beta$ 为针对短距离传输场景引入的调整因子~\cite{He2013}。
    \begin{equation} 
	\label{eq_g_ij}
	   \overline g_{i,j}^t = \frac{G_i G_j}{L_p} \left( \frac{\lambda}{4 \pi (d_{i,j} + \beta)} \right)^2,\quad \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{equation} 


	令 $\sigma_{l}^t$ 表示相应的噪声功率。链路 $l$ 上接收信号的信噪比 (SNR) 则由式~\eqref{eq_SNR} 给出。为确保链路 $l$ 上的接收信号能够被正确解码，相应的 SNR 不得低于指定的阈值 $\gamma_{\text{min}}$，如式~\eqref{eq_SNR_control} 所示~\cite{Patrik2004}。
    \begin{align}		
		\mathrm{SNR}_{l}^t=&\frac{p_{l}^tg_{l}^t}{\sigma_{l}^t},&{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR}\\
        \mathrm{SNR}_{l}^t \geq& \gamma_{\min}, &{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR_control} 
	\end{align}	


	令 $r_{l}^t$ 表示时隙 $t$ 内链路 $l$ 上的数据传输速率，可表示为式~\eqref{eq_r}，其中 $W$ 表示链路的信道带宽。我们假设所有链路具有相同的带宽 $W$。由于每个时隙持续一个单位时间长度，$r_l^t$ 实际上表示时隙 $t$ 中链路 $l$ 的容量。
	\begin{equation}
		\label{eq_r}	
		\begin{aligned}
			r_{l}^t=W\log_2\left(1+	\mathrm{SNR}_{l}^t\right),{\quad}{\forall}l{\in}\mathbb{S}^t_D,t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	 
	
为支持数据路由，假设每个节点均配备容量为 $q_{\text{max}}$ 的数据缓冲区，该缓冲区为每个汇聚节点维护一个独立的数据队列。令 $q_{i,k}^t$ 表示节点 $i$ 处发往汇聚节点 $k$ 的数据队列长度。类似地，$f_{l,k}^t$ 表示在时隙 $t$ 内，通过链路 $l$ 传输且目的地为汇聚节点 $k$ 的数据量。数据传输受链路容量及节点缓冲区大小的限制，由式~\eqref{eq_f_r}至式~\eqref{eq_f_q_j}的约束方程给出，其中 $b_j^t$ 为节点 $j$ 在时隙 $t$ 的电池能量，$p_{\text{rcv}}$ 为节点接收单位数据所消耗的能量。
    \begin{align}
f_{l,k}^t{\leq}&r_{l}^t,{\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_r}\\
f_{l,k}^t{\leq}&q_{i,k}^t, {\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_q_i}\\
f_{l,k}^t{\leq}&\frac{b_{j}^t}{p_\text{rcv}},& \forall  l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_b}\\
f_{l,k}^t{\leq}&q_\text{max}{-}\sum_{k{\in}\mathcal{K}}q_{j,k}^t & \forall l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.\label{eq_f_q_j}
    \end{align}
    
	
    在上述约束下，式~\eqref{eq_SNR_control} 中的参数 $\gamma_{\text{min}}$ 确保了数据传输的最低链路质量。链路 $l$ 在时隙 $t$ 的传输功率 $p_l^t$ 设定为满足此 SNR 阈值所需的最小功率，同时须遵守节点的最小和最大功率限制。该功率 $p_l^t$ 按下式计算：
	\begin{equation}
		\label{eq_p_f}
			p_l^t = \max\left\{p_{\min},\frac{\sigma_{l}^t \cdot \gamma_{\min}}{g_l^t}\right\}, \quad \forall l \in \mathbb{S}^t_D, t \in \mathcal{T}.
	\end{equation}
    此外，计算出的 $p_l^t$ 必须满足 $p_l^t \le p_{\max}$ 的约束 (见式~\eqref{eq_p_control})；若此条件不满足（例如由于信道条件极差导致所需功率过高），则链路 $l$ 在当前时隙 $t$ 不可用于数据传输。此功率决定了链路的数据传输速率 $r_l^t$ (式~\eqref{eq_r})，进而影响实际数据流量 $f_{l,k}^t$ (式~\eqref{eq_f_r})。

    节点 $i$ 在时隙 $t$ 用于数据发送和数据接收所消耗的能量（分别用 $\hat{p}_i^t$ 和 $\check{p}_i^t$ 表示）由式~\eqref{eq_p_out} 和式~\eqref{eq_p_in} 分别给出。
	\begin{align}
	\hat p_i^t =&\sum_{l\in\mathcal{O}_i}p_l^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_p_out}\\
    \check p_i^t = &\sum_{l\in\mathcal{I}_i}\sum_{k{\in}\mathcal{K}}p_\text{rcv}{\cdot}f_{l,k}^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_p_in}
	\end{align}

	节点 $i$ 处发往汇聚节点 $k$ 的数据队列在时隙 $t$ 的更新公式如下：
	\begin{equation}
		\label{eq_q}
		\begin{aligned}
			q_{i,k}^{t{+}1}=q_{i,k}^{t}+a_{i,k}^{t}+\sum_{l\in \mathcal{I}_i}f_{l,k}^t-\sum_{l\in \mathcal{O}_i}f_{l,k}^t,\\{\forall}i{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}

    
	\subsection{能量收集模型}
	  各节点均配备容量为 $b_{\max}$ 的可充电电池用于能量存储。假设节点通过专用设备从环境来源（如太阳能）收集能量进行供电。本文采用~\cite{Ku2015} 中描述的隐马尔可夫链框架对节点从环境收集能量的过程进行建模。该模型将节点的能量收集条件划分为四个状态：\textit{极好}、\textit{良好}、\textit{一般}和\textit{差}，分别索引为1至4。对于各状态 $c{\in}\{1,2,3,4\}$，节点 $i$ 在时隙 $t$ 收集的能量（表示为 $h_i^t$）遵循均值为 $\mu_c$、方差为 $\rho_c$ 的高斯分布。此外，从状态 $c$ 转换到状态 $c'$ 的概率表示为 $P_{cc'}$。这些参数源自实际的太阳辐照度测量值~\cite{Ku2015}。

    除了从环境来源收集能量外，节点还可以从邻近节点的无线电信号中收集射频 (RF) 能量以补充其能量储备。这些无线电信号包括：(1) 用于无线能量传输 (WPT) 以实现能量协作的专用能量信号；(2) 发往特定目的地的单播数据信号，由于无线信号的广播特性，邻近节点亦可从中收集能量。我们假设节点的能量协作邻居也是其通信邻居。为防止能量信号对数据信号产生干扰，假设能量协作在不同于数据传输的频率信道上进行，同时能量天线能够从能量传输和数据传输两类信号中收集能量。
	令 $\mathbb{S}_E^t$ 为在时隙 $t$ 参与能量传输的节点集合，$e_{\max}$ 和 $e_{\min}$ 分别为节点的最大和最小能量传输功率。节点 $i$ 在时隙 $t$ 用于能量传输的能量量以 $\hat{e}_i^t$ 表示，其约束如式~\eqref{eq_epower_bounds_first}所示。
	\begin{equation}
		\label{eq_epower_bounds_first}
		e_\text{min}\leq \hat{e}_i^t\leq e_\text{max}, \quad {\forall} i{\in} \mathbb{S}_E^t, t{\in}\mathcal{T}.
	\end{equation}
	
	令 $\hat{p}_j^t$ 和 $\hat{e}_j^t$ 分别表示节点 $j$ 在时隙 $t$ 的数据传输功率和能量传输功率。节点 $i$ 在时隙 $t$ 接收到的总 RF 功率由来自邻居节点的数据传输信号功率和能量传输信号功率组成。令 $Rf_{i,\text{data}}^t = \sum_{j{\in}\mathcal{N}_i} \hat{p}_j^t g_{j,i}^t$ 表示来自数据信号的总功率，令 $Rf_{i,\text{energy}}^t = \sum_{j{\in}\mathcal{N}_i} \hat{e}_j^t g_{j,i}^t$ 表示来自能量信号的总功率。如果节点 $i$ 自身在传输能量 ($i \in \mathbb{S}_E^t$)，则它不能接收 RF 能量。因此，节点 $i$ 接收到的用于能量收集的 RF 功率分量为：
	\begin{equation}
		\label{eq_Rf_components}
		(Rf_{i,\text{data}}^t, Rf_{i,\text{energy}}^t) = 
		\left\{
		\begin{aligned}
			&(\sum_{j{\in}\mathcal{N}_i} \hat{p}_j^t g_{j,i}^t, \sum_{j{\in}\mathcal{N}_i} \hat{e}_j^t g_{j,i}^t), && \text{if } i{\notin}\mathbb{S}^t_E, \\
			&(0, 0), && \text{if } i{\in}\mathbb{S}^t_E.
		\end{aligned}
		\right.
	\end{equation}
	我们假设从数据信号中收集能量的效率可能低于从专用能量信号中收集能量的效率。引入一个效率因子 $\eta_{\text{data}} \in [0, 1]$ 来表示从数据信号功率转换为可用于收集的有效 RF 功率的效率（仿真中设为 $\eta_{\text{data}}=0.1$）。因此，节点 $i$ 用于 RF 能量收集的有效总输入功率 $Rf_i^{\text{eff},t}$ 可以表示为：
	\begin{equation}
	    \label{eq_Rf_eff}
	    Rf_i^{\text{eff},t} = \eta_{\text{data}} Rf_{i,\text{data}}^t + Rf_{i,\text{energy}}^t
	\end{equation}

        由于能量收集过程存在损耗，节点 $i$ 实际收集的能量小于其接收到的有效 RF 信号功率 $Rf_i^{\text{eff},t}$。令 $\check{e}_i^t$ 表示节点 $i$ 从 $Rf_i^{\text{eff},t}$ 收集的能量，其计算如式~\eqref{eq_EH}所示。式中，$\Omega_{i}$ 和 $\Psi_{i}^{t}$ 分别由式~\eqref{eq_omega} 和式~\eqref{eq_psi} 定义；$\exp(\cdot)$ 为自然指数函数；常数 $e_\text{mp}$ 代表节点单时隙从 RF 信号收集的最大能量（即 RF 信号最大能量收集功率）；$\mu_{i}$ 和 $\nu_{i}$ 为相关硬件组件决定的固定参数~\cite{Boshkovska2015}。
\begin{subequations}
\label{eq_EH1}
\begin{align}
    \check{e}_{i}^{t}=&\frac{\left[\Psi_{i}^{t}-e_\text{mp}\Omega_{i}\right]}{1-\Omega_{i}},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_EH}\\
	\Omega_{i}=& \frac{1}{1+\exp(\mu_{i}\nu_{i})},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_omega}\\
	\Psi_{i}^{t}=&\frac{e_\text{mp}}{1+\exp\left(-\mu_{i}(Rf_{i}^{\text{eff},t}-\nu_{i})\right)},& \forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_psi} 
\end{align}
\end{subequations}
	考虑节点的电池容量 $b_\text{max}$，节点 $i$ 在时隙 $t$ 实际获得的能量（表示为 $\bar{e}_i^t$）须满足以下约束。
	\begin{align}
		\bar{e}_i^t{\leq}&\check{e}_i^t{+}h_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T},\label{eq_e_eh}\\
		\bar{e}_i^t{\leq}&b_{\text{max}}{-}b_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.\label{eq_e_b}
	\end{align}  
	
    考虑到能量使用的因果关系，我们假设在一个时隙收集的能量只能在后续时隙中使用。因此，节点在一个时隙内的能量消耗行为必须满足式~\eqref{eq_energy_control}。
	\begin{equation}
		\label{eq_energy_control}
		s_{i}^t+\hat{p}_i^t+\check{p}_i^t+\hat{e}_{i}^t\leq b_i^t, \quad {\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
    总之，节点 $i$ 的电池能量演化公式由下式给出：
	\begin{equation}
		\label{eq_b}
		\begin{aligned}
			b_i^{t+1}=b_i^t+\bar e_i^t-s_i^t-\hat p_i^t-\check p_i^t-\hat{e}_{i}^t\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	
	
\subsection{数据通信与能量协作的调度模型}
    在能量协作辅助的数据收集范式中，能量协作与数据传输行为须在各时隙内进行仔细调度。各时隙的能量协作调度决定参与能量交互的节点及相应的功率分配，数据通信调度则确定激活链路、传输数据及传输功率。链路的激活隐含地决定了相应的发送方和接收方。能量协作调度负责将节点分配到时隙进行能量传输/接收，数据通信调度则负责将链路分配到时隙进行数据传输。在~\cite{Patrik2004} 中，这些分别被称为基于节点的分配和基于链路的分配。鉴于数据收集主要依赖数据通信调度，通过链路将数据传输至汇聚节点，因此术语\textit{数据收集调度}与\textit{数据通信调度}可互换使用。能量协作调度和数据通信调度的组合称为能量协作辅助数据收集 (ECaDC) 联合调度。

   为描述 ECaDC 调度，定义如下决策变量：令 $x_{l,k}^t{\in}\{0,1\}$ 为一二进制变量，当其值为1时表示链路 $l$ 在时隙 $t$ 被激活以传输目标为汇聚节点 $k$ 的数据包，为0则表示未激活。令 $y_i^t{\in}\{0,1\}$ 为一二进制变量，表示节点 $i$ 在时隙 $t$ 的能量交互状态：$y_i^t{=}1$ 表示节点 $i$ 传输能量，$y_i^t{=}0$ 表示其接收能量。令 $\textbf{x}{:=}[x_{l,k}^t| l{\in} \mathcal{E}, k {\in}\mathcal{K}, t {\in}\mathcal{T}]$、$\textbf{y}{:=}[y_i^t| i {\in} \mathcal{N}, t{\in}\mathcal{T}]$、$\textbf{p} {:=} [p_l^t | l {\in} \mathcal{E}, t {\in} \mathcal{T}]$ 及 $\textbf{e}{:=} [\hat{e}_i^t|i {\in} \mathcal{N}, t {\in} \mathcal{T}]$。向量 $\textbf{x}$、$\textbf{y}$、$\textbf{p}$、$\textbf{e}$ 共同构成 ECaDC 调度，记为 ECaDC($\textbf{x,y,p,e}$)。

    假设节点在单一时隙内仅能传输一种类型的数据包。进一步考虑数据通信的半双工模式，链路的激活状态须满足以下约束。
	\begin{equation}
		\label{eq_x}
		\sum_{k\in \mathcal{K}}\sum_{l\in \mathcal{I}_i}x_{l,k}^t+\sum_{k\in \mathcal{K}}\sum_{l\in\mathcal{O}_i}x_{l,k}^t\leq1,  \quad{\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
	使用 $\textbf{x}$ 中的决策变量，式~\eqref{eq_p_control} 中对 $p_l^t$ 的约束可以重新表示为式~\eqref{eq_p_x}。
	\begin{equation}
		\label{eq_p_x}
		\begin{aligned}
			\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{min}\leq p_{l}^t\leq\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{max},\quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	式~\eqref{eq_SNR_control} 与式~\eqref{eq_SNR} 中的 SNR 约束可合并为式~\eqref{eq_SNR_x_control}，其中 $\phi_1$ 为一足够大的常数 ($\phi_1 {\gg} \gamma_\text{min}$)，确保在链路未激活时该约束自动满足。
	\begin{equation} 
		\label{eq_SNR_x_control}
		p_{l}^tg_{l}^t+\phi_1(1-\sum_{k\in \mathcal{K}}x_{lk}^t)\geq\gamma_\text{min}\sigma_{l}^t, \quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
	\end{equation}	
	 
	式~\eqref{eq_epower_bounds_first} 中用于控制 \(\hat{e}_i^t\) 的约束可以重新表示为：
	\begin{equation}
		\label{eq_y}
		y_{i}^t{\cdot}e_\text{min}\leq \hat{e}_i^t\leq y_{i}^t{\cdot}e_\text{max}, \quad \forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.
	\end{equation}
	
	\section{长期平均吞吐量最大化问题建模}
	\label{sec_problem_def}
	
	在本文中，我们采用 Lyapunov 优化框架来解决多播能量协作和数据收集的联合调度问题。主要目标为最大化系统的长期平均 (LTA) 吞吐量，同时确保数据队列的稳定性并将各节点的LTA电池能量水平维持于预设阈值之上。
	
	LTA 系统吞吐量，表示为 $\bar{\mathcal{D}}$，定义为数据包成功传递到各自汇聚节点的时间平均速率：
	\begin{equation}
		\label{eq_lta_throughput}
		\bar{\mathcal{D}} = \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E\left[ \sum_{k \in \mathcal{K}} \sum_{l=(i,k) \in \mathcal{I}_k} f_{l,k}^t \right]
	\end{equation}
    其中 $\mathcal{I}_k$ 为至汇聚节点 $k$ 的入向链路集合，$f_{l,k}^t$ 为时隙 $t$ 内经由链路 $l$ 传输且目的地为汇聚节点 $k$ 的数据量。期望 $E[\cdot]$ 针对随机信道状态和能量收集过程计算。

    我们的目标是在以下约束条件下最大化 $\bar{\mathcal{D}}$：
    \begin{itemize}
        \item \textbf{数据队列稳定性：} 所有数据队列 $q_{i,k}(t)$ 均须稳定，即其长期平均大小有界，这隐含确保所有生成数据最终均被成功传递。
        \item \textbf{LTA 电池能量约束：} 每个节点 $i$ 的 LTA 电池能量水平必须维持在预定义的阈值 $\delta_i$ 之上：
        \begin{equation}
            \label{eq_lta_energy}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge \delta_i, \quad \forall i \in \mathcal{N}
        \end{equation}
        \item \textbf{瞬时约束：} 第~\ref{sec_model} 节中定义的所有系统模型约束均须在各时隙 $t$ 得到满足。此类约束包括能量因果关系 (式~\eqref{eq_energy_control})、电池容量 (式~\eqref{eq_e_b})、功率限制 (式~\eqref{eq_p_x}, \eqref{eq_y})、半双工操作 (式~\eqref{eq_x})、数据流约束 (式~\eqref{eq_f_r}-\eqref{eq_f_q_j}) 及SNR要求 (式~\eqref{eq_SNR_x_control}) 等。
    \end{itemize}

    形式上，优化问题可以表述为：
	\begin{equation}
		\label{eq_P1_prime}
		\begin{array}{rrl}
			\textbf{(\textbf{P1'})} &\max\limits_{\textbf{x,y,p,e}} & \bar{\mathcal{D}} \\
			& \text{s.t.}& \text{所有 } q_{i,k}(t) \text{ 的数据队列稳定性} \\
			         && \text{所有 } i \in \mathcal{N} \text{ 的 LTA 电池能量约束 (式~\eqref{eq_lta_energy})} \\
			&& \text{所有 } t \text{ 的瞬时约束 (式}.~\eqref{eq_s}-\eqref{eq_y})
		\end{array}
	\end{equation}
    
    问题 P1' 本质上是一个随机网络优化问题，其目标为最大化长期平均效用并满足相应的长期约束，因此适宜采用 Lyapunov 优化理论进行求解。尽管 P1' 关注长期平均性能，其核心调度决策的复杂性依然存在。引理~\ref{proposition_1} 将证明，与 P1' 相关的底层调度子问题是 NP-Hard 的。
    
    直接求解问题 P1' 极具挑战性，这主要源于其长期平均 (LTA) 目标与约束的特性，以及各时隙决策之间存在的复杂耦合与随机性。为克服此困难，本文将运用 Lyapunov 优化技术，将原问题 P1' 转化为一系列可在每个时隙独立求解的确定性优化问题。

	\begin{lemma}
		\label{proposition_1}
		与问题 P1' 相关的底层调度决策是 NP-Hard 的。
	\end{lemma}
	
	\begin{proof}
    为证明与问题 P1' 相关的底层调度决策的 NP-Hard 特性，我们将已知的 NP-Complete 问题——灵活作业车间调度 (Flexible Job Shop Scheduling, FJSP) 问题~\cite{SDPJDLSKT2024}——归约至一个特定的调度子问题实例。该子问题，我们称之为数据收集调度 (Data Collection Scheduling, DCS) 问题，是在一系列简化假设下定义的：所有节点初始能量充足故无需能量协作，不存在数据缓冲队列，且数据传输采用固定的功率。
		
		\textbf{DCS 问题：} 给定一个网络图 $\mathcal{G}(\mathcal{V},\mathcal{E})$ 和一个数据包集合 $\mathcal{P}$。每个数据包 $p \in \mathcal{P}$ 在源节点 $s_p \in \mathcal{V}$ 生成，并须通过预定义路由集合 $\mathcal{R}_p$ 中的某一条路径传输至其目的节点 $d_p \in \mathcal{V}$。数据包 $p$ 在中间节点 $v \in \mathcal{V}$ 的传输（或处理）时间记为 $\tau_p^v$。DCS 问题的目标是确定一个链路调度方案，以最小化所有数据包到达其各自目的地的最大完成时间 (makespan)。
		
		\textbf{FJSP 问题：} 依据~\cite{SDPJDLSKT2024}，FJSP 问题定义如下：给定一组作业 $\mathcal{J}=\{J_1, \dots, J_m\}$ 和一组机器 $\mathcal{M}=\{M_1, \dots, M_w\}$。每个作业 $J_j \in \mathcal{J}$ 包含一个由 $n_j$ 个操作组成的序列 $(O_{j1}, O_{j2}, \dots, O_{jn_j})$，这些操作必须按指定顺序执行。对于每个操作 $O_{ji}$，存在一个可选机器集合 $\mathcal{M}_{ji} \subseteq \mathcal{M}$，操作 $O_{ji}$ 可在其中任意一台机器上执行。操作 $O_{ji}$ 在机器 $M_k \in \mathcal{M}_{ji}$ 上的处理时间为 $p_{jik}$。FJSP 的目标是为每个操作选择一台执行机器，并确定所有操作的开始时间，以最小化所有作业的最大完成时间 ($C_{\max}$)，同时满足以下约束：(1) 每个操作一旦开始，必须不间断地执行完毕；(2) 每台机器在任一时刻最多只能处理一个操作；(3) 作业内部的操作顺序必须得到满足。
		
		\textbf{从 FJSP 实例到 DCS 实例的归约：} 考虑任意一个 FJSP 实例，我们构造一个对应的 DCS 实例，具体映射规则如下：
		\begin{itemize}
			\item \textbf{节点映射 ($\mathcal{M} \cup \mathcal{J} \rightarrow \mathcal{V}$):} 
			对于 FJSP 中的每台机器 $M_k \in \mathcal{M}$，在 DCS 中创建一个对应的机器节点 $v_{Mk}$。对于 FJSP 中的每个作业 $J_j \in \mathcal{J}$，在 DCS 中创建两个唯一的节点：源节点 $v^s_{Jj}$ 和目的节点 $v^d_{Jj}$。因此，DCS 实例的节点集合为 $\mathcal{V} = \{v_{Mk} | M_k \in \mathcal{M}\} \cup \{v^s_{Jj}, v^d_{Jj} | J_j \in \mathcal{J}\}$。
			
			\item \textbf{数据包映射 ($\mathcal{J} \rightarrow \mathcal{P}$):} 
			对于 FJSP 中的每个作业 $J_j \in \mathcal{J}$，在 DCS 中创建一个对应的数据包 $P_j \in \mathcal{P}$。数据包 $P_j$ 的源节点为 $s_{P_j} = v^s_{Jj}$，目的节点为 $d_{P_j} = v^d_{Jj}$。
			
			\item \textbf{路由与操作映射 ($\mathcal{O}_{ji}, \mathcal{M}_{ji} \rightarrow \mathcal{R}_{P_j}$):} 
			对于作业 $J_j$ 的操作序列 $(O_{j1}, O_{j2}, \dots, O_{jn_j})$，数据包 $P_j$ 的一条可能路由 $R \in \mathcal{R}_{P_j}$ 构造如下：该路由从 $v^s_{Jj}$ 开始，依次通过 $n_j$ 个机器节点，最后到达 $v^d_{Jj}$。具体地，如果作业 $J_j$ 的第 $l$-个操作 $O_{jl}$ 被分配到机器 $M_k \in \mathcal{M}_{jl}$ 上执行，则数据包 $P_j$ 的路由中，对应于第 $l$ 个处理步骤的中间节点即为 $v_{Mk}$。因此，一条完整的路由形如 $(v^s_{Jj}, v_{M_{k_1}}, v_{M_{k_2}}, \dots, v_{M_{k_{n_j}}}, v^d_{Jj})$，其中 $M_{k_l}$ 是操作 $O_{jl}$ 被分配执行的机器。
			
			\item \textbf{处理时间映射 ($p_{jik} \rightarrow \tau_{P_j}^{v_{Mk}}$):} 
			FJSP 中操作 $O_{ji}$ 在机器 $M_k \in \mathcal{M}_{ji}$ 上的处理时间 $p_{jik}$ 映射为 DCS 中数据包 $P_j$ 在对应机器节点 $v_{Mk}$ 上的传输时间 $\tau_{P_j}^{v_{Mk}} = p_{jik}$。
		\end{itemize}
		
		上述归约过程显然可以在多项式时间内完成。可以证明，FJSP 实例存在一个最大完成时间不超过 $T_{makespan}$ 的可行调度，当且仅当构造出的 DCS 实例存在一个最大完成时间不超过 $T_{makespan}$ 的可行调度。由于 FJSP 是 NP-Complete 的，这意味着所定义的 DCS 问题是 NP-Hard 的。鉴于 DCS 问题是与问题 P1' 相关的底层调度决策在特定简化条件下的一个特例，因此，问题 P1' 所涉及的联合能量协作与数据收集调度决策同样是 NP-Hard 的。
	\end{proof}
	
	\section{Lyapunov 优化框架}
	   \label{sec_lyapunov}
	   为求解式~\eqref{eq_P1_prime} 所定义的随机网络优化问题 P1'，本文采用 Lyapunov 优化理论~\cite{neely2010stochastic}。该理论框架的核心思想是将具有长期平均 (LTA) 目标和约束的随机优化问题，转化为一系列可在每个时隙独立求解的确定性优化问题。

    \subsection{虚拟队列}
    Lyapunov 优化框架的核心机制之一是利用虚拟队列 (virtual queues) 来处理长期平均 (LTA) 约束。本文中，除了用于跟踪数据积压的实际数据队列 $q_{i,k}(t)$ (定义见式~\eqref{eq_q}) 外，我们还为每个传感器节点 $i \in \mathcal{N}$ 引入一个虚拟能量赤字队列 (virtual energy deficit queue) $B_i(t)$，以应对 LTA 电池能量约束 (式~\eqref{eq_lta_energy})。该虚拟队列 $B_i(t)$ 用于衡量节点 $i$ 的瞬时电池能量水平 $b_i(t)$ 相对于预设阈值 $\delta_i$ 的累积能量赤字。其更新规则定义如下：
    \begin{equation}
        \label{eq_virtual_energy_queue}
        B_i(t+1) = \max\{ B_i(t) + \delta_i - b_i(t+1), 0 \}
    \end{equation}
    其中，$b_i(t+1)$ 表示在时隙 $t$ 的决策执行完毕后、时隙 $t+1$ 开始时的实际电池能量，其计算遵循式~\eqref{eq_b}。从直观上看，若 $b_i(t+1)$ 低于预设阈值 $\delta_i$，则能量赤字队列 $B_i(t)$ 将趋于增长，这反映了系统状态偏离了预期的 LTA 能量维持目标。根据 Lyapunov 优化理论，虚拟队列 $B_i(t)$ 的稳定性（即其长期平均值有界）是确保 LTA 约束式~\eqref{eq_lta_energy} 得以满足的关键条件~\cite{neely2010stochastic}。

    令 $\mathbf{\Theta}(t) = (\mathbf{q}(t), \mathbf{B}(t))$ 代表在时隙 $t$ 开始时，网络中所有实际数据队列 $\mathbf{q}(t) = [q_{i,k}(t)]_{\forall i,k}$ 与所有虚拟能量赤字队列 $\mathbf{B}(t) = [B_i(t)]_{\forall i}$ 的组合状态向量。

    \subsection{Lyapunov 函数与漂移}
    为评估网络整体的队列积压程度，我们定义如下的二次 Lyapunov 函数 $L(\mathbf{\Theta}(t))$：
    \begin{equation}
        \label{eq_lyapunov_function}
        L(\mathbf{\Theta}(t)) = \frac{1}{2} \sum_{i \in \mathcal{N}} \sum_{k \in \mathcal{K}} q_{i,k}(t)^2 + \frac{1}{2} \sum_{i \in \mathcal{N}} B_i(t)^2
    \end{equation}
    值得注意的是，可以为不同的数据队列或能量赤字队列引入非均匀的权重因子以实现差异化的服务质量或优先级控制，但为简化表述，本文在此采用统一权重。

    单时隙条件 Lyapunov 漂移 (one-slot conditional Lyapunov drift)，记为 $\Delta L(\mathbf{\Theta}(t))$，定义为在给定当前系统状态 $\mathbf{\Theta}(t)$ 的条件下，Lyapunov 函数在一个时隙内的期望变化量：
    \begin{equation}
        \label{eq_lyapunov_drift}
        \Delta L(\mathbf{\Theta}(t)) = E[ L(\mathbf{\Theta}(t+1)) - L(\mathbf{\Theta}(t)) | \mathbf{\Theta}(t) ]
    \end{equation}
    Lyapunov 优化的一个核心目标是通过控制决策来最小化此漂移，从而将系统推向队列积压较低的状态，进而实现队列的稳定性。

    \subsection{漂移加惩罚与问题转换}
    为同时优化 LTA 吞吐量 (式~\eqref{eq_lta_throughput}) 并通过队列稳定性间接满足 LTA 约束，本文采用漂移加惩罚 (drift-plus-penalty) 方法~\cite{neely2010stochastic}。此方法旨在每个时隙最小化一个包含 Lyapunov 漂移与负加权期望吞吐量的组合目标，从而将原 LTA 优化问题 P1' 分解为一系列逐时隙优化子问题。

    具体而言，漂移加惩罚方法致力于在每个时隙 $t$ 最小化如下定义的表达式 $Y(t)$：
    \begin{equation}
        \label{eq_drift_plus_penalty_func}
        Y(t) := \Delta L(\mathbf{\Theta}(t)) - V \cdot E[\text{DeliveredData}(t) | \mathbf{\Theta}(t)]
    \end{equation}
    其中，$\text{DeliveredData}(t) = \sum_{k \in \mathcal{K}} \sum_{l=(i,k) \in \mathcal{I}_k} f_{l,k}^t$ 表示在时隙 $t$ 内成功传输至所有汇聚节点的总数据量。参数 $V \ge 0$ 是一个重要的非负控制因子，它用于调节在最小化 Lyapunov 漂移（即追求队列稳定性）与最大化期望吞吐量 $E[\text{DeliveredData}(t)]$ 之间的权衡。选择较大的 $V$ 值意味着更侧重于提升吞吐量。通过最小化 $Y(t)$，控制策略力图将系统导向队列积压较低的状态，同时追求目标效用（吞吐量）的最大化。

    通过在每个时隙最小化 $Y(t)$，原问题 P1' 中的数据队列稳定性约束和 LTA 电池能量约束（通过虚拟能量赤字队列 $B_i(t)$ 的稳定性来体现）能够得到有效处理。因此，原问题 P1' 可以近似转化为在每个时隙 $t$ 求解以下优化问题 P2：
    \begin{equation}
        \label{eq_P2}
        \begin{array}{rrl}
            \textbf{(P2)} &\min\limits_{\textbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t} & Y(t) \\
            & \text{s.t.}& \text{时隙 } t \text{ 的瞬时约束 (式}.~\eqref{eq_s}-\eqref{eq_y})
        \end{array}
    \end{equation}
    然而，问题 P2 的目标函数 $Y(t)$ 中包含的漂移项 $\Delta L(\mathbf{\Theta}(t))$ 依赖于下一时隙的系统状态 $\mathbf{\Theta}(t+1)$（通过 $L(\mathbf{\Theta}(t+1))$ 体现），这使得直接求解 P2 以进行在线决策变得困难。为了克服这一挑战，后续将推导 $Y(t)$ 的一个上界，该上界仅依赖于当前时隙的变量和决策。

    首先，我们推导单时隙条件 Lyapunov 漂移 $\Delta L(\mathbf{\Theta}(t))$ 的一个上界。根据 Lyapunov 函数的定义 (式~\eqref{eq_lyapunov_function})，该漂移可以自然地分解为源于实际数据队列的漂移分量 $\Delta L_Q(t)$ 和源于虚拟能量赤字队列的漂移分量 $\Delta L_B(t)$：
    \begin{equation}
        \Delta L(\mathbf{\Theta}(t)) = \Delta L_Q(t) + \Delta L_B(t)
    \end{equation}
    \begin{align}
        \Delta L_Q(t) &= E\left[\frac{1}{2} \sum_{i,k} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) \bigg| \mathbf{\Theta}(t) \right] \\
        \Delta L_B(t) &= E\left[\frac{1}{2} \sum_i (B_i(t+1)^2 - B_i(t)^2) \bigg| \mathbf{\Theta}(t) \right]
    \end{align}

    \subsubsection{数据队列漂移上界 \texorpdfstring{$\Delta L_Q(t)$}{Delta LQ(t)}}
    考虑单个数据队列 $q_{i,k}(t)$ 的漂移。其队列动态遵循式~\eqref{eq_q}。为应用后续的Lyapunov分析工具，并考虑队列长度的非负性，可将其等效动态表示为 $q_{i,k}(t+1) = \max\{0, q_{i,k}(t) - \text{Outflow}_{i,k}^t \} + A_{i,k}(t)$，其中 $A_{i,k}(t) = a_{i,k}^t + \sum_{l'=(j,i) \in \mathcal{I}_i} f_{l',k}^t$ 代表在时隙 $t$ 内到达节点 $i$ 且目标为汇聚节点 $k$ 的总数据量（包括新生成和从其他节点中继传入的数据），而 $\text{Outflow}_{i,k}^t = \sum_{l=(i,j) \in \mathcal{O}_i} f_{l,k}^t$ 代表从节点 $i$ 发出且目标为汇聚节点 $k$ 的总数据量。
    根据 Lyapunov 优化理论中的一个标准结果（参见~\cite[引理~4.2]{neely2010stochastic}），对于任意非负实数 $q, A, D$，以下不等式成立：
    \begin{equation*}
        \frac{1}{2} [(\max\{0, q-D\} + A)^2 - q^2] \le \frac{1}{2} (A^2 + D^2) + q(A-D)
    \end{equation*}
    将此不等式应用于数据队列 $q_{i,k}(t)$，令 $q=q_{i,k}(t)$，$A=A_{i,k}(t)$，$D=\text{Outflow}_{i,k}^t$，可得：
    \begin{align}
        \frac{1}{2} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) &\le \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \nonumber \\
        &\quad + q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t)
    \end{align}
    对所有数据队列 $(i,k)$ 求和，并对当前状态 $\mathbf{\Theta}(t)$ 取条件期望，得到 $\Delta L_Q(t)$ 的一个上界：
    \begin{align}
        \Delta L_Q(t) &= E\left[\frac{1}{2} \sum_{i,k} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
        &\le E\left[ \sum_{i,k} \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
        &\quad + E\left[ \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \bigg| \mathbf{\Theta}(t) \right] \label{eq_delta_lq_step1}
    \end{align}
    假设在给定当前状态 $\mathbf{\Theta}(t)$ 的条件下，总流入量 $A_{i,k}(t)$ 和总流出量 $\text{Outflow}_{i,k}^t$ 的条件二阶矩一致有界，即存在有限常数 $C_{A^2}$ 和 $C_{D^2}$，使得对于所有的 $i,k,t$，$E[A_{i,k}(t)^2 | \mathbf{\Theta}(t)] \le C_{A^2}$ 且 $E[(\text{Outflow}_{i,k}^t)^2 | \mathbf{\Theta}(t)] \le C_{D^2}$。这一假设在实际网络中通常是合理的，因为数据到达率、链路容量和节点传输功率均受物理或协议限制。基于此假设，式~\eqref{eq_delta_lq_step1} 右侧的第一项（即期望的平方和项）可以由一个正常数 $C_q$ 界定：
    \begin{multline*}
         E\left[ \sum_{i,k} \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \bigg| \mathbf{\Theta}(t) \right] \\
         \le \sum_{i,k} \frac{1}{2} (C_{A^2} + C_{D^2}) =: C_q
    \end{multline*}
    展开并重新组织 $E\left[ \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \bigg| \mathbf{\Theta}(t) \right]$ 中的求和项，并考虑到汇聚节点 $k'$ 处的队列 $q_{k',k}(t)=0$（因数据已到达目的地），可得：
    \begin{align}
        & \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \nonumber \\
        &= \sum_{i,k} q_{i,k}(t) a_{i,k}^t + \sum_{i,k} q_{i,k}(t) \left( \sum_{l'=(j,i)} f_{l',k}^t - \sum_{l=(i,j)} f_{l,k}^t \right) \nonumber \\
        &= \sum_{i,k} q_{i,k}(t) a_{i,k}^t + \sum_{l=(i,j) \in \mathcal{E}} \sum_{k \in \mathcal{K}} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t
    \end{align}
    因此，数据队列漂移分量 $\Delta L_Q(t)$ 的上界可以表示为：
    \begin{align}
        \Delta L_Q(t) \le C_q &+ \sum_{i,k} q_{i,k}(t) E[a_{i,k}^t | \mathbf{\Theta}(t)] \nonumber \\
        &+ E\left[ \sum_{l=(i,j) \in \mathcal{E}} \sum_{k \in \mathcal{K}} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \bigg| \mathbf{\Theta}(t) \right] \label{eq_delta_lq_bound}
    \end{align}
    \subsubsection{能量赤字队列漂移上界 \texorpdfstring{$\Delta L_B(t)$}{Delta LB(t)}}
    接下来，我们分析虚拟能量赤字队列 $B_i(t)$ 的漂移。其更新规则如式~\eqref{eq_virtual_energy_queue} 所示：$B_i(t+1) = \max\{ B_i(t) + \delta_i - b_i(t+1), 0 \}$。
    为推导其二次项的漂移，我们利用不等式 $(\max\{0, z\})^2 \le z^2$ 对于任意实数 $z$ 均成立的性质（特别地，当 $z < 0$ 时，$\max\{0,z\}=0$，不等式显然成立；当 $z \ge 0$ 时，$\max\{0,z\}=z$，不等式亦成立）。令 $z = B_i(t) + \delta_i - b_i(t+1)$，则有：
    \begin{align}
        B_i(t+1)^2 &= (\max\{0, B_i(t) + \delta_i - b_i(t+1)\})^2 \nonumber \\
                   &\le (B_i(t) + \delta_i - b_i(t+1))^2 \nonumber \\
                   &= B_i(t)^2 + (\delta_i - b_i(t+1))^2 + 2 B_i(t) (\delta_i - b_i(t+1))
    \end{align}
    移项并除以2，可得单个能量赤字队列的二次漂移项满足：
    \begin{equation}
        \label{eq_energy_drift_base}
        \frac{1}{2} (B_i(t+1)^2 - B_i(t)^2) \le \frac{1}{2} (\delta_i - b_i(t+1))^2 + B_i(t) (\delta_i - b_i(t+1))
    \end{equation}
    考虑到实际电池能量 $b_i(t+1)$ 的取值范围为 $[0, b_{\max}]$，且能量阈值 $\delta_i$ 为一常数，因此 $(\delta_i - b_i(t+1))^2$ 必然有界。对所有节点 $i$ 求和并对当前状态 $\mathbf{\Theta}(t)$ 取条件期望，式~\eqref{eq_energy_drift_base} 右侧的第一项可以被一个正常数 $C_b$ 界定：$E\left[\sum_i \frac{1}{2} (\delta_i - b_i(t+1))^2 \bigg| \mathbf{\Theta}(t)\right] \le C_b$。因此，总的能量赤字队列漂移 $\Delta L_B(t)$ 满足：
    \begin{equation}
        \label{eq_delta_lb_step1}
        \Delta L_B(t) \le C_b + E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right]
    \end{equation}
    接下来，我们重点分析式~\eqref{eq_delta_lb_step1} 中的第二项，即 $E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right]$。将电池能量的动态演化关系式~\eqref{eq_b}（即 $b_i(t+1) = b_i(t) + \bar{e}_i^t - \text{Cons}_i^t$）代入，其中 $\text{Cons}_i^t = s_i^t + \hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t$ 表示节点 $i$ 在时隙 $t$ 的总能量消耗，而 $\bar{e}_i^t$ 是节点 $i$ 在时隙 $t$ 实际获得的净能量增益（该增益同时受到环境能量收集 $h_i^t$、RF能量收集 $\check{e}_i^t$ 以及电池容量 $b_{\max}$ 的限制，即 $\bar{e}_i^t = \min(\check{e}_i^t + h_i^t, b_{\max} - (b_i(t) - \text{Cons}_i^t))$）。
    于是，我们有 $\delta_i - b_i(t+1) = \delta_i - (b_i(t) + \bar{e}_i^t - \text{Cons}_i^t) = \delta_i - b_i(t) - \bar{e}_i^t + \text{Cons}_i^t$。
    
    直接在漂移分析中处理 $\bar{e}_i^t = \min(\check{e}_i^t + h_i^t, b_{\max} - (b_i(t) - \text{Cons}_i^t))$ 中的 $\min$ 函数会导致表达式异常复杂。为简化推导并获得一个结构上更易于在线优化的目标函数，我们在此采用一种在Lyapunov优化文献中处理有限容量资源（如电池）时常见的近似方法：**即在推导漂移上界时，暂时忽略电池容量上限 $b_{\max}$ 对能量存储过程的饱和效应**。具体而言，我们假设实际获得的能量增益 $\bar{e}_i^t$ 近似等于总的潜在能量收集量，即 $\bar{e}_i^t \approx \check{e}_i^t + h_i^t$。此近似在电池大部分时间未达到其容量上限（即不经常发生能量溢出）的场景下较为合理，并且它能够保留能量消耗和能量收集对能量赤字队列影响的核心加权关系。
    基于此近似，我们继续推导 $E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right]$：
    $\delta_i - b_i(t+1) \approx \delta_i - b_i(t) - (\check{e}_i^t + h_i^t) + \text{Cons}_i^t$
    由于 $B_i(t) \ge 0$，将其乘以 $(\delta_i - b_i(t+1))$ 并对所有节点求和再取条件期望，得到：
    \begin{align}
        & E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
        &\approx E\left[ \sum_i B_i(t) (\delta_i - b_i(t) - \check{e}_i^t - h_i^t + \text{Cons}_i^t) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
        &= \sum_i B_i(t) (\delta_i - b_i(t)) - E\left[\sum_i B_i(t) \check{e}_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
        &- E\left[\sum_i B_i(t) h_i^t \bigg| \mathbf{\Theta}(t)\right] + E\left[\sum_i B_i(t) \text{Cons}_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
        &= \sum_i B_i(t) (\delta_i - b_i(t)) - E\left[\sum_i B_i(t) \check{e}_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
        &- E\left[\sum_i B_i(t) h_i^t \bigg| \mathbf{\Theta}(t)\right] + E\left[\sum_i B_i(t) s_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
        & + E\left[ \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t) \bigg| \mathbf{\Theta}(t) \right] \label{eq_energy_term_approx}
    \end{align}
    将式~\eqref{eq_energy_term_approx} 的结果代回式~\eqref{eq_delta_lb_step1}，我们便获得了能量赤字队列漂移 $\Delta L_B(t)$ 的一个近似上界：
    \begin{align}
        \Delta L_B(t) \lesssim{}& C_b + \sum_i B_i(t) (\delta_i - b_i(t)) \nonumber \\
        & + E\left[\sum_i B_i(t) s_i^t \bigg| \mathbf{\Theta}(t)\right] - E\left[\sum_i B_i(t) h_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
        & + E\left[ \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \bigg| \mathbf{\Theta}(t) \right] \label{eq_delta_lb_bound}
    \end{align}
    其中，符号 $\lesssim$ 强调了此上界是基于前述忽略电池容量饱和效应的近似而获得的。
    \begin{remark}[关于电池饱和效应近似的影响]
    式~\eqref{eq_delta_lb_bound} 中能量赤字队列漂移上界的推导，其有效性基于忽略电池容量上限 $b_{\max}$ 的饱和效应这一近似。此近似虽显著简化了理论分析，并保留了优化目标 $W^*(t)$ (式~\eqref{eq_W_star_final}) 中各项成本与收益由能量赤字队列 $B_i(t)$ 加权的关键结构，但在电池频繁饱和的场景下，该上界可能不够紧密。这种不精确性可能会对基于此上界设计的算法在这些极端条件下的实际性能产生一定影响。然而，Lyapunov 优化框架通常展现出较好的鲁棒性，能够在一定程度上容忍此类近似带来的模型失配，特别是当控制参数 $V$ 的取值较大时，系统的整体性能将更多地受到由 $V$ 驱动的吞吐量最大化项的主导。
    \end{remark}

    \subsubsection{总漂移上界与逐时隙优化}
    结合上界式~\eqref{eq_delta_lq_bound} 和式~\eqref{eq_delta_lb_bound}，总漂移 $\Delta L(\Theta(t)) = \Delta L_Q(t) + \Delta L_B(t)$ 的上界为：
    \begin{align}
        \Delta L(\Theta(t)) \le C &+ \sum_{i,k} q_{i,k}(t) E[a_{i,k}^t | \Theta(t)] + \sum_i B_i(t) (\delta_i - b_i(t)) \nonumber \\
        &+ E\left[\sum_i B_i(t) s_i^t \bigg| \Theta(t)\right] - E\left[\sum_i B_i(t) h_i^t \bigg| \Theta(t)\right] \nonumber \\
        &+ E \bigg[ \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
        & \quad + \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \bigg| \Theta(t) \bigg] \label{eq_drift_upper_bound_detailed}
    \end{align}
    其中 $C = C_q + C_b$ 是一个常数。

    将漂移上界式~\eqref{eq_drift_upper_bound_detailed} 代入漂移加惩罚函数式~\eqref{eq_drift_plus_penalty_func}，我们得到 $Y(t)$ 的一个上界：
    \begin{align}
        Y(t) \le C'(\Theta(t)) &+ E \bigg[ \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
        & \quad + \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \nonumber \\
        & \quad - V \sum_{k, l=(i,k)} f_{l,k}^t \bigg| \Theta(t) \bigg] \label{eq_Y_upper_bound}
    \end{align}
    其中 $C'(\Theta(t))$ 包括常数 $C = C_q + C_b$ 以及涉及 $q_{i,k}(t)$ 和 $B_i(t)$ 的项，这些项由期望到达量 $E[a_{i,k}^t | \Theta(t)]$、感知成本 $E[s_i^t | \Theta(t)]$、环境能量收集 $E[h_i^t | \Theta(t)]$ 和当前电池水平 $b_i(t)$ 加权。关键的是，$C'(\Theta(t))$ 不依赖于当前时隙 $t$ 做出的控制决策 $(\mathbf{x}^t, \mathbf{y}^t, \mathbf{p}^t, \mathbf{e}^t)$。
    
    Lyapunov 优化方法转而最小化 $Y(t)$ 在式~\eqref{eq_Y_upper_bound} 中给出的上界，而非直接最小化精确的 $Y(t)$ (问题 P2)，后者因涉及期望及未来状态而难以直接求解。由于 $C'(\Theta(t))$ 独立于当前的控制动作，最小化上界等价于最小化方括号内的期望项。最小化此项等价于最大化其负值。因此，我们得到逐时隙优化问题 P3：
    \begin{equation}
        \label{eq_P3}
        \begin{array}{rrl}
            \textbf{(P3)} &\max\limits_{\textbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t} & W^*(t) \\
            & \text{s.t.}& \text{时隙 } t \text{ 的瞬时约束 (式}.~\eqref{eq_s}-\eqref{eq_y})
        \end{array}
    \end{equation}
    其中目标函数 $W^*(t)$ 定义为：
    \begin{align}
        W^*(t) := & V \sum_{k, l=(i,k)} f_{l,k}^t - \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
        & - \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \label{eq_maximize_target_W_detailed}
    \end{align}
    重新排列各项，得到算法设计中使用的形式：
    \begin{align}
        W^*(t) = & \underbrace{\sum_{l=(i,j) \in E} \sum_{k \in K} (q_{i,k}(t) - q_{j,k}(t)) f_{l,k}^t + V \sum_{k, l=(i,k)} f_{l,k}^t}_{\text{第 1 项：加权数据传输效益}} \nonumber \\
        & + \underbrace{\sum_{i \in N} B_i(t) (\check{e}_i^t - (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t))}_{\text{第 2 项：加权净能量增益（不包括环境收集）}} \label{eq_W_star_final}
    \end{align}
    问题 P3 是一个确定性优化问题，需要在每个时隙 $t$ 仅基于观测到的当前状态 $\Theta(t)$、当前电池水平 $b(t)$ 和当前信道条件 $g^t$ 来解决。下一节提出的在线算法 Lyapunov-MEC 旨在在每个时隙解决（或近似解决）P3。

	\section{基于 Lyapunov 的 MEC 算法 (Lyapunov-MEC)}
	\label{sec_lyapunov_algo}
	   直接在每个时隙精确求解问题 P3，即最大化式~\eqref{eq_W_star_final} 中的目标函数 $W^*(t)$，是一个极具挑战性的混合整数非线性规划问题。其复杂性主要源于：(1) 离散的链路激活决策 ($\mathbf{x}^t$) 和能量协作模式决策 ($\mathbf{y}^t$) 与连续的功率分配决策 ($\mathbf{p}^t, \mathbf{e}^t$) 之间的紧密耦合；(2) 能量收集过程 ($\check{e}_i^t$) 和数据传输速率 ($r_l^t$) 对控制决策的非线性依赖关系。考虑到在线实现的计算可行性要求，本文提出了一种低复杂度的启发式在线算法，称为 Lyapunov-MEC。该算法旨在通过一系列基于 $W^*(t)$ 函数结构的贪婪决策，在每个时隙近似最大化该目标函数，同时严格满足所有瞬时约束条件。

	   Lyapunov-MEC 算法在每个时隙 $t$ 独立运行，其核心决策过程分为两个主要阶段：首先确定能量协作相关的动作，随后确定数据传输相关的动作。

    \subsection{算法概述}
    在每个时隙 $t$ 的开始阶段，Lyapunov-MEC 算法首先观测当前的系统状态，包括组合队列向量 $\mathbf{\Theta}(t) = (\mathbf{q}(t), \mathbf{B}(t))$、各节点的电池能量水平 $\mathbf{b}(t)$ 以及所有链路的瞬时信道增益 $\mathbf{g}^t$。基于这些观测值，算法按以下两个主要阶段顺序执行决策：
    
    \subsubsection{阶段 1：能量协作决策 \texorpdfstring{$(\mathbf{y}^t, \mathbf{e}^t)$}{(y\textasciicircum t, e\textasciicircum t)}}
    此阶段旨在确定参与能量传输的节点 ($y_i^t=1$) 及其相应的传输功率 ($\hat{e}_i^t$)。这些决策主要影响目标函数 $W^*(t)$ (式~\eqref{eq_W_star_final}) 中的加权净能量增益项，并采用启发式方法制定：
    \begin{enumerate}
        \item \textbf{识别潜在发送者：} 满足能量条件 $b_i(t) > \alpha \cdot b_{\max}$ 且能量赤字条件 $B_i(t) < \beta'$ 的节点 $i$ 被识别为潜在的能量发送者。其中 $\alpha \in (0, 1]$ 和 $\beta' \ge 0$ 是算法的输入参数。
        \item \textbf{识别潜在接收者：} 满足能量条件 $b_j(t) < \gamma \cdot b_{\max}$ 且能量赤字条件 $B_j(t) > \zeta'$ 的节点 $j \in \mathcal{N}_i$ 被识别为潜在的能量接收者。其中 $\gamma \in [0, 1)$ 和 $\zeta' \ge 0$ 是算法的输入参数。
        \item \textbf{估计增益：} 对于每个潜在发送者 $i$，估计以试验功率 $e_{\text{trial}} \in [e_{\min}, e_{\max}]$ 传输能量在 $W^*(t)$ 中的边际增益。该增益计算如下：
        \begin{equation}
            \Delta W_i^{\text{EC}}(e_{\text{trial}}) = \sum_{j \in \mathcal{N}_i, y_j^t=0} B_j(t) \Delta \check{e}_j^t(e_{\text{trial}}) - B_i(t) e_{\text{trial}}
            \label{eq_ec_gain_est}
        \end{equation}
        其中 $\Delta \check{e}_j^t(e_{\text{trial}})$ 代表邻居节点 $j$ 因节点 $i$ 以试验功率 $e_{\text{trial}}$ 进行能量传输而预期额外收集的 RF 能量。此增量是基于一个简化的、忽略并发传输干扰的模型估算得出：假设仅有节点 $i$ 以功率 $e_{\text{trial}}$ 发射能量，从而计算节点 $j$ 接收到的 RF 功率 $Rf_j^{\text{est}} = e_{\text{trial}} g_{i,j}^t$；利用非线性 RF 能量收集模型 (式~\eqref{eq_EH1}) 计算相应的收集能量 $\check{e}_j^{\text{est}} = \text{EH\_Model}(Rf_j^{\text{est}})$。因此，能量增量被设定为 $\Delta \check{e}_j^t(e_{\text{trial}}) = \check{e}_j^{\text{est}}$，这隐含了对基线收集量（即无此特定传输时的收集量）为零的假设。此估计方法未计入网络中其他潜在并发能量传输或数据传输可能产生的干扰效应。
        \item \textbf{迭代贪婪选择：} 采用迭代方式选择能量发送者。在每次迭代中，从未被选中的潜在发送者中选择提供最大正增益 $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*)$ 的节点 $i^*$，且满足能量因果关系 $e_{\text{trial}}^* \le b_{i^*}^t$。如果存在这样的 $i^*$ 且 $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*) > 0$，则设置 $y_{i^*}^t = 1$ 和 $\hat{e}_{i^*}^t = e_{\text{trial}}^*$，并将该节点从本阶段的潜在发送者集合中移除。此阶段仅确定节点的能量传输决策 ($\mathbf{y}^t, \mathbf{e}^t$)，并不限制其参与后续的数据传输决策。重复此迭代过程，直到没有潜在发送者能提供正增益。
        \item 未被选为发送者的节点是潜在的接收者 ($y_i^t=0$)。
    \end{enumerate}

    \subsubsection{阶段 2：数据传输决策 \texorpdfstring{$(\mathbf{x}^t, \mathbf{p}^t, \mathbf{f}^t)$}{(x\textasciicircum t, p\textasciicircum t, f\textasciicircum t)}}
    在确定了能量协作决策 $(\mathbf{y}^t, \mathbf{e}^t)$ 后，此阶段旨在选择激活的数据传输链路 ($x_{l,k}^t=1$)、分配传输功率 ($p_l^t$) 并确定数据流 ($f_{l,k}^t$)，以近似最大化 $W^*(t)$ (式~\eqref{eq_W_star_final})。此阶段允许所有节点（包括阶段 1 的能量发送者）参与数据传输决策，但须满足半双工及组合能量约束。
    \begin{enumerate}
        \item \textbf{计算链路权重：} 对于每个存在待传数据的潜在链路 $l=(i,j) \in \mathcal{E}$（即 $\exists k, q_{i,k}(t)>0$），计算其基础数据传输效益权重 $W_{l,k}$，该权重反映了传输单位数据对降低队列积压和增加吞吐量的贡献：
        \begin{equation}
            W_{l,k} = q_{i,k}(t) - q_{j,k}(t) + V \cdot \mathbb{I}(j=k)
            \label{eq_link_weight}
        \end{equation}
        其中 $\mathbb{I}(j=k)$ 是指示函数，当接收节点 $j$ 是最终汇聚节点 $k$ 时为 1，否则为 0。
        \item \textbf{计算净权重：} 激活链路 $l=(i,j)$ 不仅带来由 $W_{l,k}$ 量化的数据传输效益，还会产生能量成本，包括数据传输本身消耗的能量 $\hat{p}_i^t = p_l^t$ 和数据接收消耗的能量 $\check{p}_j^t = p_{\text{rcv}} f_{l,k}^t$。这些成本由发送者和接收者的能量赤字队列 $B_i(t)$ 和 $B_j(t)$ 加权。为了综合评估链路激活的价值，我们计算其净权重 $\text{NetWeight}_{l,k}(p_l^t)$：
        \begin{equation}
            \text{NetWeight}_{l,k}(p_l^t) = (W_{l,k}) f_{l,k}^t(p_l^t) - B_i(t) p_l^t - B_j(t) p_{\text{rcv}} f_{l,k}^t(p_l^t)
            \label{eq_net_link_weight}
        \end{equation}
        其中 $f_{l,k}^t(p_l^t)$ 为给定传输功率 $p_l^t$ 时，节点 $i$ 经链路 $l$ 向汇聚节点 $k$ 传输的实际数据流。该数据流受链路容量 $r_l^t(p_l^t)$ (式~\eqref{eq_r})、发送者队列 $q_{i,k}^t$、接收者 $j$ 的剩余可用接收能量 (扣除其自身能量传输消耗 $\hat{e}_j^t$ 后) 及可用缓冲区空间的限制。具体计算如下（假设 $p_l^t$ 已确定）：
        \begin{equation}
            f_{l,k}^t(p_l^t) = \min\left(r_l^t(p_l^t), q_{i,k}^t, \lfloor (b_j^t - \hat{e}_j^t) / p_{\text{rcv}} \rfloor, q_{\max} - \sum_{k' \in \mathcal{K}} q_{j,k'}^t\right)
            \label{eq_f_l_k_calc}
        \end{equation}
        注意，计算 $f_{l,k}^t$ 时需确保 $\lfloor (b_j^t - \hat{e}_j^t) / p_{\text{rcv}} \rfloor \ge 0$。
        \item \textbf{迭代贪婪链路选择：} 采用迭代方式选择激活的数据链路。
            a. 初始化一个集合 $\mathcal{N}_{\text{data\_busy}} = \emptyset$，用于记录在本时隙已参与数据传输（发送或接收）的节点。识别所有存在待传数据的潜在链路集合 $\mathcal{L}_{\text{pot}} = \{l=(i,j) \in \mathcal{E} | \exists k, q_{i,k}(t)>0\}$。
            b. 对 $\mathcal{L}_{\text{pot}}$ 中各潜在链路 $l=(i,j)$ 及相关汇聚节点 $k$ 进行初步评估。获取由阶段 1 确定的节点 $i, j$ 的能量传输功率 $\hat{e}_i^t, \hat{e}_j^t$。计算满足最低 SNR 阈值 $\gamma_{\min}$ 所需的最小传输功率 $p_{\text{required\_min\_W}}$ (基于式~\eqref{eq_p_f} 并考虑 $p_{\min}, p_{\max}$ 约束)。若 $p_{\text{required\_min\_W}}$ 不可行 (如超出范围或信道过差)，则忽略此链路。校验发送者 $i$ 的组合能量约束：$p_{\text{required\_min\_W}} + \hat{e}_i^t \le b_i^t$。若满足，则以此功率 $p_{\text{trial}} = p_{\text{required\_min\_W}}$ 计算潜在数据流 $f_{\text{potential}}$ (依据式~\eqref{eq_f_l_k_calc})。进一步校验接收者 $j$ 的组合能量约束：$p_{\text{rcv}} f_{\text{potential}} + \hat{e}_j^t \le b_j^t$。若所有校验通过且 $f_{\text{potential}}>0$，则计算相应的净权重 $\text{NetWeight}_{l,k}(p_{\text{required\_min\_W}})$ (式~\eqref{eq_net_link_weight})，并将此 (链路, 汇聚节点) 对及其净权重、潜在流量和所需功率存入候选列表 $Pairs$。
            c. 将 $Pairs$ 列表按净权重 $\text{NetWeight}_{l,k}(p_{\text{required\_min\_W}})$ 降序排序。
            d. 按排序顺序迭代处理 $Pairs$ 中的每个候选对 $((l^*, k^*), \text{Weight}, f_{\text{potential}}, p_{\text{req}})$，其中 $l^*=(i^*, j^*)$：
            e. \textbf{检查数据半双工约束：} 如果发送节点 $i^*$ 和接收节点 $j^*$ 均未包含在 $\mathcal{N}_{\text{data\_busy}}$ 中。
            f. \textbf{确定最终功率和流量：} 使用候选对中存储的所需功率 $p_{l^*}^t = p_{\text{req}}$ 和潜在流量 $f_{l^*,k^*}^t = f_{\text{potential}}$。获取 $\hat{e}_{i^*}^t$ 和 $\hat{e}_{j^*}^t$。
            g. \textbf{最终组合能量检查：} 再次确认发送者能量 $p_{l^*}^t + \hat{e}_{i^*}^t \le b_{i^*}^t$ 和接收者能量 $p_{\text{rcv}} f_{l^*,k^*}^t + \hat{e}_{j^*}^t \le b_{j^*}^t$ 是否满足。
            h. \textbf{激活链路：} 如果半双工和能量约束均满足，并且预计算的净权重 $\text{Weight} > 0$，则激活该链路：设置 $x_{l^*,k^*}^t = 1$，记录 $p_{l^*}^t = p_{\text{req}}$ 和实际流量 $f_{l^*,k^*}^t = f_{\text{potential}}$。
            i. \textbf{标记节点数据繁忙：} 如果链路被激活，则将节点 $i^*$ 和 $j^*$ 加入 $\mathcal{N}_{\text{data\_busy}}$ 集合。
            j. 继续处理排序列表中的下一个候选对，直到列表处理完毕。
    \end{enumerate}

    \subsection{算法伪代码}
    整个过程分为两个阶段，分别总结在算法~\ref{alg_lyapunov_mec_phase1} 和算法~\ref{alg_lyapunov_mec_phase2} 中。

% Algorithm 1: Phase 1
\begin{algorithm}
\linespread{1.1}\selectfont
    \caption{Lyapunov-MEC Algorithm: Phase 1 (Energy Cooperation)}
    \label{alg_lyapunov_mec_phase1}
    \KwIn{Current state $\Theta(t)=(\mathbf{q}(t), \mathbf{B}(t))$, $\mathbf{b}(t)$, $\mathbf{g}^t$; Energy thresholds $\alpha, \beta'$; Power bounds $e_{\min}, e_{\max}$;}
    \KwOut{Energy decisions $\mathbf{y}^t, \mathbf{e}^t$}
    Initialize $\mathbf{y}^t=\mathbf{0}, \mathbf{e}^t=\mathbf{0}$ \\
    Initialize $\mathbb{S}_E^t = \emptyset$ \\
    Initialize $\mathcal{N}_{\text{avail}}^{\text{energy}} = \mathcal{N}$ \\

    \tcp{Iterative Energy Cooperation Decision}
    \While{true}{
        Find potential sender $i^*$ and power $e_{\text{trial}}^* \in [e_{\min}, \min(e_{\max}, b_{i^*}^t)]$ that maximizes $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*)$ among $i^* \in \mathcal{N}_{\text{avail}}^{\text{energy}}$ such that: \\
        \quad a) $i^*$ satisfies sender conditions: $b_{i^*}(t) > \alpha \cdot b_{\max}$ and $B_{i^*}(t) < \beta'$ \\
        \quad b) Gain is positive: $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*) > 0$, calculated using Eq.~\eqref{eq_ec_gain_est} (simplified $\Delta \check{e}_j^t$) \\
        
        \If{such an $i^*$ exists}{
            Set $y_{i^*}^t = 1$, $\hat{e}_{i^*}^t = e_{\text{trial}}^*$ \\
            $\mathbb{S}_E^t = \mathbb{S}_E^t \cup \{i^*\}$ \\
            $\mathcal{N}_{\text{avail}}^{\text{energy}} = \mathcal{N}_{\text{avail}}^{\text{energy}} \setminus \{i^*\}$
        }
        \Else{
            Break
        }
    }
\end{algorithm}

% Algorithm 2: Phase 2 and State Update 
\begin{algorithm}
\linespread{0.9}\selectfont
    \caption{Lyapunov-MEC Algorithm: Phase 2 (Data Transmission) and State Update}
    \label{alg_lyapunov_mec_phase2}
    \KwIn{Current state $\Theta(t)=(\mathbf{q}(t), \mathbf{B}(t))$, $\mathbf{b}(t)$, $\mathbf{g}^t$; Phase 1 outputs $\mathbf{y}^t, \mathbf{e}^t$; Parameters $V, p_{\min}, p_{\max}, \gamma_{\min}, \sigma^2, W, p_{\text{rcv}}, q_{\max}, b_{\max}$; Thresholds $\delta_i$}
    \KwOut{Updated state $\Theta(t+1), \mathbf{b}(t+1)$; Data decisions $\mathbf{x}^t, \mathbf{p}^t, \mathbf{f}^t$}
    Initialize $\mathbf{x}^t=\mathbf{0}, \mathbf{p}^t=\mathbf{0}, \mathbf{f}^t = \mathbf{0}, \mathbb{S}_D^t = \emptyset, \mathcal{N}_{\text{data\_busy}} = \emptyset$ \\

    Initialize list $CandidateLinks = []$ \\
    \For{each link $l=(i,j) \in \mathcal{E}$ and destination $k \in \mathcal{K}$ with $q_{i,k}(t)>0$}{
        Let $\hat{e}_i^t = \mathbf{e}^t[i]$ and $\hat{e}_j^t = \mathbf{e}^t[j]$ \\
        Calculate $p_{\text{required\_min\_W}}$ based on $g_l^t, \gamma_{\min}, \sigma^2, p_{\min}, p_{\max}$ \\
        \If{$p_{\text{required\_min\_W}} \neq \infty$ and $p_{\text{required\_min\_W}} + \hat{e}_i^t \le b_i^t$}{
             Calculate potential flow $f_{\text{potential}}$ using Eq.~\eqref{eq_f_l_k_calc} with $p_l^t = p_{\text{required\_min\_W}}$ and considering $\hat{e}_j^t$ \\
             \If{$p_{\text{rcv}} f_{\text{potential}} + \hat{e}_j^t \le b_j^t$ and $f_{\text{potential}} > 0$}{
                Calculate link weight $W_{l,k}$ using Eq.~\eqref{eq_link_weight} \\
                Calculate net weight $\text{NetWeight} = (W_{l,k}) f_{\text{potential}} - B_i(t) p_{\text{required\_min\_W}} - B_j(t) p_{\text{rcv}} f_{\text{potential}}$ \\
                Add tuple $((l, k), \text{NetWeight}, f_{\text{potential}}, p_{\text{required\_min\_W}})$ to $CandidateLinks$
             }
        }
    }

    Sort $CandidateLinks$ by $\text{NetWeight}$ in descending order \\
    \For{each ranked tuple $((l^*, k^*), \text{Weight}, f_{\text{potential}}, p_{\text{req}})$ in $CandidateLinks$ where $l^*=(i^*, j^*)$}{
        \If{$i^* \notin \mathcal{N}_{\text{data\_busy}}$ and $j^* \notin \mathcal{N}_{\text{data\_busy}}$}{
            \If{$\text{Weight} > 0$}{
                Set $x_{l^*,k^*}^t = 1$, $p_{l^*}^t = p_{\text{req}}$ \\
                Store $f_{l^*,k^*}^t = f_{\text{potential}}$ in the flow matrix $\mathbf{f}^t$ \\
                $\mathbb{S}_D^t = \mathbb{S}_D^t \cup \{l^*\}$ \\
                $\mathcal{N}_{\text{data\_busy}} = \mathcal{N}_{\text{data\_busy}} \cup \{i^*, j^*\}$
            }
        }
    }
    Calculate final $\hat{p}_i^t = \sum_{l \in \mathcal{O}_i \cap \mathbb{S}_D^t} p_l^t$ and $\check{p}_i^t = \sum_{l \in \mathcal{I}_i} \sum_{k' \in \mathcal{K}} p_{\text{rcv}} \cdot \mathbf{f}^t[l, k']$

    Calculate actual RF received power $Rf_i^{\text{eff},t}$ using Eq.~\eqref{eq_Rf_eff} based on final $\mathbf{y}^t, \mathbf{e}^t, \mathbf{p}^t$ \\
    Calculate actual energy harvested $\check{e}_i^t$ using Eq.~\eqref{eq_EH1} with $Rf_i^{\text{eff},t}$ \\
    Calculate total consumption $\text{Cons}_i^t = s_i^t + \hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t$ \\
    Update $b_i(t+1) = \min(b_{\max}, b_i(t) - \text{Cons}_i^t + \check{e}_i^t + h_i^t)$ \\
    Update $q_{i,k}(t+1)$ using Eq.~\eqref{eq_q} with final $\mathbf{f}^t$ \\
    Update $B_i(t+1)$ using Eq.~\eqref{eq_virtual_energy_queue} with $b_i(t+1)$ \\
\end{algorithm}

    \subsection{复杂度分析}
    我们分析每个时隙执行 Lyapunov-MEC 算法（算法~\ref{alg_lyapunov_mec_phase1} 和~\ref{alg_lyapunov_mec_phase2}）的计算复杂度。令 $d_{max}$ 为网络中节点的最大度数。
    \textbf{算法~\ref{alg_lyapunov_mec_phase1} (Phase 1):} 该阶段包含一个迭代循环，最多执行 $N$ 次。在每次迭代中，算法搜寻能提供最大正能量协作增益 $\Delta W_i^{\text{EC}}$ 的潜在发送者。此过程涉及对不超过 $N$ 个候选节点进行条件检查，并为每个候选节点计算其增益。单个增益的计算需遍历该节点的邻居（数量上限为 $d_{max}$），故其复杂度为 $O(d_{max})$。因此，单次迭代中寻找最优发送者的复杂度为 $O(N \cdot d_{max})$，导致 Phase 1 的总复杂度为 $O(N^2 \cdot d_{max})$。
    \textbf{算法~\ref{alg_lyapunov_mec_phase2} (Phase 2 + Update):} 此阶段首先为所有潜在的 (链路, 汇聚节点) 对（数量为 $O(EK)$）计算初始净权重，此步骤复杂度为 $O(EK)$。随后，对这些候选对进行排序，其复杂度为 $O(EK \log(EK))$。接下来的迭代选择过程最多执行 $EK$ 次，每次迭代内部操作（如检查节点可用性、计算数据流、更新状态集合等）均具有较低复杂度（通常视为 $O(1)$）。最后的状态更新步骤中，计算各节点实际收集的 RF 能量 $\check{e}_i^t$ 需 $O(N \cdot d_{max})$；更新所有数据队列 $q_{i,k}$ (依据式~\eqref{eq_q}) 则涉及遍历各节点的入向和出向链路，总复杂度为 $O(NK d_{\max})$；更新能量赤字队列 $B_i$ 的复杂度为 $O(N)$。因此，Phase 2 及状态更新的总复杂度主要由排序和队列更新主导，可表示为 $O(EK \log(EK) + NK d_{\max})$。
    \textbf{总体复杂度：} Lyapunov-MEC 算法在每个时隙的总计算复杂度由上述两个阶段的复杂度叠加而成，即 $O(N^2 \cdot d_{max} + EK \log(EK) + NK d_{\max})$。

	Lyapunov-MEC 算法提供了一种在线控制策略，该策略动态适应网络条件（队列积压、能量水平、信道状态），旨在优化 LTA 吞吐量，同时管理能量约束。作为一种旨在平衡性能和计算复杂性的启发式近似算法，其相对于理论最优策略（将在下一节分析）的实际性能表现，需要通过仿真进行评估。

    \section{Lyapunov-MEC 的理论分析}
    \label{sec_analysis}
    本节致力于为所提出的 Lyapunov-MEC 算法（其具体流程由算法~\ref{alg_lyapunov_mec_phase1} 与算法~\ref{alg_lyapunov_mec_phase2} 共同定义）建立理论性能保证。具体而言，我们将证明该算法能够保障所有网络队列（包括实际数据队列及虚拟能量赤字队列）的稳定性，并分析其实现的长期平均 (LTA) 吞吐量与理论最优值之间的关系。该分析遵循 Lyapunov 优化的标准方法~\cite{neely2010stochastic}。

    \subsection{队列稳定性保证}
    本小节旨在建立 Lyapunov-MEC 算法下所有相关队列的稳定性。队列的稳定性是确保有界数据延迟及满足 LTA 能量约束的核心前提。

    \begin{lemma}[队列稳定性]
        \label{lemma_queue_stability}
        假设平均数据到达率向量 $\lambda = (E[a_{i,k}^t])$ 严格位于网络的稳定区域 $\Lambda$ 内。稳定区域 $\Lambda$ 是所有到达率向量的集合，对于这些向量，存在某个平稳随机策略 $\pi^*$ 可以满足所有网络约束并稳定所有队列。对于任何在每个时隙 $t$ 做出决策 $(\mathbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t)$ 以最小化式~\eqref{eq_Y_upper_bound} 中漂移加惩罚上界的控制策略（以参数 $V > 0$ 执行），所有数据队列 $q_{i,k}(t)$ 和能量赤字队列 $B_i(t)$ 都是强稳定的。也就是说，它们的长期时间平均值是有界的：
        \begin{align}
            \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} \sum_{i,k} E[q_{i,k}(t)] &< \infty \label{eq_data_queue_stable}\\
            \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} \sum_{i} E[B_i(t)] &< \infty \label{eq_energy_queue_stable}
        \end{align}
    \end{lemma}
    \begin{proof}
        证明依赖于分析 Lyapunov 漂移加惩罚表达式。根据第 V.C 节，该算法在每个时隙最小化漂移加惩罚上界。令最小化后的值为 $Y_{min}(t)$，最优平稳策略 $\pi^*$ 下的值为 $Y^*(t)$。我们有：
        \begin{equation}
            \Delta L(\Theta(t)) - V E[\text{DeliveredData}(t) | \Theta(t)] \le Y_{min}(t)
        \end{equation}
        由于 Lyapunov-MEC 在当前时隙的所有可行策略上最小化该表达式，其性能至少与应用于当前状态的平稳策略 $\pi^*$ 的性能一样好：
        \begin{equation}
            Y_{min}(t) \le Y^*(t) = E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
        \end{equation}
        其中 $\Delta L^*(\Theta(t))$ 是策略 $\pi^*$ 下的漂移。
        结合这些，我们得到：
        \begin{align}
            \Delta L(\Theta(t)) &- V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
            &\le E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
        \end{align}
        由于 $\lambda$ 严格位于稳定区域 $\Lambda$ 内，存在一个 $\epsilon > 0$，使得策略 $\pi^*$ 满足稳定性条件：
        \begin{align}
            E[A_{i,k}^*(t) | \Theta(t)] &\le E[\text{Outflow}_{i,k}^*(t) | \Theta(t)] + E[a_{i,k}^t] - \epsilon \\
            E[b_i^*(t+1) | \Theta(t)] &\ge \delta_i + \epsilon
        \end{align}
        使用这些条件，可以证明（遵循标准的 Lyapunov 分析~\cite{neely2010stochastic}）
        $\pi^*$ 下的漂移满足：
        \begin{equation}
            E[\Delta L^*(\Theta(t)) | \Theta(t)] \le C^* - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t))
        \end{equation}
        对于某些常数 $C^*, \epsilon' > 0$。
        将此代回漂移加惩罚不等式：
        \begin{align}
            & \Delta L(\Theta(t)) - V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
            &\le C^* - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t)) - V E[\text{DeliveredData}^*(t) | \Theta(t)]
        \end{align}
        由于 $E[\text{DeliveredData}(t)]$ 和 $E[\text{DeliveredData}^*(t)]$ 是非负的，我们可以界定该表达式：
        \begin{equation}
            \Delta L(\Theta(t)) \le C^{**} - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t))
        \end{equation}
        其中 $C^{**} = C^* + V \cdot \text{MaxPossibleThroughput}$ 是一个常数（假设吞吐量有界）。
        这个不等式表明，只要队列长度的加权和 $\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t)$ 足够大（大于 $C^{**}/\epsilon'$），漂移就是负的。根据 Foster-Lyapunov 稳定性判据~\cite{meyn2012markov}，
        这个负漂移条件意味着系统状态（由队列表示）是正常返的，因此是稳定的。在到达和服务过程的二阶矩满足温和条件下，强稳定性（有界平均队列大小）随之成立。
    \end{proof}

    \subsection{LTA 能量约束满足}
    虚拟能量赤字队列 $B_i(t)$ 的稳定性是确保 LTA 能量约束得以满足的关键。以下推论将对此进行证明，其分析方法借鉴了~\cite{Gao2024SWIPT}中引理 2 的思路。

    \begin{corollary}[LTA 能量约束满足]
        \label{corollary_energy_constraint}
        在引理~\ref{lemma_queue_stability} 的条件下，遵循最小化漂移加惩罚上界原则的控制策略确保对于所有节点 $i \in \mathcal{N}$，LTA 电池能量约束（式~\eqref{eq_lta_energy}）得到满足：
        $$ \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge \delta_i $$
    \end{corollary}
    \begin{proof}
        根据虚拟队列 $B_i(t)$ 的更新规则（式~\eqref{eq_virtual_energy_queue}），我们有 $B_i(t+1) \ge B_i(t) + \delta_i - b_i(t+1)$。
        重新排列得到：
        \begin{equation}
            b_i(t+1) \ge B_i(t) - B_i(t+1) + \delta_i
        \end{equation}
        将此不等式从 $t=0$ 到 $T-1$ 求和：
        \begin{align}
            \sum_{t=0}^{T-1} b_i(t+1) &\ge \sum_{t=0}^{T-1} (B_i(t) - B_i(t+1)) + \sum_{t=0}^{T-1} \delta_i \nonumber \\
            &= (B_i(0) - B_i(1)) + (B_i(1) - B_i(2)) + \dots + (B_i(T-1) - B_i(T)) + T \delta_i \nonumber \\
            &= B_i(0) - B_i(T) + T \delta_i
        \end{align}
        对两边取期望：
        \begin{equation}
            \sum_{t=0}^{T-1} E[b_i(t+1)] \ge E[B_i(0)] - E[B_i(T)] + T \delta_i
        \end{equation}
        除以 $T$：
        \begin{equation}
            \frac{1}{T} \sum_{t=1}^{T} E[b_i(t)] \ge \frac{E[B_i(0)]}{T} - \frac{E[B_i(T)]}{T} + \delta_i
        \end{equation}
        当 $T \to \infty$ 时取下极限：
        \begin{equation}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=1}^{T} E[b_i(t)] \ge \liminf_{T \to \infty} \left( \frac{E[B_i(0)]}{T} - \frac{E[B_i(T)]}{T} + \delta_i \right)
        \end{equation}
        由于 $B_i(0)$ 是有限的初始值，$\lim_{T \to \infty} E[B_i(0)]/T = 0$。根据引理~\ref{lemma_queue_stability}，$B_i(t)$ 是强稳定的，这意味着平均队列大小是有界的，$ \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[B_i(t)] < \infty$。这进一步意味着 $\lim_{T \to \infty} E[B_i(T)]/T = 0$（否则平均值将无界增长）。
        因此，我们有：
        \begin{equation}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge 0 - 0 + \delta_i = \delta_i
        \end{equation}
        这证实了 LTA 电池能量约束得到满足。
    \end{proof}

    \subsection{LTA 吞吐量性能界限}
    最后，本小节旨在推导所提出算法在 LTA 吞吐量方面的性能下界，并将其与理论最优值进行比较。分析方法与~\cite{Gao2024SWIPT}中相关引理的证明思路相似。

    \begin{lemma}[LTA 吞吐量界限]
        \label{lemma_throughput_bound}
        令 $\mathcal{D}^*$ 为受网络约束的最大可实现 LTA 吞吐量（可能在最优的、可能是预知策略 $\pi^*$ 下）。对于一个旨在最小化漂移加惩罚上界的控制策略，其实现的 LTA 吞吐量 $\bar{\mathcal{D}}$ 满足：
        \begin{equation}
            \label{eq_throughput_lower_bound}
            \bar{\mathcal{D}} = \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \ge \mathcal{D}^* - \frac{C}{V}
        \end{equation}
        其中 $C$ 是来自漂移界限（式~\eqref{eq_drift_upper_bound_detailed}）的常数，与平方到达和服务速率的界限有关，$V$ 是控制参数。
    \end{lemma}
    \begin{proof}
        基于 Lyapunov-MEC 算法旨在最小化漂移加惩罚上界 (式~\eqref{eq_Y_upper_bound}) 这一核心原则，并参考引理~\ref{lemma_queue_stability} 证明过程中的关键不等式，我们有：
        \begin{align}
                     \Delta L(\Theta(t)) &- V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
                     &\le E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
                \end{align}
        对 $\Theta(t)$ 取期望：
        \begin{align}
                     E[\Delta L(\Theta(t))] &- V E[\text{DeliveredData}(t)] \nonumber \\
                     &\le E[\Delta L^*(\Theta(t))] - V E[\text{DeliveredData}^*(t)]
                \end{align}
        项 $E[\Delta L^*(\Theta(t))]$ 代表最优平稳策略 $\pi^*$ 下的期望单时隙 Lyapunov 漂移。对于任何能够稳定系统的平稳策略，其期望漂移必存在一个常数上界，记为 $C_{drift}^*$ (即 $E[\Delta L^*(\Theta(t))] \le C_{drift}^*$)。同时，根据定义，$E[\text{DeliveredData}^*(t)] = \mathcal{D}^*$。
        将这些代入不等式：
        \begin{equation}
             E[L(\Theta(t+1)) - L(\Theta(t))] - V E[\text{DeliveredData}(t)] \le C_{drift}^* - V \mathcal{D}^*
        \end{equation}
        从 $t=0$ 到 $T-1$ 求和：
        \begin{align}
            & \sum_{t=0}^{T-1} (E[L(\Theta(t+1))] - E[L(\Theta(t))]) - V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
            &\le T C_{drift}^* - T V \mathcal{D}^*
        \end{align}
        第一个和是伸缩和：$E[L(\Theta(T))] - E[L(\Theta(0))]$。
        \begin{align}
                    E[L(\Theta(T))] &- E[L(\Theta(0))] \nonumber \\
                    &- V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
                    &\le T C_{drift}^* - T V \mathcal{D}^*
                \end{align}
        由于 $L(\Theta(t)) \ge 0$，我们有 $- E[L(\Theta(0))] - V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \le T C_{drift}^* - T V \mathcal{D}^*$。
        重新排列并除以 $VT$（假设 $V>0$）：
        \begin{equation}
            \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \ge \mathcal{D}^* - \frac{C_{drift}^*}{V} - \frac{E[L(\Theta(0))]}{VT}
        \end{equation}
        当 $T \to \infty$ 时取下极限：
        \begin{align}
            \bar{\mathcal{D}} &= \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
            &\ge \mathcal{D}^* - \frac{C_{drift}^*}{V} + \liminf_{T \to \infty} \left( - \frac{E[L(\Theta(0))]}{VT} \right)
        \end{align}
        由于 $L(\Theta(0))$ 是有限的，$\lim_{T \to \infty} E[L(\Theta(0))]/(VT) = 0$。
        因此，我们得到期望的结果：
        \begin{equation}
            \bar{\mathcal{D}} \ge \mathcal{D}^* - \frac{C_{drift}^*}{V}
        \end{equation}
        我们可以用来自一般漂移界限（式~\eqref{eq_drift_upper_bound_detailed}）的常数 $C$ 替换常数 $C_{drift}^*$，因为它们代表了类似系统动态的界限，从而得到 $\bar{\mathcal{D}} \ge \mathcal{D}^* - C/V$。
    \end{proof}
    
    引理~\ref{lemma_throughput_bound} 量化了理想控制策略的性能保证。它表明，通过选择足够大的控制参数 $V$ 值，理想策略实现的 LTA 吞吐量可以任意接近最优吞吐量 $\mathcal{D}^*$。项 $C/V$ 表示最优性差距。然而，增加 $V$ 通常会导致更大的平均队列大小（如稳定性证明结构所暗示，通常按 $O(V)$ 比例缩放），这代表了随机网络优化中平均性能最优性与队列积压/延迟之间的基本 $[O(1/V), O(V)]$ 权衡。

    \subsection{关于启发式算法 Lyapunov-MEC 的讨论}
    上述理论分析适用于能够精确最小化漂移加惩罚上界的理想控制策略。而本文提出的 Lyapunov-MEC 算法（算法~\ref{alg_lyapunov_mec_phase1} 和~\ref{alg_lyapunov_mec_phase2}）是该理想策略的一个启发式实现，它采用了若干简化来降低计算复杂度：
    \begin{itemize}
        \item \textbf{能量协作决策近似：} 阶段 1 中计算能量协作增益 $\Delta W_i^{\text{EC}}$ 时（式~\eqref{eq_ec_gain_est}），采用了简化的能量收集估计，忽略了来自其他并发传输的潜在干扰或贡献。
        \item \textbf{贪婪链路选择：} 阶段 2 中采用贪婪方法按链路净权重排序并依次选择激活链路，这可能无法达到全局最优的链路组合。
        \item \textbf{功率控制策略：} 阶段 2 中采用了一种动态功率控制策略，即计算并使用满足最低 SNR 阈值 $\gamma_{\min}$ 所需的最小传输功率 $p_{\text{required\_min\_W}}$（受限于 $[p_{\min}, p_{\max}]$），而非进行更复杂的联合功率优化。
    \end{itemize}
    这些近似虽然使得算法可以在线高效运行，但也意味着 Lyapunov-MEC 在每个时隙可能无法完全最小化漂移加惩罚上界。因此，其真实的性能表现（例如实际的吞吐量-延迟权衡）可能偏离理论上的 $[O(1/V), O(V)]$ 界限。此外，如果启发式选择在某些情况下显著偏离最优决策，算法的稳定性可能需要比理论假设更强的条件（例如更小的到达率或更严格的信道/能量条件）。
    
    因此，进行全面的仿真实验对于评估所提出的 Lyapunov-MEC 启发式算法的实际性能、验证其在不同网络场景下的有效性，以及量化其与理论性能界限之间的差距至关重要。

\section{仿真结果与分析}
\label{sec_simulation}
在本节中，我们通过仿真实验来评估所提出的 Lyapunov-MEC 算法的性能。仿真使用 Python  在配备 Intel Core i7 CPU 和 16GB RAM 的 Windows 10 计算机上进行。我们首先介绍仿真设置，包括性能指标、对比算法和参数配置，然后展示并分析仿真结果。

\subsection{仿真设置}
\label{subsec_sim_setup}

\subsubsection{性能指标 (Performance Metrics)}
\label{subsubsec_metrics}
我们采用以下性能指标来评估算法性能：
\begin{itemize}
    \item \textbf{长期平均吞吐量 (Long-Term Average Throughput):} 定义为在整个仿真时长内，所有汇聚节点成功接收到的总数据量与仿真时长的比值 (单位：Mbps)。这是衡量系统数据处理能力的主要指标。
    \item \textbf{平均队列长度 (Average Queue Length):} 包括平均数据队列长度 ($\frac{1}{T}\sum_{t=0}^{T-1} E[\sum_{i,k} q_{i,k}(t)]$) 和平均能量赤字队列长度 ($\frac{1}{T}\sum_{t=0}^{T-1} E[\sum_{i} B_{i}(t)]$)。这些指标用于评估队列稳定性和资源积压情况。
    \item \textbf{平均电池电量 (Average Battery Level):} 定义为 $\frac{1}{T}\sum_{t=0}^{T-1} E[\sum_{i} b_{i}(t)] / N$，用于观察能量维持情况。
\end{itemize}
长期平均吞吐量是主要的性能衡量标准，而队列长度和电池电量作为辅助指标，用于更深入地理解算法行为。

\subsubsection{对比算法 (Algorithms for Comparison)}
\label{subsubsec_benchmarks_list}
为了全面评估所提出的 Lyapunov-MEC 算法，我们将其与以下算法进行比较：
\begin{itemize}
    \item \textbf{Lyapunov-MEC:} 本文所提出的基于 Lyapunov 优化的在线启发式算法，集成了多播能量协作 (M-EC) 机制 (详见算法~\ref{alg_lyapunov_mec_phase1} 与~\ref{alg_lyapunov_mec_phase2})。其数据传输功率 $p_l^t$ 动态调整以满足预设的最低信噪比阈值 $\gamma_{\min}$，即 $p_l^t = p_{\text{required\_min\_W}}$，且其功率范围受限于参数 $p_{\min}$ 和 $p_{\max}$ (详见表~\ref{tab:sim_params})。
    \item \textbf{Lyapunov-NoEC:} Lyapunov-MEC 算法的一个变体，其中能量协作功能被完全禁用。其数据传输功率控制策略与 Lyapunov-MEC 保持一致。
    \item \textbf{Lyapunov-UEC:} Lyapunov-MEC 算法的另一个变体，采用单播能量协作 (U-EC) 替代 M-EC。其数据传输功率控制策略亦与 Lyapunov-MEC 相同。
    \item \textbf{Greedy-MaxWeight (GMW):} 一种经典的贪婪调度算法，基于队列差异进行链路选择，不显式考虑节点能量状态或实施能量协作。该算法采用固定的 5 dBm 数据传输功率。
    \item \textbf{Energy-Aware Greedy (EAG):} GMW 算法的改进版本，引入了基本的能量感知能力，优先选择能量较为充裕的节点进行数据发送。该算法亦采用固定的 5 dBm 数据传输功率。
    \item \textbf{Randomized Scheduling (RAND):} 一种随机调度策略，在满足基本链路约束的前提下随机激活链路，通常作为性能基准的下限。该算法同样采用固定的 5 dBm 数据传输功率。
\end{itemize}
Lyapunov 系列算法的功率参数 ($p_{\min}, p_{\max}$) 详见表~\ref{tab:sim_params}。

\subsubsection{仿真参数 (Simulation Parameters)}
\label{subsubsec_sim_params_desc}
我们考虑一个部署在 100m $\times$ 100m 正方形区域内的无线传感器网络。网络包含 $N$ 个传感器节点和 $K$ 个汇聚节点，它们的具体位置是随机生成的，但确保网络是连通的。除非另有说明，默认设置 $N=20$ 和 $K=2$。传感器节点的最大通信范围设为 30m。

主要的仿真参数设置基于相关文献，总结在表~\ref{tab:sim_params} 中。数据通信参数包括信道带宽 $W = 1$ MHz，噪声功率谱密度 -174 dBm/Hz ($\sigma^2 = -114$ dBm)。采用 Rician 衰落信道模型 (式~\eqref{eq_g_ij_rician})，K 因子 $K_{i,j}=10$~\cite{Gao2015}，以及调整后的 Friis 路径损耗模型 (式~\eqref{eq_g_ij})，参数 $\lambda=0.33$ m, $G_i=8$ dBi, $G_j=2$ dBi, $\beta=0.2$, $L_p=1$ dB~\cite{He2013}。最小 SNR 阈值 $\gamma_{\min} = 5$ dB。对于 Lyapunov 系列算法，其数据传输功率范围为 $[p_{\min}, p_{\max}] = [10, 20]$ dBm (详见表~\ref{tab:sim_params})；对比算法 GMW, EAG, RAND 则采用固定的 5 dBm 传输功率 (详见 Section~\ref{subsubsec_benchmarks_list})。接收和感知能耗分别为 $p_{\text{rcv}} = 50$ $\mu$J/Mbit 和 $p_{\text{sense}} = 60$ $\mu$J/Mbit。数据队列容量 $q_{\max} = 50$ Mbit。

能量相关参数包括电池最大容量 $b_{\max} = 10$ J，初始能量在 $[0.2 b_{\max}, 0.8 b_{\max}]$ 随机分布。环境能量收集遵循~\cite{Ku2015} 的四状态马尔可夫模型，平均收集功率为 [10, 5, 2, 0.5] mW。RF 能量收集采用非线性模型 (式~\eqref{eq_EH1})，参数 $\mu = 1500$, $\nu = 0.002$, $e_{\text{mp}} = 0.024$ W~\cite{Boshkovska2015}，数据信号的 RF 收集效率 $\eta_{\text{data}}=0.1$。能量传输功率范围 $[e_{\min}, e_{\max}] = [10, 100]$ mW。LTA 能量阈值 $\delta_i = 0.3 b_{\max}$。

数据以概率 $p_{\text{arrival}} = 0.2$ 在每个节点生成，目的地随机选择，包大小服从均值为 10 Mbit 的指数分布。Lyapunov-MEC (及变种) 的控制参数 $V$ 默认设为 100，能量协作阈值 $\alpha=0.3, \beta'=10, \gamma=0.4, \zeta'=10.0$。

仿真总时长为 $T = 10000$ 个时隙。为了获得可靠的结果，每个仿真配置都独立运行 30 次，最终结果取平均值。在图中，我们展示了均值以及对应的 95\% 置信区间，以体现结果的统计显著性。


\begin{table}[htbp]
\centering
\caption{仿真参数设置 (Simulation Parameter Settings)}
\label{tab:sim_params}
\resizebox{0.48\textwidth}{!}{
\begin{tabular}{|l|l|c|}
\hline
\textbf{类别 (Category)} & \textbf{参数描述 (Parameter Description)} & \textbf{默认值 (Default Value)} \\
\hline
\multirow{4}{*}{网络设置 (Network)} 
& 部署区域 (Area) & 100m $\times$ 100m \\
& 传感器节点数 ($N$) & 20 \\
& 汇聚节点数 ($K$) & 2 \\
& 最大通信范围 (Max Comm. Range) & 30 m \\
\hline
\multirow{11}{*}{通信参数 (Communication)}
& 信道带宽 ($W$) & 1 MHz \\
& 噪声功率谱密度 (Noise PSD) & -174 dBm/Hz \\
& 噪声功率 ($\sigma^2$) & -114 dBm \\
& Rician K 因子 ($K_{i,j}$) & 10 \cite{Gao2015} \\
& 信号波长 ($\lambda$) & 0.33 m \cite{He2013} \\
& 天线增益 ($G_i, G_j$) & 8 dBi, 2 dBi \cite{He2013} \\
& 路径损耗参数 ($\beta, L_p$) & 0.2, 1 dB \cite{He2013} \\
& 最小 SNR 阈值 ($\gamma_{\min}$) & 5 dB \\
& 数据传输功率 ($[p_{\min}, p_{\max}]$) & [10, 20] dBm \\
& 接收能耗 ($p_{\text{rcv}}$) & 50 $\mu$J/Mbit \\
& 感知能耗 ($p_{\text{sense}}$) & 60 $\mu$J/Mbit \\
& 数据队列容量 ($q_{\max}$) & 50 Mbit \\
\hline
\multirow{8}{*}{能量参数 (Energy)}
& 电池最大容量 ($b_{\max}$) & 10 J \\
& 初始能量范围 (Initial Energy) & $[0.2, 0.8] \times b_{\max}$ \\
& 环境收集模型 (Env. EH Model) & 4-state Markov \cite{Ku2015} \\
& 平均收集功率 (Avg. EH Power) (mW) & [10, 5, 2, 0.5] \cite{Ku2015} \\
& RF 收集参数 ($\mu, \nu, e_{\text{mp}}$) & 1500, 0.002, 0.024 W \cite{Boshkovska2015} \\
& RF 数据信号收集效率 ($\eta_{\text{data}}$) & 0.1 \\
& 能量传输功率 ($[e_{\min}, e_{\max}]$) & [10, 100] mW \\
& LTA 能量阈值 ($\delta_i$) & $0.3 b_{\max}$ \\
\hline
\multirow{2}{*}{数据到达 (Data Arrival)} 
& 到达概率 ($p_{\text{arrival}}$) & 0.2 \\
& 包大小分布 (Packet Size Dist.) & Exp(mean=10 Mbit) \\
\hline
\multirow{5}{*}{Lyapunov 参数 (Lyapunov Params)}
& 控制参数 ($V$) & 100 (可变) \\
& EC 阈值 ($\alpha$) & 0.3 \\
& EC 阈值 ($\beta'$) & 10.0 \\
& EC 阈值 ($\gamma$) & 0.4 \\
& EC 阈值 ($\zeta'$) & 10.0 \\
\hline
\multirow{2}{*}{仿真控制 (Simulation Control)} 
& 仿真总时长 ($T$) & 10000 时隙 \\
& 独立运行次数 (Runs) & 30 \\
\hline
\end{tabular}
}
\end{table}

\subsection{性能比较 (Performance Comparison)}
\label{subsec_perf_comp}
在本小节中，我们基于 \ref{subsubsec_metrics} 节定义的性能指标，对所提出的 Lyapunov-MEC 算法与 \ref{subsubsec_benchmarks_list} 节中定义的基准算法进行性能比较。我们首先分析 Lyapunov 控制参数 $V$ 对吞吐量和延迟的影响，然后重点比较不同能量协作模式下的性能差异。

\subsubsection{Lyapunov 参数 V 的影响 (Impact of Lyapunov Parameter V)}
\label{subsubsec_impact_V}
Lyapunov 优化框架中的控制参数 $V$ 调节了吞吐量最大化与队列稳定性之间的权衡。图~\ref{fig_throughput_vs_V} 展示了不同算法的长期平均吞吐量随参数 $V$ 变化的曲线。

从图~\ref{fig_throughput_vs_V} 中可以观察到，对于所有基于 Lyapunov 优化的算法（Lyapunov-MEC、Lyapunov-NoEC 和 Lyapunov-UEC），LTA 吞吐量均表现出随控制参数 $V$ 增大而改善并最终趋于饱和的趋势。具体而言，Lyapunov-MEC 的吞吐量从 $V=0$ 时的约 32.1 Mbps 迅速提升至 $V=2$ 时的约 33 Mbps，随后在 $V \ge 2$ 的范围内稳定在 33 Mbps 左右的最高水平。Lyapunov-NoEC 的吞吐量从 $V=0$ 时的约 30.1 Mbps 增长至 $V=2$ 时的约 30.8 Mbps，之后在 30.5 Mbps 至 31 Mbps 之间小幅波动。类似地，采用新启发式单播逻辑的 Lyapunov-UEC 算法，其吞吐量从 $V=0$ 时的约 28.3 Mbps 缓慢增加，在 $V=4$ 附近达到约 28.8 Mbps 的峰值后亦趋于稳定。这一普遍现象与引理~\ref{lemma_throughput_bound} 的理论分析相符，即增大 $V$ 值会促使算法更加侧重于优化瞬时吞吐量，系统会倾向于容忍更大的队列积压以换取更高的传输效率，并且通常不需要极大的 $V$ 值即可接近各算法自身的吞吐量潜力上限。

在所有测试的 $V$ 值下，Lyapunov-MEC 的吞吐量始终显著高于 Lyapunov-NoEC 和 Lyapunov-UEC，这清晰地证明了多播能量协作 (M-EC) 在提升系统数据传输能力方面的固有优势。在控制参数 $V$ 的整个变化范围内，Lyapunov-NoEC 的性能始终优于新的启发式 Lyapunov-UEC，表明在此参数维度下，完全不进行能量协作的策略反而比当前设计的单播启发式策略能获得更高的吞吐量。所有 Lyapunov 系列算法的置信区间均相对较窄，表明其性能表现具有较好的一致性。相比之下，不基于 Lyapunov 优化的 GMW 和 EAG 算法的吞吐量（分别稳定在约 28.1 Mbps 和 28.3 Mbps）对 $V$ 不敏感（因其不采用此控制参数），且其性能显著低于 Lyapunov-MEC 和 Lyapunov-NoEC，这主要归因于它们缺乏对队列稳定性和长期能量约束的系统性管理机制。RAND 算法的吞吐量最低（约 20.5 Mbps），符合其作为随机调度基准的预期。


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig_parallel/par_sweep_throughput_vs_V_CONTROL.png}
    \caption{LTA 吞吐量随 Lyapunov 控制参数 V 的变化。}
    \label{fig_throughput_vs_V}
\end{figure}

\subsubsection{能量协作模式对比 (Comparison of Energy Cooperation Modes)}
\label{subsubsec_ec_mode_comparison}

为了更深入地阐明能量协作机制对网络整体性能的贡献，我们在默认参数配置（例如 $V=100$）下，详细考察了 Lyapunov-MEC (采用多播能量协作 M-EC) 与 Lyapunov-NoEC (完全不进行能量协作) 在关键性能指标上的动态演化及稳态表现。图~\ref{fig_comparison_mec_uec_noec} 直观地展示了这两种策略以及其他基准算法（GMW, EAG, RAND）在能量稳定性、数据队列积压和瞬时吞吐量等方面的对比情况。Lyapunov-UEC (采用单播能量协作 U-EC) 的长期平均吞吐量性能的详细参数影响分析见第~\ref{subsec_param_impact}节。

从图~\ref{fig_comparison_mec_uec_noec} 中可以清晰地观察到以下几点：
\begin{itemize}
    \item \textbf{能量稳定性与维持:} 如图~\ref{fig_comparison_mec_uec_noec} (左上和左下) 所示，能量协作是维持网络能量平衡的核心要素。Lyapunov-NoEC 策略下的平均能量赤字队列随仿真时间推移呈现无界增长趋势，这明确指示了其无法满足预设的长期平均能量维持约束。与此形成鲜明对比的是，Lyapunov-MEC 凭借其高效的多播能量协作机制，成功地将能量赤字队列稳定控制在接近零的水平。Lyapunov-NoEC 的平均电池电量反而稳定在一个相对较高的水平，甚至超过了 Lyapunov-MEC；然而，这主要是其整体数据传输活动较低（从而导致能量消耗减少）的结果，而非有效能量管理的体现。Lyapunov-MEC 的电池电量在仿真后期略有下降，反映了能量被积极地用于支持更高的数据传输速率和必要的能量协作活动，同时依然能够成功地将平均电池能量维持在预定义的阈值 $\delta_i$ 之上。相较之下，GMW、EAG 和 RAND 等不具备复杂能量管理能力的算法，其节点电池电量迅速饱和至接近 $b_{max}$，这进一步佐证了它们较低的能量消耗率，而这通常与较低的网络吞吐量性能相伴。
    \item \textbf{数据队列与拥塞控制:} 如图~\ref{fig_comparison_mec_uec_noec} (右上) 所示，能量协作机制对于缓解网络拥塞具有显著效果。Lyapunov-MEC 策略实现了最低且最为稳定的平均网络数据队列长度，这直接反映了其在数据传输效率和拥塞控制方面的卓越能力。Lyapunov-NoEC 策略在仿真初期，当网络中节点普遍能量较为充足时，其数据队列长度与 Lyapunov-MEC 策略相近；然而，随着仿真的持续进行，由于缺乏有效的能量补充，关键中继节点的能量逐渐耗尽，导致其队列长度迅速增长，并最终稳定在与 GMW 和 EAG 等传统算法相似的较高水平。这一现象清晰地揭示了，尽管 Lyapunov-NoEC 在初始阶段可能表现尚可，但能量协作的缺失使其难以维持长期的低拥塞运行状态。GMW、EAG 和 RAND 算法的持续高队列长度则普遍反映了它们在调度决策和整体资源利用效率方面的固有局限性。
    \item \textbf{瞬时吞吐量:} 如图~\ref{fig_comparison_mec_uec_noec} (右下) 所示，Lyapunov-MEC 策略实现了最高且相对最为平稳的平均瞬时吞吐量，显著优于所有其他对比算法。Lyapunov-NoEC 策略的瞬时吞吐量在仿真初始阶段能够接近 Lyapunov-MEC 的水平，但随着节点能量的消耗而快速下降，并最终稳定在一个远低于 Lyapunov-MEC 的水平（尽管仍略高于 GMW 和 EAG）。这再次有力地证明了能量协作对于维持长期、高效数据传输的关键作用。GMW 和 EAG 的瞬时吞吐量较低，而 RAND 算法的性能最低，这符合其作为随机调度基准的预期。
\end{itemize}
综上所述，这些对比结果从多个维度定量地展示了能量协作——特别是 Lyapunov-MEC 算法中所采用的多播能量协作——在改善网络性能方面的多重且显著的优势。能量协作不仅是保障网络长期能量稳定性的核心机制，还能有效缓解数据拥塞，并最终大幅提升系统整体的吞吐量。Lyapunov-MEC 算法凭借其对能量协作和数据传输的智能联合调度策略，在所有被评估的关键性能指标上均展现出最优的性能。


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig/fig_comparison_mec_noec_GMW_EAG_RAND.png}
    \caption{Lyapunov-MEC (M-EC), Lyapunov-NoEC (No-EC), GMW, EAG, 与 RAND 算法性能对比：(左上) 平均电池电量，(右上) 总网络队列大小，(左下) 平均能量赤字队列，(右下) 平均瞬时吞吐量 (100 时隙平滑)。}
    \label{fig_comparison_mec_uec_noec}
\end{figure}


\subsection{参数影响分析 (Parameter Impact Analysis)}
\label{subsec_param_impact}
在本小节中，我们进一步研究关键系统参数对所提出的 Lyapunov-MEC 算法及相关基准算法性能的影响。我们主要关注长期平均吞吐量指标，以评估算法在不同网络条件下的鲁棒性和适应性。

\subsubsection{电池容量的影响 (Impact of Battery Capacity)}
\label{subsubsec_impact_battery}
图~\ref{fig_throughput_vs_battery} 中展示了LTA吞吐量随节点电池最大容量 $b_{\max}$ 变化的趋势，其中 $b_{\max}$ 的取值范围为 1 J 至 25 J。分析此图可以得到以下关键观察：

Lyapunov-MEC 算法的LTA吞吐量在整个测试的电池容量范围内均表现出卓越且高度稳定的性能。其吞吐量从 $b_{\max}=1$ J 时的约 32.6 Mbps 微升至 $b_{\max}=5$ J 时的约 32.9 Mbps 后，便几乎不再随电池容量的进一步增加而变化，始终维持在 32.9 Mbps 左右的水平。这一现象充分证明了所提出的多播能量协作 (M-EC) 机制的鲁棒性和高效性，表明即使在节点个体储能能力极为有限的情况下，Lyapunov-MEC 依然能够通过有效的能量共享策略维持接近最优的高吞吐量。

与 Lyapunov-MEC 形成对比，Lyapunov-NoEC 和新的启发式 Lyapunov-UEC 算法的吞吐量均显著受益于电池容量的增加。Lyapunov-NoEC 的性能从 $b_{\max}=1$ J 时的约 28.2 Mbps 稳步提升至 $b_{\max}=25$ J 时的约 32.5 Mbps。类似地，Lyapunov-UEC 的吞吐量也从 $b_{\max}=1$ J 时的约 26.4 Mbps 增长至 $b_{\max}=25$ J 时的约 31.0 Mbps。这两种算法性能的提升趋势清晰地表明，在能量协作能力受限（UEC的单播模式）或缺失（NoEC）时，节点自身的储能能力成为影响网络吞吐量的关键因素；更大的电池容量能够更好地缓冲环境能量收集的随机性，从而支持更持续的数据传输。

进一步比较这三种基于Lyapunov的策略：在电池容量极小（例如 $b_{\max}=1$ J）时，Lyapunov-MEC 的性能优势最为突出，其吞吐量远高于Lyapunov-NoEC（高出约 4.4 Mbps）和Lyapunov-UEC（高出约 6.2 Mbps），这凸显了在极端能量受限条件下M-EC的关键价值。随着电池容量的增加，Lyapunov-NoEC 和 Lyapunov-UEC 的性能逐渐提升并缩小与Lyapunov-MEC的差距。然而，即使在 $b_{\max}=25$ J 时，Lyapunov-MEC 依然保持着最高的吞吐量。当电池容量较大时（例如 $b_{\max} \ge 15$ J），Lyapunov-NoEC 的性能略微超过了Lyapunov-UEC。这可能暗示，当节点自身储能较为充裕时，当前启发式UEC算法所采用的单播能量传输带来的增益，可能不足以弥补其潜在的能量传输开销或机会成本，使得节点独立工作的NoEC策略反而表现更佳。

    对于GMW和EAG算法，它们的吞吐量（约28.1-28.4 Mbps）以及RAND算法的吞吐量（约20.5 Mbps）基本不受电池容量变化的影响，且显著低于所有Lyapunov优化算法，这符合它们的设计特性，即未充分考虑动态的能量状态和协作机会。

综上所述，这些结果清晰地表明，Lyapunov-MEC 中的多播能量协作机制不仅实现了最高的LTA吞吐量，而且极大地增强了网络对电池容量变化的适应能力。缺乏高效能量协作的系统则严重依赖于更大的电池容量来提升性能，并且即便如此，其性能上限也难以企及实现了高效能量共享的Lyapunov-MEC算法。


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig_parallel/par_sweep_throughput_vs_BATTERY_MAX_J.png}
    \caption{LTA 吞吐量随电池最大容量 $b_{max}$ 的变化。}
    \label{fig_throughput_vs_battery}
\end{figure}

\subsubsection{节点数量的影响 (Impact of Number of Nodes)}
\label{subsubsec_impact_nodes}
图~\ref{fig_throughput_vs_nodes} 研究了网络中传感器节点数量 $N$ 对 LTA 吞吐量的影响。在此项研究中，$N$ 的取值范围从10变化至50，步长为10。所有节点均部署于一个固定的 100m $\times$ 100m 的正方形区域内，这意味着随着 $N$ 的增加，网络的节点密度亦相应增大。节点数量的增加通常伴随着网络总数据生成潜力的提升（假设单个节点的平均数据到达率维持不变），同时也可能引入更丰富的路由选择和能量协作机会。

图~\ref{fig_throughput_vs_nodes} 展示了在此设定下，各算法的LTA吞吐量随传感器节点数量 $N$ （从20至50）变化的具体趋势。我们观察到以下主要趋势：

Lyapunov-MEC 算法展现出最优的性能和良好的可扩展性。其LTA吞吐量随着节点数量 $N$ 的增加而持续提升，从 $N=20$ 时的约 32.9 Mbps 稳步增长至 $N=50$ 时的约 36.5 Mbps。这表明Lyapunov-MEC能够有效利用网络规模扩大所带来的更丰富的路由多样性和能量协作机会，从而支持更高的网络负载并提升整体数据传输能力。

采用新启发式逻辑的Lyapunov-UEC算法同样显示出显著的性能提升趋势。其吞吐量从 $N=20$ 时的约 28.5 Mbps 大幅增长至 $N=50$ 时的约 35.6 Mbps。随着节点数量的增加，Lyapunov-UEC的性能逐渐逼近Lyapunov-MEC，在 $N=50$ 时两者差距已较小。这可能归因于在节点更密集的网络中，UEC算法更容易找到合适的单播能量共享对，从而提高了其能量协作的有效性。

相比之下，Lyapunov-NoEC算法的性能表现出不同的趋势。其吞吐量从 $N=20$ 时的约 30.4 Mbps 增加到 $N=40$ 时的约 33.9 Mbps（峰值），但当节点数量进一步增加到 $N=50$ 时，其吞吐量反而下降至约 33.3 Mbps。这揭示了在缺乏能量协作机制的情况下，尽管节点数量的增加初期能通过增加潜在路由路径带来一定的性能增益，但随着网络规模的持续扩大，能量耗尽的瓶颈问题（尤其是在关键中继节点）会愈发突出，从而限制了其可扩展性。在节点数量较多时，网络复杂性的增加以及更长的潜在路由可能反而加剧了能量瓶颈效应，导致性能下降。因此，在节点数量较多（例如 $N \ge 40$）的场景下，Lyapunov-UEC的性能优于Lyapunov-NoEC。

GMW和EAG算法的吞吐量在 $N$ 从20增加到40时有所上升（分别达到约 32.6 Mbps和32.3 Mbps的峰值），但在 $N=50$ 时均表现出明显的性能下降，进一步凸显了它们在较大规模网络中扩展能力的局限性。RAND算法的吞吐量随 $N$ 的增加而缓慢提升，从 $N=20$ 时的约 20.6 Mbps 增长到 $N=50$ 时的约 23.2 Mbps，始终处于最低水平。

这些结果综合表明，所提出的Lyapunov-MEC算法不仅在各种网络规模下均能保持性能优势，而且其良好的可扩展性也证明了多播能量协作在支持大规模EH-WSN高效运行方面的重要性。同时，启发式的单播能量协作（Lyapunov-UEC）在节点密度较高时也能提供有竞争力的性能，并优于无能量协作的方案。


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig_parallel/par_sweep_throughput_vs_NUM_NODES.png}
    \caption{LTA 吞吐量随网络节点数量 N 的变化。}
    \label{fig_throughput_vs_nodes}
\end{figure}

\subsubsection{统计显著性分析 (Statistical Significance Analysis)}
\label{subsubsec_anova}
为了对不同能量协作策略的性能进行严格的统计评估，我们针对在特定参数配置（即网络规模 $N=20$, 汇聚节点数 $K=2$, Lyapunov 控制参数 $V=100$, 以及最大电池容量 $b_{\max}=10$ J）下获得的长期平均吞吐量数据，执行了单因素方差分析 (ANOVA)，比较了 Lyapunov-MEC (M-EC)、Lyapunov-UEC (U-EC, 采用新的启发式单播逻辑) 及 Lyapunov-NoEC (No-EC) 三种算法。所有统计检验的显著性水平均设定为 $\alpha = 0.05$。

ANOVA 结果（详见表~\ref{tbl_anova_analysis}）揭示了所比较的三种算法在LTA吞吐量方面存在高度显著的统计学差异 (F-statistic = 70.0765, $p$-value = $7.4 \times 10^{-19}$)。鉴于 $p$-value 远低于预设的显著性水平 $\alpha$，我们拒绝了各算法平均吞吐量相等的零假设。

为进一步探究各算法对之间的具体差异，我们实施了 Tukey HSD (Honestly Significant Difference) 事后检验。该检验结果同样汇总于表~\ref{tbl_anova_analysis}，其主要发现如下：
\begin{itemize}
    \item Lyapunov-MEC 算法的平均LTA吞吐量显著优于 Lyapunov-NoEC 算法 (平均差值约 2.35 Mbps, $p < 0.001$)。
    \item Lyapunov-MEC 算法的平均LTA吞吐量同样显著优于采用新启发式逻辑的 Lyapunov-UEC 算法 (平均差值约 4.02 Mbps, $p < 0.001$)。
    \item Lyapunov-NoEC 算法的平均LTA吞吐量亦显著优于新启发式 Lyapunov-UEC 算法 (平均差值约 1.67 Mbps, $p < 0.001$)。
\end{itemize}
这些统计分析结果为以下结论提供了强有力的证据：在所考察的参数条件下 ($b_{\max}=10$ J)，Lyapunov-MEC 凭借其多播能量协作机制，在LTA吞吐量方面表现出最优性能。其次是无能量协作的 Lyapunov-NoEC 方案。而经过启发式逻辑修改后的 Lyapunov-UEC 方案，在此特定配置下的性能表现相对最弱。此性能排序与先前基于早期 UEC 版本所作的初步分析存在差异，这凸显了能量协作策略的具体设计对算法整体性能的关键性影响，并进一步证实了 M-EC 策略在提升网络吞吐量方面的固有优势。


\begin{table}[htbp]	
\centering
\caption{LTA 吞吐量的 ANOVA 检验与 Tukey HSD 事后分析 (ANOVA Test and Tukey HSD Post-Hoc Analysis for LTA Throughput)。MD 表示平均差值 (Mean Difference)；Lb 和 Ub 分别表示该差值95\%置信区间的下界 (Lower Bound) 和上界 (Upper Bound)。}
\label{tbl_anova_analysis}
\resizebox{0.48\textwidth}{!}{
    \begin{tabular}{|c|c|c|c|c|c|c|}
        \hline
        \multicolumn{3}{|c|}{\textbf{ANOVA 检验 (ANOVA Test)}} & \multicolumn{4}{c|}{\textbf{Tukey HSD 事后分析 (Tukey HSD Post-Hoc Analysis)}} \\
        \hline
        F-statistic  & $p$-value & F-critical & 对比算法 (Comparison) & Lb & MD & Ub \\
        \hline
        \multirow{3}{*}{70.0765} & \multirow{3}{*}{$7.4 \times 10^{-19}$} & \multirow{3}{*}{3.1013} & MEC vs. UEC & 3.2088 & 4.0228 & 4.8369  \\
        \cline{4-7}
        & & & MEC vs. NoEC & 1.5356 & 2.3497 & 3.1637  \\
        \cline{4-7}
        & & & NoEC vs. UEC & 0.8591 & 1.6732 & 2.4872  \\
        \hline
    \end{tabular}
}
\end{table}


\section{结论}
\label{sec_conclusion}
本文研究了能量收集无线传感器网络中能量协作辅助的数据收集问题，目标是最大化网络的长期平均吞吐量，同时保证数据队列稳定性和满足节点的长期平均能量维持需求。我们将此问题建模为一个随机网络优化问题，并利用 Lyapunov 优化理论将其转化为一系列逐时隙的确定性优化问题。

为了解决逐时隙优化问题中存在的计算复杂性，我们提出了一种低复杂度的在线启发式算法 Lyapunov-MEC。该算法通过将决策过程分解为能量协作和数据传输两个阶段，并采用基于队列状态和能量状态的贪婪策略，在满足瞬时约束的同时，近似最大化 Lyapunov 漂移加惩罚目标。理论分析表明，理想的 Lyapunov 控制策略能够保证队列稳定性和 LTA 能量约束满足，并实现接近最优的吞吐量性能，其性能与最优值之间的差距由控制参数 $V$ 控制。

仿真结果验证了 Lyapunov-MEC 算法的有效性。与禁用能量协作的 Lyapunov-NoEC 算法以及其他基准算法（如 GMW、EAG）相比，Lyapunov-MEC 在长期平均吞吐量方面表现出显著优势，尤其是在网络负载较高或节点能量受限的情况下。仿真结果也清晰地展示了 Lyapunov 优化中吞吐量与延迟之间的权衡关系，以及能量协作对提升系统性能的关键作用。此外，参数影响分析表明，Lyapunov-MEC 算法对电池容量和网络规模等关键参数具有良好的适应性和可扩展性。

未来的工作可以探索更优化的功率控制策略，以进一步改善当前算法中采用的基于满足最低SNR阈值的动态功率调整策略，从而可能进一步提升性能。

\bibliographystyle{IEEEtran}
\bibliography{mybibfile}
	
\end{document}
