# visualize_simulation.py
# Script to visualize the WSN simulation from simulation.py in real-time.

import simulation as sim
import comparison_algorithms as comp_alg # Import comparison algorithms
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import matplotlib.patches as patches # Needed for potential text backgrounds later
import numpy as np
import time

# --- Visualization Parameters ---
NODE_SIZE = 100
SINK_SIZE = 150
NODE_COLOR = '#3498db' # Brighter blue
SINK_COLOR = '#34495e' # Darker grey/blue
LINK_COLOR = '#bdc3c7' # Light grey
LINK_LINEWIDTH = 0.4 # Slightly thinner
ARROW_WIDTH = 0.01 # Base width
EC_ARROW_WIDTH = 0.02 # Make EC arrows thicker
ARROW_HEAD_WIDTH = 0.2
ARROW_HEAD_LENGTH = 0.3
DATA_ARROW_COLOR = '#e67e22' # Orange for data flow
EC_ARROW_COLOR = '#2ecc71' # Emerald green for EC

# Colors for battery levels (Node Edge Colors)
BATTERY_EDGE_COLOR_HIGH = '#27ae60' # Darker Green
BATTERY_EDGE_COLOR_MEDIUM = '#f39c12' # Orange/Yellow
BATTERY_EDGE_COLOR_LOW = '#c0392b' # Darker Red
BATTERY_HIGH_THRESHOLD = 0.7 * sim.BATTERY_MAX_J
BATTERY_LOW_THRESHOLD = 0.3 * sim.BATTERY_MAX_J
NODE_EDGE_WIDTH = 1.5 # Make edge visible

# --- Matplotlib Setup ---
plt.style.use('seaborn-darkgrid') # Apply a style
fig, ax = plt.subplots(figsize=(10, 10))
network_lines = [] # To store plotted link lines
node_scatter = None
sink_scatter = None
node_texts = {} # To store text annotations (ID, Queue Size, Deficit)
# battery_indicators = {} # No longer needed, using edge color
dynamic_arrows = [] # To store arrows for data/energy flow
dynamic_texts = [] # To store dynamic text annotations (like energy values)
ec_log = [] # Initialize list to log energy cooperation events globally

# --- Helper Functions ---

def get_battery_edge_color(energy_level):
    """Determines the node edge color based on battery level."""
    if energy_level >= BATTERY_HIGH_THRESHOLD:
        return BATTERY_EDGE_COLOR_HIGH
    elif energy_level >= BATTERY_LOW_THRESHOLD:
        return BATTERY_EDGE_COLOR_MEDIUM
    else:
        return BATTERY_EDGE_COLOR_LOW

def draw_initial_network(nodes, sinks, potential_links):
    """Draws the static network elements."""
    global node_scatter, sink_scatter, network_lines, node_texts # Removed battery_indicators
    ax.clear()
    # Use style's background color directly from rcParams after style is applied
    current_facecolor = plt.rcParams['axes.facecolor']
    fig.patch.set_facecolor(current_facecolor)
    ax.set_facecolor(current_facecolor)
    ax.set_xlim(-10, sim.AREA_WIDTH + 10)
    ax.set_ylim(-10, sim.AREA_HEIGHT + 10)
    ax.set_aspect('equal', adjustable='box')
    ax.set_title("Wireless Sensor Network Simulation")
    ax.set_xlabel("X Coordinate (m)")
    ax.set_ylabel("Y Coordinate (m)")

    # Draw potential links (thin blue lines)
    network_lines = []
    drawn_links = set()
    for sender_id, receivers in potential_links.items():
        sender = sim.get_device_by_id(sender_id, nodes, sinks)
        if not sender: continue
        for receiver_id in receivers:
            receiver = sim.get_device_by_id(receiver_id, nodes, sinks)
            if not receiver: continue
            # Avoid drawing links twice
            link_pair = tuple(sorted((sender_id, receiver_id)))
            if link_pair not in drawn_links:
                line, = ax.plot([sender['pos'][0], receiver['pos'][0]],
                                [sender['pos'][1], receiver['pos'][1]],
                                color=LINK_COLOR, linewidth=LINK_LINEWIDTH, linestyle='-', zorder=1)
                network_lines.append(line)
                drawn_links.add(link_pair)

    # Draw nodes - Initial drawing with default edge color, will be updated in animation loop
    node_pos = np.array([n['pos'] for n in nodes])
    node_edge_colors = [get_battery_edge_color(n['battery']) for n in nodes] # Initial edge colors
    if len(node_pos) > 0:
        node_scatter = ax.scatter(node_pos[:, 0], node_pos[:, 1], s=NODE_SIZE, c=NODE_COLOR,
                                  edgecolors=node_edge_colors, linewidths=NODE_EDGE_WIDTH, # Use edge colors
                                  zorder=2, label='Sensor Node')
        # Add node IDs and initialize text
        for node in nodes:
            node_id = node['id']
            x, y = node['pos']
            # Initialize text with background
            node_texts[node_id] = ax.text(x, y + 4, f"ID: {node_id}\nQ: 0.00Mb\nB: 0.00\nE: 0.00J", fontsize=7, ha='center', va='bottom', zorder=5, # Increase zorder
                                          bbox=dict(boxstyle='round,pad=0.1', fc='white', alpha=0.6, ec='none')) # Add background
            # No need to initialize battery_indicators anymore

    # Draw sinks
    sink_pos = np.array([s['pos'] for s in sinks])
    if len(sink_pos) > 0:
        sink_scatter = ax.scatter(sink_pos[:, 0], sink_pos[:, 1], s=SINK_SIZE, c=SINK_COLOR, marker='s',
                                  edgecolors='white', linewidths=0.5, # Thinner edge for sinks
                                  zorder=2, label='Sink Node')
        # Add sink IDs - adjust color based on the determined facecolor
        # Simple check: if facecolor is light, use black text, otherwise white.
        # This might need refinement for mid-tones, but works for typical dark/light styles.
        text_color = 'black' if np.mean(plt.matplotlib.colors.to_rgb(current_facecolor)) > 0.5 else 'white'
        for sink in sinks:
            ax.text(sink['pos'][0], sink['pos'][1] + 4, f"Sink {sink['id']}", fontsize=7, ha='center', va='bottom', color=text_color, zorder=3)

    ax.legend(loc='upper right', fontsize='small', frameon=True, facecolor='white', framealpha=0.7) # Improved legend

def update_visualization(frame, nodes, sinks, actions, energy_harvested_rf, current_slot, total_delivered_bits, algorithm_name):
    """Updates the visualization for the current time slot."""
    global dynamic_arrows, dynamic_texts # Add dynamic_texts

    # --- Clear previous dynamic elements ---
    for arrow in dynamic_arrows:
        arrow.remove()
    dynamic_arrows.clear()
    for text in dynamic_texts: # Clear previous energy texts
        text.remove()
    dynamic_texts.clear()


    # --- Update Node States (Battery Edge Color and Queue Text) ---
    new_edge_colors = []
    node_id_list = [n['id'] for n in nodes] # Get list of node IDs in current order
    for node in nodes:
        node_id = node['id']
        # Append new edge color for this node
        new_edge_colors.append(get_battery_edge_color(node['battery']))
        # Update queue text
        if node_id in node_texts: # Corrected indentation
              # Use queue_bits for total size, not the list of packet IDs in queues
              total_queue_mbit = sum(node['queue_bits'].values()) / 1e6
              deficit_queue = node['energy_deficit_queue']
              battery_level = node['battery'] # Get current battery level
              node_texts[node_id].set_text(f"ID: {node_id}\nQ: {total_queue_mbit:.2f}Mb\nB: {deficit_queue:.2f}\nE: {battery_level:.2f}J") # Add battery level display
              # Update background properties if needed (though usually text update is enough)
              # node_texts[node_id].set_bbox(dict(boxstyle='round,pad=0.1', fc='white', alpha=0.6, ec='none'))

    # Update scatter plot edge colors if nodes exist
    if node_scatter:
        node_scatter.set_edgecolors(new_edge_colors)

    # --- Draw Dynamic Arrows based on Actions ---
    y_t = actions.get('y', {})
    e_hat_t = actions.get('e_hat', {})
    f_t = actions.get('f', {})
    p_t = actions.get('p', {}) # Needed to know which links transmitted data for RF harvesting

    # 1. Data Flow Arrows (Orange Solid)
    # Note: flow_bits here is the list of packet IDs decided in simulation.py for Lyapunov
    # For comparison algorithms, it's the float value of bits. Visualization only checks if it exists.
    for (i, j, k), flow_info in f_t.items():
        # Check if flow exists (non-empty list for Lyapunov, non-zero float for others)
        if flow_info: # This works for both non-empty lists and non-zero numbers
            sender = sim.get_device_by_id(i, nodes, sinks)
            receiver = sim.get_device_by_id(j, nodes, sinks)
            if sender and receiver:
                dx = receiver['pos'][0] - sender['pos'][0]
                dy = receiver['pos'][1] - sender['pos'][1]
                # Calculate a small perpendicular offset
                dist = np.sqrt(dx**2 + dy**2)
                offset_scale = 0.5 # Adjust this value to control separation
                offset_x = -dy / dist * offset_scale if dist > 0 else 0
                offset_y = dx / dist * offset_scale if dist > 0 else 0
                # Apply offset to start and end points for data arrow
                start_x = sender['pos'][0] + offset_x
                start_y = sender['pos'][1] + offset_y
                arrow_dx = dx * 0.8 - offset_x # Adjust arrow vector for offset start
                arrow_dy = dy * 0.8 - offset_y
                arrow = ax.arrow(start_x, start_y, arrow_dx, arrow_dy, # Use offset start and adjusted vector
                                 color=DATA_ARROW_COLOR, width=ARROW_WIDTH,
                                 head_width=ARROW_HEAD_WIDTH, head_length=ARROW_HEAD_LENGTH,
                                 length_includes_head=True, zorder=4)
                dynamic_arrows.append(arrow)

    # 2. Energy Cooperation Arrows (Green Dashed, Thicker) - Apply opposite offset
    # This part will only draw arrows if the selected algorithm actually performs EC (e.g., Lyapunov-MEC)
    for node_id, is_transmitting in y_t.items():
        if is_transmitting == 1 and e_hat_t.get(node_id, 0) > 1e-9: # Check for non-negligible energy
            sender = sim.get_device_by_id(node_id, nodes, sinks)
            if not sender: continue
            # Find potential receivers (neighbors not transmitting energy)
            if node_id in sim.potential_links:
                 for neighbor_id in sim.potential_links[node_id]:
                     receiver = sim.get_device_by_id(neighbor_id, nodes, sinks)
                     # Only draw to nodes that *could* receive (y_j=0)
                     if receiver and not receiver['is_sink'] and y_t.get(neighbor_id, 0) == 0:
                         dx = receiver['pos'][0] - sender['pos'][0]
                         dy = receiver['pos'][1] - sender['pos'][1]
                         # Calculate a small perpendicular offset (opposite direction to data)
                         dist = np.sqrt(dx**2 + dy**2)
                         offset_scale = 0.5 # Adjust this value to control separation (same as data)
                         offset_x = dy / dist * offset_scale if dist > 0 else 0 # Note the sign change for opposite offset
                         offset_y = -dx / dist * offset_scale if dist > 0 else 0
                         # Apply offset to start and end points for EC arrow
                         start_x = sender['pos'][0] + offset_x
                         start_y = sender['pos'][1] + offset_y
                         arrow_dx = dx * 0.8 - offset_x # Adjust arrow vector for offset start
                         arrow_dy = dy * 0.8 - offset_y
                         arrow = ax.arrow(start_x, start_y, arrow_dx, arrow_dy, # Use offset start and adjusted vector
                                          color=EC_ARROW_COLOR, linestyle='--', width=EC_ARROW_WIDTH,
                                          head_width=ARROW_HEAD_WIDTH, head_length=ARROW_HEAD_LENGTH,
                                          length_includes_head=True, zorder=4)
                         dynamic_arrows.append(arrow)
                         # Add text for energy value near the middle of the *offset* arrow
                         energy_val = e_hat_t.get(node_id, 0)
                         text_x = start_x + arrow_dx * 0.5 # Position text relative to offset arrow
                         text_y = start_y + arrow_dy * 0.5 + 1 # Offset slightly vertically
                         ec_text = ax.text(text_x, text_y, f"{energy_val*1000:.1f}mW", fontsize=7, color=EC_ARROW_COLOR, ha='center', va='bottom', zorder=5,
                                           bbox=dict(boxstyle='round,pad=0.1', fc='white', alpha=0.6, ec='none'))
                         dynamic_texts.append(ec_text)
                         # Draw only one arrow and text per sender-receiver pair for EC
                         # (The loop structure already handles this implicitly for EC)

    # 3. RF Harvesting Arrows (Red Dash-Dotted) - Temporarily Disabled (as before)
    # ... (code remains the same)

    # --- Update Title ---
    # Include the algorithm name in the title
    ax.set_title(f"WSN Sim [{algorithm_name.upper()}] - Slot: {current_slot+1}/{sim.TOTAL_TIME_SLOTS} - Delivered: {total_delivered_bits/1e6:.2f} Mbit")

    # Return all dynamic artists that need updating/clearing
    # Need to return node_scatter as well since its edge colors change
    artists = list(node_texts.values()) + dynamic_arrows + dynamic_texts
    if node_scatter:
        artists.append(node_scatter)
    # Removed battery_indicators
    return artists


# --- Simulation and Animation ---

def run_simulation_step_by_step():
    """Generator function to run the simulation step-by-step."""
    global nodes, sinks, all_devices, potential_links, avg_channel_gains # Allow modification

    # --- 选择要可视化的算法 ---
    # 可选项: "lyapunov_mec", "gmw", "eag", "rand"
    ALGORITHM_TO_VISUALIZE = "lyapunov_mec" # <---- 在这里修改为你想要可视化的算法名
    print(f"--- Running Visualization for Algorithm: {ALGORITHM_TO_VISUALIZE.upper()} ---")

    # Initialize Network (using sim.py's function)
    sim.initialize_network()
    nodes = sim.nodes # Get initialized nodes
    sinks = sim.sinks # Get initialized sinks
    potential_links = sim.potential_links # Get potential links
    avg_channel_gains = sim.avg_channel_gains # Get avg gains

    # Draw the initial network layout
    draw_initial_network(nodes, sinks, potential_links)

    total_delivered_data_bits = 0.0
    global ec_log # Declare usage of global variable
    ec_log.clear() # Clear log for new simulation run

    for t in range(sim.TOTAL_TIME_SLOTS):
        # 1. Get current instantaneous channel conditions
        current_channel_gains = {}
        for (sender_id, receiver_id), avg_gain in avg_channel_gains.items():
            if avg_gain > 0:
                inst_gain = sim.get_rician_channel_gain(avg_gain)
                current_channel_gains[(sender_id, receiver_id)] = inst_gain
            else:
                current_channel_gains[(sender_id, receiver_id)] = 0

        # 2. Run the selected scheduling algorithm
        if ALGORITHM_TO_VISUALIZE == "lyapunov_mec":
            actions = sim.lyapunov_mec_scheduling(t, nodes, sinks, potential_links, current_channel_gains)
        elif ALGORITHM_TO_VISUALIZE == "gmw":
            actions = comp_alg.gmw_scheduling(t, nodes, sinks, potential_links, current_channel_gains,
                                              sim.NOISE_POWER_W, sim.BANDWIDTH) # Pass constants from sim (Corrected: BANDWIDTH)
        elif ALGORITHM_TO_VISUALIZE == "eag":
            actions = comp_alg.eag_scheduling(t, nodes, sinks, potential_links, current_channel_gains,
                                              sim.NOISE_POWER_W, sim.BANDWIDTH, sim.BATTERY_MAX_J) # Pass constants from sim (Corrected: BANDWIDTH)
        elif ALGORITHM_TO_VISUALIZE == "rand":
            actions = comp_alg.rand_scheduling(t, nodes, sinks, potential_links, current_channel_gains,
                                               sim.NOISE_POWER_W, sim.BANDWIDTH) # Pass constants from sim (Corrected: BANDWIDTH)
        else:
            raise ValueError(f"Unknown algorithm specified for visualization: {ALGORITHM_TO_VISUALIZE}")


        # --- Log Energy Cooperation Events (Only relevant if algorithm performs EC) ---
        y_t = actions.get('y', {})
        e_hat_t = actions.get('e_hat', {})
        if y_t and e_hat_t: # Only log if these keys exist and are not empty
            for sender_id, is_transmitting in y_t.items():
                if is_transmitting == 1:
                    power_mW = e_hat_t.get(sender_id, 0) * 1000
                    if power_mW > 0:
                        # Find potential receivers for logging purposes
                        receivers_list = []
                        if sender_id in potential_links:
                            for neighbor_id in potential_links[sender_id]:
                                receiver = sim.get_device_by_id(neighbor_id, nodes, sinks)
                                if receiver and not receiver['is_sink'] and y_t.get(neighbor_id, 0) == 0:
                                    receivers_list.append(neighbor_id)
                        ec_log.append({
                            'slot': t,
                            'sender': sender_id,
                            'power_mW': f"{power_mW:.1f}",
                            'potential_receivers': receivers_list
                        })

        # 3. Update node states and get harvested RF energy
        delivered_this_slot, energy_harvested_rf = sim.update_node_states(
            nodes, sinks, actions, current_channel_gains, t,
            sim.ARRIVAL_PROB, sim.PACKET_SIZE_MEAN_bits, sinks
        )

        total_delivered_data_bits += delivered_this_slot

        # Yield the current state, actions, harvested energy, and algorithm name for the animation frame
        yield t, nodes, sinks, actions, energy_harvested_rf, total_delivered_data_bits, ALGORITHM_TO_VISUALIZE

        # Optional: Add a small delay to control animation speed
        # time.sleep(0.05)

# --- Main Execution ---
if __name__ == "__main__":
    # Create the generator
    simulation_generator = run_simulation_step_by_step()

    # Define the animation function that consumes the generator
    def animation_frame(data):
        # Unpack the data yielded by the generator
        current_slot, current_nodes, current_sinks, current_actions, harvested_rf, current_delivered, algo_name = data
        # Update the visualization using the yielded data
        return update_visualization(current_slot, current_nodes, current_sinks, current_actions, harvested_rf, current_slot, current_delivered, algo_name)

    # Create the animation
    # Note: blit=True can improve performance but requires update functions to return all modified artists.
    # Switching back to blit=False as it's generally more reliable for text updates.
    ani = animation.FuncAnimation(fig, animation_frame, frames=simulation_generator,
                                  interval=50, # Delay between frames in ms (adjust for speed)
                                  repeat=False, blit=False, # Use blit=False for robust text updates
                                  save_count=sim.TOTAL_TIME_SLOTS) # Ensure enough frames are processed

    plt.show()

    print("Visualization finished.")
    # --- Print Energy Cooperation Log ---
    if ec_log:
        print("\n--- Energy Cooperation Log ---")
        for event in ec_log:
            print(f"Slot {event['slot']}: Node {event['sender']} -> {event['potential_receivers']} ({event['power_mW']} mW)")
    else:
        print("\n--- No Energy Cooperation events occurred during the simulation. ---")
