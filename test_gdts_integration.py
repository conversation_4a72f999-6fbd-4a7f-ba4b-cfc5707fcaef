#!/usr/bin/env python3
"""
Test script for GDTS integration with the simulation framework.
Runs a small simulation to verify GDTS works correctly within the framework.
"""

import sys
import numpy as np
import time

# Import simulation components
from simulation import run_single_simulation
from comparison_algorithms import gdts_scheduling, gdts_no_ec_scheduling

def test_gdts_simulation_integration():
    """Test GDTS integration with the simulation framework."""
    print("Testing GDTS integration with simulation framework...")

    # Temporarily modify simulation parameters for faster testing
    import simulation
    original_total_slots = simulation.TOTAL_TIME_SLOTS
    original_num_nodes = simulation.NUM_NODES
    original_num_sinks = simulation.NUM_SINKS

    # Set smaller parameters for testing
    simulation.TOTAL_TIME_SLOTS = 100  # Much smaller for testing
    simulation.NUM_NODES = 3  # Smaller network
    simulation.NUM_SINKS = 1  # Single sink

    try:
        # Test both GDTS variants
        algorithms_to_test = {
            "GDTS": gdts_scheduling,
            "GDTS-NoEC": gdts_no_ec_scheduling
        }

        for algo_name, scheduling_func in algorithms_to_test.items():
            print(f"\n  Testing {algo_name}...")

            try:
                    # Run a short simulation
                    start_time = time.time()
                    results = run_single_simulation(scheduling_func)
                    end_time = time.time()

                    # Verify results structure
                    required_keys = [
                        'final_avg_throughput_mbps', 'final_total_delivered_mbits',
                        'time_avg_battery', 'time_avg_queue_mbit', 'time_avg_energy_deficit',
                        'log_timeslot', 'log_avg_battery', 'log_total_queue_mbit',
                        'log_avg_energy_deficit', 'log_delivered_mbps'
                    ]

                    for key in required_keys:
                        assert key in results, f"Missing key '{key}' in {algo_name} results"

                    # Verify reasonable values
                    assert results['final_avg_throughput_mbps'] >= 0, f"Negative throughput in {algo_name}"
                    assert results['final_total_delivered_mbits'] >= 0, f"Negative delivered data in {algo_name}"
                    assert results['time_avg_battery'] >= 0, f"Negative battery in {algo_name}"
                    assert results['time_avg_queue_mbit'] >= 0, f"Negative queue size in {algo_name}"

                    # Print summary
                    print(f"    ✓ {algo_name} simulation completed successfully!")
                    print(f"      Runtime: {end_time - start_time:.2f}s")
                    print(f"      Avg Throughput: {results['final_avg_throughput_mbps']:.4f} Mbps")
                    print(f"      Total Delivered: {results['final_total_delivered_mbits']:.2f} Mbits")
                    print(f"      Avg Battery: {results['time_avg_battery']:.2f} J")
                    print(f"      Avg Queue: {results['time_avg_queue_mbit']:.2f} Mbits")

            except Exception as e:
                print(f"    ❌ {algo_name} simulation failed: {e}")
                import traceback
                traceback.print_exc()
                return False

        return True

    finally:
        # Restore original parameters
        simulation.TOTAL_TIME_SLOTS = original_total_slots
        simulation.NUM_NODES = original_num_nodes
        simulation.NUM_SINKS = original_num_sinks

def test_gdts_output_consistency():
    """Test that GDTS produces consistent output format with other algorithms."""
    print("\nTesting GDTS output format consistency...")
    
    # Import other algorithms for comparison
    from comparison_algorithms import gmw_scheduling, rand_scheduling
    
    # Create a simple test scenario
    from test_gdts import create_test_network
    nodes, sinks, potential_links, current_channel_gains = create_test_network()
    
    current_slot = 0
    noise_power_W = 1e-12
    bandwidth = 1e6
    
    algorithms = {
        "GDTS": gdts_scheduling,
        "GDTS-NoEC": gdts_no_ec_scheduling,
        "GMW": gmw_scheduling,
        "RAND": rand_scheduling
    }
    
    # Test each algorithm
    all_results = {}
    for algo_name, algo_func in algorithms.items():
        try:
            if algo_name in ["GMW", "RAND"]:
                actions = algo_func(current_slot, nodes, sinks, potential_links, 
                                  current_channel_gains, noise_power_W, bandwidth)
            else:  # GDTS variants
                actions = algo_func(current_slot, nodes, sinks, potential_links, 
                                  current_channel_gains, noise_power_W, bandwidth)
            
            all_results[algo_name] = actions
            
            # Verify structure
            required_keys = ['y', 'e_hat', 'x', 'p', 'f']
            for key in required_keys:
                assert key in actions, f"Missing key '{key}' in {algo_name} output"
            
            print(f"    ✓ {algo_name} output format is consistent")
            
        except Exception as e:
            print(f"    ❌ {algo_name} failed: {e}")
            return False
    
    # Compare output structures
    reference_algo = "GMW"
    reference_actions = all_results[reference_algo]
    
    for algo_name, actions in all_results.items():
        if algo_name == reference_algo:
            continue
            
        # Check that all algorithms have the same node IDs in y and e_hat
        assert set(actions['y'].keys()) == set(reference_actions['y'].keys()), \
            f"{algo_name} has different node IDs in 'y' compared to {reference_algo}"
        assert set(actions['e_hat'].keys()) == set(reference_actions['e_hat'].keys()), \
            f"{algo_name} has different node IDs in 'e_hat' compared to {reference_algo}"
        
        print(f"    ✓ {algo_name} node ID consistency verified")
    
    return True

def test_gdts_edge_cases():
    """Test GDTS behavior in edge cases."""
    print("\nTesting GDTS edge cases...")
    
    # Test case 1: No energy available
    print("  Testing case: No energy available...")
    nodes_no_energy = [
        {
            'id': 1, 'pos': (0, 0), 'is_sink': False,
            'battery': 0.1,  # Very low energy (below p_min)
            'queue_bits': {2: 1000.0},
            'energy_deficit_queue': 0.0,
            'env_eh_state': 0
        }
    ]
    sinks = [{'id': 2, 'pos': (1, 0), 'is_sink': True}]
    potential_links = {1: [2]}
    current_channel_gains = {(1, 2): 0.1}
    
    actions = gdts_scheduling(0, nodes_no_energy, sinks, potential_links, 
                             current_channel_gains, 1e-12, 1e6)
    
    # Should have no active links due to insufficient energy
    assert len(actions['x']) == 0, "Should have no active links with insufficient energy"
    print("    ✓ No energy case handled correctly")
    
    # Test case 2: No data to transmit
    print("  Testing case: No data to transmit...")
    nodes_no_data = [
        {
            'id': 1, 'pos': (0, 0), 'is_sink': False,
            'battery': 5.0,  # Sufficient energy
            'queue_bits': {},  # No data
            'energy_deficit_queue': 0.0,
            'env_eh_state': 0
        }
    ]
    
    actions = gdts_scheduling(0, nodes_no_data, sinks, potential_links, 
                             current_channel_gains, 1e-12, 1e6)
    
    # Should have no active links due to no data
    assert len(actions['x']) == 0, "Should have no active links with no data"
    print("    ✓ No data case handled correctly")
    
    return True

def run_integration_tests():
    """Run all integration tests."""
    print("=" * 60)
    print("GDTS Framework Integration Test Suite")
    print("=" * 60)
    
    try:
        success = True
        success &= test_gdts_simulation_integration()
        success &= test_gdts_output_consistency()
        success &= test_gdts_edge_cases()
        
        if success:
            print("\n" + "=" * 60)
            print("✓ All GDTS integration tests passed successfully!")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ Some GDTS integration tests failed!")
            print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
