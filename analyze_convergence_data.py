import json
import numpy as np

def analyze_convergence(filepath="mean_instantaneous_throughput.json",
                        steady_state_start_fraction=0.5, # Use last 50% of data for steady state
                        convergence_percentage=0.90):    # Converged when 90% of steady state is reached
    """
    Analyzes convergence time for algorithms from the saved JSON data.

    Args:
        filepath (str): Path to the JSON file.
        steady_state_start_fraction (float): Fraction of total timeslots to start
                                             calculating steady state from.
        convergence_percentage (float): Percentage of steady state throughput
                                        to consider as converged.

    Returns:
        dict: A dictionary {algo_name: convergence_time_slot}.
              Returns None if file not found or data is malformed.
    """
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: File not found at {filepath}")
        return None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {filepath}")
        return None

    if 'timeslot' not in data:
        print("Error: 'timeslot' key missing in JSON data.")
        return None

    timeslots = np.array(data['timeslot'])
    num_total_slots = len(timeslots)
    if num_total_slots == 0:
        print("Error: Timeslot data is empty.")
        return None

    convergence_times = {}

    # Determine the start index for steady-state calculation
    steady_state_start_index = int(num_total_slots * steady_state_start_fraction)
    if steady_state_start_index >= num_total_slots:
        print(f"Warning: steady_state_start_fraction ({steady_state_start_fraction}) is too high, no data for steady state. Using last point.")
        steady_state_start_index = num_total_slots -1 if num_total_slots > 0 else 0


    print(f"Calculating steady state from slot index {steady_state_start_index} onwards (Total slots: {num_total_slots}).")

    for algo_name, throughput_series in data.items():
        if algo_name == 'timeslot':
            continue

        if not isinstance(throughput_series, list) or len(throughput_series) != num_total_slots:
            print(f"Warning: Throughput data for '{algo_name}' is malformed or length mismatch (expected {num_total_slots}, got {len(throughput_series)}). Skipping.")
            continue
        
        throughput_series_np = np.array(throughput_series)

        # Calculate steady-state throughput
        if steady_state_start_index < num_total_slots :
            steady_state_throughput_values = throughput_series_np[steady_state_start_index:]
            if len(steady_state_throughput_values) > 0:
                 steady_state_mean = np.mean(steady_state_throughput_values)
            else: # This case should ideally be caught by the initial check on steady_state_start_index
                print(f"Warning: No data points for steady state for {algo_name} despite index check. Using last value.")
                steady_state_mean = throughput_series_np[-1] if num_total_slots > 0 else 0
        else: 
            print(f"Warning: steady_state_start_index ({steady_state_start_index}) is invalid for {algo_name}. Using last value.")
            steady_state_mean = throughput_series_np[-1] if num_total_slots > 0 else 0


        convergence_threshold = steady_state_mean * convergence_percentage
        
        print(f"\nAlgorithm: {algo_name}")
        print(f"  Steady State Mean Throughput (slots {steady_state_start_index}-end): {steady_state_mean:.4f} Mbps")
        print(f"  Convergence Threshold ({convergence_percentage*100:.0f}% of steady state): {convergence_threshold:.4f} Mbps")

        # Find convergence time
        converged_slot_value = -1 # Default if not converged
        # Iterate through the throughput series to find the first point at or above threshold
        found_convergence = False
        for i in range(num_total_slots): # Iterate up to num_total_slots
            if throughput_series_np[i] >= convergence_threshold:
                converged_slot_value = timeslots[i] # Get the actual time slot value
                found_convergence = True
                break 
        
        if found_convergence:
            convergence_times[algo_name] = int(converged_slot_value) # Store as int
            print(f"  Converged at Time Slot: {converged_slot_value}")
        else:
            convergence_times[algo_name] = None # Or some indicator like 'Not Converged'
            print(f"  Did not reach {convergence_percentage*100:.0f}% of steady state.")
            
    return convergence_times

if __name__ == '__main__':
    # Example usage:
    # Make sure 'mean_instantaneous_throughput.json' is in the same directory
    # or provide the full path.
    results = analyze_convergence(filepath="mean_instantaneous_throughput.json",
                                  steady_state_start_fraction=0.75, # e.g., use last 25% for steady state
                                  convergence_percentage=0.90)     # 90% of steady state

    if results:
        print("\n--- Convergence Times (Time Slot) ---")
        for algo, time_slot in results.items():
            if time_slot is not None:
                print(f"  {algo}: {time_slot}")
            else:
                print(f"  {algo}: Did not converge to threshold.")
