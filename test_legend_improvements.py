#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图例改进效果的脚本
生成示例图形来验证图例可见性改进
"""

import numpy as np
import matplotlib.pyplot as plt
import os

# 模拟算法名称（与parallel_parameter_sweep.py中相同）
ALGORITHMS = [
    "Lyapunov-MEC", "Lyapunov-NoEC", "Lyapunov-UEC", 
    "GMW", "EAG", "RAND", "GDTS", "GDTS-NoEC"
]

# 绘图配置（与parallel_parameter_sweep.py中相同）
LINESTYLES = ['-', '--', '-.', ':', (0, (3, 1, 1, 1)), (0, (5, 10)), (0, (3, 5, 1, 5)), (0, (1, 1))]
MARKERS = ['o', 's', '^', 'v', 'd', 'x', 'p', '*']

def create_test_plot_old_style():
    """创建使用旧样式的测试图（图例可能重叠）"""
    plt.figure(figsize=(10, 6))
    
    # 生成测试数据
    x = np.linspace(1, 25, 9)  # 模拟BATTERY_MAX_J参数范围
    
    for i, algo in enumerate(ALGORITHMS):
        # 生成模拟的吞吐量数据（不同算法有不同性能）
        base_performance = 30 - i * 3  # 基础性能递减
        y = base_performance + np.random.normal(0, 2, len(x)) + x * 0.5
        
        linestyle_idx = i % len(LINESTYLES)
        marker_idx = i % len(MARKERS)
        
        plt.plot(x, y, marker=MARKERS[marker_idx], linestyle=LINESTYLES[linestyle_idx], 
                label=algo, linewidth=2, markersize=6)
    
    plt.xlabel('Maximum Battery Capacity (J)')
    plt.ylabel('Average Throughput (Mbps)')
    plt.title('Old Style: Throughput vs. Battery Capacity')
    plt.grid(True)
    plt.legend(loc='best')  # 旧的图例设置
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('test_legend_old_style.png', dpi=300)
    plt.close()
    print("已生成旧样式测试图: test_legend_old_style.png")

def create_test_plot_new_style():
    """创建使用新样式的测试图（图例优化）"""
    fig, ax = plt.subplots(figsize=(14, 8))  # 更大的图形尺寸
    
    # 生成测试数据
    x = np.linspace(1, 25, 9)  # 模拟BATTERY_MAX_J参数范围
    
    for i, algo in enumerate(ALGORITHMS):
        # 生成模拟的吞吐量数据（不同算法有不同性能）
        base_performance = 30 - i * 3  # 基础性能递减
        y = base_performance + np.random.normal(0, 2, len(x)) + x * 0.5
        
        linestyle_idx = i % len(LINESTYLES)
        marker_idx = i % len(MARKERS)
        
        line, = ax.plot(x, y, marker=MARKERS[marker_idx], linestyle=LINESTYLES[linestyle_idx], 
                       label=algo, linewidth=2, markersize=6)
        
        # 添加置信区间
        ci = np.random.uniform(1, 3, len(x))
        ax.fill_between(x, y - ci, y + ci, color=line.get_color(), alpha=0.2)
    
    ax.set_xlabel('Maximum Battery Capacity (J)', fontsize=12)
    ax.set_ylabel('Average Throughput (Mbps)', fontsize=12)
    ax.set_title('New Style: Throughput vs. Battery Capacity', fontsize=14)
    ax.grid(True, alpha=0.3)
    
    # 优化的图例设置：放在图形右侧外部
    legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', 
                      fontsize=10, frameon=True, fancybox=True, 
                      shadow=True, framealpha=0.9, edgecolor='black')
    legend.get_frame().set_facecolor('white')
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(right=0.75)  # 为右侧图例留出空间
    
    # 保存图形
    fig.savefig('test_legend_new_style.png', dpi=300, bbox_inches='tight', 
               facecolor='white', edgecolor='none')
    plt.close(fig)
    print("已生成新样式测试图: test_legend_new_style.png")

def create_alternative_legend_styles():
    """创建其他图例样式的示例"""
    
    # 样式1：图例在底部
    fig, ax = plt.subplots(figsize=(12, 8))
    x = np.linspace(1, 25, 9)
    
    for i, algo in enumerate(ALGORITHMS):
        base_performance = 30 - i * 3
        y = base_performance + np.random.normal(0, 2, len(x)) + x * 0.5
        
        linestyle_idx = i % len(LINESTYLES)
        marker_idx = i % len(MARKERS)
        
        ax.plot(x, y, marker=MARKERS[marker_idx], linestyle=LINESTYLES[linestyle_idx], 
               label=algo, linewidth=2, markersize=6)
    
    ax.set_xlabel('Maximum Battery Capacity (J)', fontsize=12)
    ax.set_ylabel('Average Throughput (Mbps)', fontsize=12)
    ax.set_title('Alternative Style: Legend at Bottom', fontsize=14)
    ax.grid(True, alpha=0.3)
    
    # 图例在底部，分两行显示
    legend = ax.legend(bbox_to_anchor=(0.5, -0.15), loc='upper center', 
                      ncol=4, fontsize=10, frameon=True, fancybox=True, 
                      shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor('white')
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.2)  # 为底部图例留出空间
    
    fig.savefig('test_legend_bottom_style.png', dpi=300, bbox_inches='tight', 
               facecolor='white', edgecolor='none')
    plt.close(fig)
    print("已生成底部图例样式测试图: test_legend_bottom_style.png")

def main():
    """主函数：生成所有测试图"""
    print("正在生成图例改进效果测试图...")
    
    # 设置随机种子以获得一致的结果
    np.random.seed(42)
    
    # 生成不同样式的测试图
    create_test_plot_old_style()
    create_test_plot_new_style()
    create_alternative_legend_styles()
    
    print("\n图例改进测试完成！")
    print("生成的文件：")
    print("1. test_legend_old_style.png - 旧样式（可能有图例重叠问题）")
    print("2. test_legend_new_style.png - 新样式（图例在右侧外部）")
    print("3. test_legend_bottom_style.png - 替代样式（图例在底部）")
    print("\n请检查这些图像以验证图例可见性改进效果。")

if __name__ == "__main__":
    main()
