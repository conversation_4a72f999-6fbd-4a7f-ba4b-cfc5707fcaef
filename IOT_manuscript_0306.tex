\documentclass[journal]{IEEEtran}
\usepackage{graphicx}
\usepackage{epstopdf}
\graphicspath{{englishpic/}}
\usepackage{hyperref}
\usepackage{bm}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{flushend}
\usepackage{subfigure}
\newtheorem{lemma}{Lemma}
\usepackage{enumerate}
\usepackage{amssymb}
\usepackage{siunitx}
\usepackage{amsmath}
\usepackage[numbers,sort&compress]{natbib}
\usepackage[ruled]{algorithm2e}

\newenvironment{proof}{{\quad \it Proof:}}{$\hfill\blacksquare$\par}
\begin{document}
\title{Maximizing Long-term Average System Communication Capacity of SWIPT-Enabled AF Relay System via Lyapunov Optimization}

% use a multiple column layout for up to three different
% affiliations
\author{Zhenguo~Gao*~\IEEEmembership{Member,~IEEE}, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> \IEEEmembership{Member,~IEEE} %<-this % stops a space
 \IEEEcompsocitemizethanks{\IEEEcompsocthanksitem Z.~G<PERSON>~<PERSON>, <PERSON><PERSON>~<PERSON><PERSON>~<PERSON>, <PERSON><PERSON>~<PERSON>, and <PERSON><PERSON>~<PERSON><PERSON>~<PERSON> are with both the Department of Computer Science and Technology at Huaqiao University and the Key Laboratory of Computer Vision and Machine Learning of Fujian Province University, Xiamen, FJ, 361021, CHINA. E-mail: <EMAIL>; R.~Zhao is with the College of Information Science and Engineering at Huaqiao University, Xiamen, FJ, 361021, CHINA.
} % <-this % stops an unwanted space

\thanks{This work was supported by the National Natural Science Foundation of China under Grant 61972166.}}%


% The paper headers
\markboth{IEEE INTERNET OF THINGS JOURNAL,~Vol.~xx, No.~xx, August~XXXX}%
{Gao \MakeLowercase{\textit{et al.}}: Maximizing Long-term Average System Communication Capacity}

\maketitle

\begin{abstract}
For a SWIPT-enabled single AF relay communication system, we investigate the relay's transmission power management problem for maximizing the long-term average~(LTA) system communication capacity while guaranteeing some constraints on LTA battery energy for the relay and destination. Because LTA expressions are involved in this problem, the Lyapunov optimization framework is adopted. We first formulate the problem, and then we construct two virtual queues for the battery energy constraints of the relay and the destination. Next, by constructing a drift-plus-penalty function that combines the original objective function and the long-term average constraints, the original problem is transformed into a global optimization problem without the LTA constraints. The new global optimization problem is further transformed into a slot-wise local optimization problem by replacing the objective function with an upper bound expression involving only the current time slot. We propose an algorithm named long-term average communication capacity optimization based on Lyapunov optimization~(LTCOL), which runs online to determine the relay's transmission power slot by slot through solving slot-wise local problems. Some important properties of LTCOL, including guaranteed LTA constraints and an assured controllable lower bound on system communication capacity, are provided and proved. Simulation results demonstrate the superiority of LTCOL over existing algorithms.

\end{abstract}
\begin{IEEEkeywords}
SWIPT, Lyapunov optimization, AF relay system, energy management, system communication capacity.
\end{IEEEkeywords}

\section{Introduction}
\IEEEPARstart{B}{eing} a primary networking technology for the sensing layer of the Internet of Things (IoT), the wireless sensor network (WSN) is developing quickly, benefiting from the prevalent applications of miniaturized wireless sensors~\cite{gu2017rate}. However, the stochastic fading of wireless communication channels restricts communication efficiency in WSNs, causing serious outages, especially in deep fading situations. Cooperative communication (relay communication), where some relays help the communication from a source to a destination by forwarding the overheard radio frequency (RF) signals to the destination, can effectively improve the source–destination communication capacity by exploiting diversity gains. In cooperative communication, the time interval of a complete transmission from the source to the destination is divided into two parts. In the first part, the source broadcasts an RF signal, which is received by relays and the destination. In the second part, one or more relays forward the information to the destination using cooperative communication protocols like decode-and-forward (DF) and amplify-and-forward (AF)~\cite{qiu2018lyapunov,qiu2018lyapunov1}. In DF, a relay decodes the information sent by the source, re-encodes it into a new RF signal, and transmits it to the destination. In AF, the relay directly amplifies and forwards the RF signal from the source to the destination, without decoding and re-encoding; hence, AF is simpler than DF.

Another important issue of wireless communications in WSNs is energy supplement to wireless nodes. Because of the limited capacity of batteries traditionally used for powering nodes, the energy limitation problem has long been a challenging issue for WSNs~\cite{Fan}. Although replacing batteries can prolong the lifetime of nodes, frequent battery replacement inevitably leads to high costs, inconvenience, or even dangers. Energy harvesting, which enables nodes to harvest energy from the environment, seems a possible solution. However, the unstable and tiny energy power harvested from a stochastic environment restricts it to be only an auxiliary energy supply. By enabling energy-abundant nodes to transfer energy with RF signals to others over a distance, wireless power transfer (WPT) technology provides a promising solution. WPT is built on energy harvesting but well addresses its weak and unstable energy supply problem. By transmitting energy and information simultaneously using the same RF signal, simultaneous wireless information and power transfer (SWIPT) technology is more flexible than the information and energy isolated transmission scheme~\cite{sinche2019survey,al2015internet,zeng2017communications,khodamoradi2022energy,zhuang2022exploiting}.

In SWIPT, the energy of a received RF signal is divided into two parts: one for energy harvesting, and one for information reception. For nodes with a single antenna, time switching (TS) and power splitting (PS) are two typical energy allocation schemes~\cite{budhiraja2021swipt}. For nodes with multiple antennas, a third scheme, named antenna switching, can also be employed~\cite{zhang2013mimo}. In this paper, we focus on the single antenna case. In the TS scheme, RF signal reception time is divided into two parts, i.e., energy harvesting and information reception~\cite{rajoria2023energy}. Whereas in PS mode, it is the energy of the received RF signal that is divided into those two parts~\cite{hossain2019survey}. The modes using harvested energy include harvest-use (HU), harvest-store-use (HSU), and harvest-use-store (HUS). In HU mode, the harvested energy is immediately and exhaustively used without storage. In HSU mode, the harvested energy is stored first and can only be used in future transmissions. In HUS mode, the harvested energy can be used immediately, but the remaining energy is stored for future use.

However, because of the energy-information coupling in SWIPT, energy management schemes for EH-enabled relay communication systems in existing works are not suitable for SWIPT-enabled relay communication systems. In addition, for example, in a deep fading channel situation, transmission performance can be very bad, and nodes should store energy in the battery and wait for a better channel. Moreover, for practical considerations, it is more desirable to always have some energy stored in the battery. Therefore, to ensure long-term performance of the system, it is reasonable to adopt a lower bounding constraint on long-term average~(LTA) energy of nodes. In summary, there is a need for a long-term robust energy management strategy that applies to SWIPT-enabled relay communication systems in WSNs and maximizes long-term performance of the system while satisfying some lower bounding constraints on LTA energy of nodes. This is the motivation behind this work.

In this paper, we focus on a SWIPT-enabled AF-relay communication system where both the destination and the relay are powered by the energy harvested from both the environment and radio signals from other nodes. We investigate the energy management problem of determining the relay's energy for its forwarding operation, with the purpose of maximizing LTA system communication capacity while assuring that the LTA battery energy of the relay and the destinations do not fall below their corresponding thresholds. As far as we know, there is no research on this transmission power management problem, which further motivates our work. We formulate the energy management problem as
a global optimization problem with both objectives and constraints involving LTA expressions. To solve the problem, we adopt the Lyapunov optimization framework to transform the original problem P1 into slot-wise local optimization problem P3, and propose an online algorithm to solve local problems slot by slot, as outlined in Fig.~\ref{fig_outline}. The proposed algorithm is named long-term average communication capacity optimization based on Lyapunov optimization (LTCOL).

\begin{figure}[htb]
	\centering
	\includegraphics[width=0.45\textwidth]{outline.png}
	\caption{Outline of our LTCOL for the energy management problem.}
	\label{fig_outline}
\end{figure}

In particular, to deal with temporal battery energy threshold violations of the relay and the destination, we define two virtual queues to transform long-term battery energy threshold constraints into queue stability constraints. By embedding queue length variations into a Lyapunov drift function, and combining this with the objective function in P1, we define a drift-plus-penalty function. We then transform P1 into a global optimization problem P2 with the drift-plus-penalty function, and it does not contain LTA expressions. However, P2 is not suitable for running online because it involves variables of future time slots. To overcome this difficulty, we derive an upper bound expression for the drift-plus-penalty objective function, and use the bound expression as a new objective. Thus, P2 is transformed into slot-wise local optimization problem P3. Because P3 only involves variables in a single time slot, P3 can be solved online easily. To this end, LTCOL is proposed using the bisection method. LTCOL runs online to solve series slot-wise P3 problems slot by slot, determining the relay's transmission powers as time goes on. Some important properties of LTCOL are provided and proved. Simulation results demonstrate the superiority of LTCOL over existing algorithms.

The main contributions in this paper are summarized as follows.

\begin{itemize}
	\item[1)]
	We formulate the energy management problem as a global optimization problem, where the objective is LTA system communication capacity, and it contains some threshold-based constraints on the LTA battery energy of the relay and the destination. We employ the Lyapunov optimization framework to solve the global optimization problem by transforming it into a slot-wise local optimization problem, which can be solved online easily since it dose not involve future variables.
\end{itemize}
\begin{itemize}
	\item[2)]
	We propose the LTCOL algorithm, which runs online to solve the slot-wise local optimization problems, thus solving the original global energy management problem. Some preferred properties of LTCOL are proved, including (a) LTCOL guarantees the LTA battery energy constraints of the relay and the destination; (b) LTCOL assures a controllable lower bound on its performance in terms of system communication capacity; and (c) the lower bound can be easily adjusted by charging the Lyapunov penalty factor.
\end{itemize}

\begin{itemize}
	\item[3)]
	We conduct extensive simulation experiments to evaluate LTCOL by comparing it with four other algorithms: OJPC-REH~\cite{dong2017online}; the MDP-based algorithm~\cite{ku2015energy}; the water-filling algorithm~\cite{ozel2011transmission}; and a greedy algorithm. The superiority of LTCOL over the others in terms of LTA system communication capacity and average energy efficiency (EE) are demonstrated and analyzed. The effects of changing the main parameters on LTCOL performance are also evaluated.
\end{itemize}

The rest of this paper is organized as follows. Related works are reviewed in Section~\ref{sec_related_work}. The preliminary models and the problem formulation are provided in Section~\ref{sec_model_and_problem}. The global optimization problem is then transformed using the Lyapunov optimization framework into slot-wise local problems in Section~\ref{sec_problem_transformation}. Then, in Section~\ref{sec_ltocl_algorithm}, the LTCOL algorithm is proposed. Some important properties of LTCOL are provided and proved in Section~\ref{sec_ltcol_feature}. Simulation results are provided and analyzed in Section~\ref{sec_sim}. Lastly, a conclusion is provided in Section~\ref{sec_conclusion}.

For easy reference, frequently used symbols are listed in Table~\ref{t1}.

\begin{table}[!htbp]
	\caption{Symbols and their meanings}
	\label{t1}
	\centering
	\begin{tabular}{|p{0.11\textwidth}|p{0.32\textwidth}|}
		\hline
		\textbf{Symbol} & \textbf{Meaning} \\
		\hline
		$\tau$         & Time length of a time slot \\
		\hline
		$\mathbb{E}(x)$         & Expectation of a random variable $x$\\
		\hline
		$\beta_R$, $\beta_D$           & Power splitting factor of R and D\\
		\hline
		$\eta_R$, $\eta_D$         & Energy conversion coefficient of R and D\\
		\hline
		$B$         & System bandwidth       \\
		\hline
		$P_S$         & Signal transmission power of the sender S\\
		\hline
		$P_{R}(t)$          & Signal transmission power of relay R in time slot $t$\\
		\hline
		$P_{D}(t)$         & Normal working power consumption of destination D in time slot $t$\\
		\hline
		$W_R(t)$, $W_D(t)$         & Energy consumed in time slot $t$ by R and D\\
		\hline
		$d_{SR}$, $d_{SD}$, $d_{RD}$         & Distance between S-R, S-D, and R-D\\
		\hline
		$h_{SR}(t)$, $h_{SD}(t)$, $h_{RD}(t)$         & Channel gains for links $SR$, $SD$, and $RD$ in time slot $t$\\
		\hline
		$h_{SR}^{\max}$, $h_{SD}^{\max}$, $h_{RD}^{\max}$         & Maximum value of $h_{SR}(t)$, $h_{SD}(t)$, and $h_{RD}(t)$ in any time slot $t$\\
		\hline
		$E_R(t)$, $E_D(t)$         & Energy harvested by R and D in time slot $t$            \\
		\hline
		$E_R^{\max}$, $E_R^{\min}$         & Maximum and minimum values of $E_R(t)$ in any time slot $t$  \\
		\hline
		$b_{R}(t)$, $b_{D}(t)$         & Instant battery energy of R and D at the beginning of time slot $t$  \\
		\hline
		$b_R^{\max}$, $b_D^{\max}$        & Battery capacity of R and D  \\
		\hline
		$\delta_{R}$, $\delta_{D}$			& LTA battery energy threshold of R and D	\\
		\hline
		$\gamma_{SR}(t)$, $\gamma_{SD}(t)$, $\gamma_{RD}(t)$         & Instant SNR of link $SR$, $SD$, and $RD$ in time slot $t$ \\
		\hline
		$\gamma_{SR}^{\max}$, $\gamma_{SD}^{\max}$, $\gamma_{RD}^{\max}$         & Maximum SNR of link $SR$, $SD$, and $RD$ \\
		\hline
		$\gamma_{SRD}(t)$         & SNR of the relay link $SRD$ in time slot $t$ \\
		\hline
		$B_{R}(t)$, $B_{D}(t)$         & Value of the virtual queue for battery energy of R and D in time slot $t$  \\
		\hline
		$B_{R}^{\max}$, $B_{D}^{\max}$         & Maximum value of the virtual queue for R and D  \\
		\hline
		$C(t)$         & System communication capacity of the relay system in time slot $t$  \\
		\hline
		$C^{\min}$         & Minimum value of $C(t)$ in any time slot $t$. \\
		\hline
		$N_0$         & Noise power \\
		\hline
	\end{tabular}
\end{table}

\section{Related work}
\label{sec_related_work}

SWIPT has been applied to numerous kinds of wireless communication systems in recent years~\cite{zhang2013mimo,ahmed2017traffic,liu2017cooperative, Xu2019}. SWIPT was applied to a MIMO system containing a base station (BS) and multiple user nodes, where the user nodes' upward data transmissions were exclusively powered by the energy harvested from the BS's RF signals~\cite{zhang2013mimo}. Ahmed et al. used SWIPT to improve the throughput of cognitive radio networks~\cite{ahmed2017traffic}. A resource allocation strategy was proposed by Liu et al. for SWIPT-enabled WSNs to improve energy efficiency~\cite{liu2017cooperative}. Moreover, Xu et al. proposed a robust resource allocation algorithm to maximize the energy efficiency of SWIPT-enabled heterogeneous networks~\cite{Xu2019}.

As a basic and typical wireless communication paradigm, wireless cooperative relay communication has been enhanced with SWIPT to meet various objectives~\cite{hossain2019survey,liu2015noncoherent,nasir2013relaying,gu2015rf,ju2015maximum,zhou2014joint,xiong2015wireless}. Some works mainly focus on analyzing system performance under various channel conditions~\cite{liu2015noncoherent,nasir2013relaying,gu2015rf}, while others are devoted to maximizing the system's end-to-end communication capacity by optimizing various controlling variables~\cite{ju2015maximum,zhou2014joint,xiong2015wireless}. In most works, the relays and/or the source/destination are powered by harvested energy~\cite{zeng2017communications,zhang2013mimo,hossain2019survey,ahmed2017traffic,liu2017cooperative,zhu2019maximizing}. Energy management of SWIPT-enabled and EH-enabled relay communication systems is challenging due to the competition on the shared RF signal between energy flow and information flow, as well as the unpredictability of harvested energy from the dynamic environment~\cite{qiu2018lyapunov}. Many studies have been devoted to this challenge. Ahmed et al. proposed an online power distribution scheme for buffer-aided adaptive link-adaptive relaying communication~\cite{ahmed2014power}. Zlatanov et al. proposed a power allocation scheme for optimizing a general utility function according to the average amount of energy collected by the relays~\cite{zlatanov2017asymptotically}. Zhu et al. proposed a maximizing throughput efficiency (MTE) algorithm to maximizes the system throughput by adjusting the source-to-relay and relay-to-destination time lengths in a cooperative communication round, but does not consider the long-term performance of the system~\cite{zhu2019maximizing}. The Markov decision process~(MDP) and the water-filling technique are two representative approaches to tackle the challenge~\cite{qiu2018lyapunov}. The computational complexity of an MDP will be prohibitively enormous when the state and action spaces are large, and the water-filling technique is restricted to optimizations with limited time slots. However, in some situations, global optimization over infinite time slots is usually preferable to combining local optimizations over short-windowed time slots. The objectives and constraints in such global optimization problems are usually described as LTA expressions, which cannot be solved by an MDP or water-filling technique. In fact, combining local optimal solutions usually leads to less-optimal global solutions because of the couplings among variables.

Lyapunov optimization is a powerful technique suitable for optimization problems involving LTA expressions and stochastic variables and  has been used in many studies~\cite{qiu2018lyapunov,qiu2018lyapunov1,albogamy2022real,shi2022joint,tong2022dynamic,jia2022lyapunov, li2023online}. Lyapunov optimization was employed by Qiu et al. to maximize the LTA net bit-rate for EH-enabled wireless sensor communications~\cite{qiu2018lyapunov}. Meanwhile, it was adopted to improve the average symbol error rate of an EH-enabled relay communication system considering the uncontrollable and unstable amount of energy harvested from the stochastic environment~\cite{qiu2018lyapunov1}. Kam et al. employed Lyapunov optimization to identify the trade-offs between three fundamental measures: age of information~(AoI), accuracy, and completeness~\cite{kam2021role}. For an EH-enabled relay communication system, online joint power control for relaying with energy harvesting~(OJPC-REH) was proposed by Dong et al. to determine the transmission powers of the source and the relay; this resulted in an optimized LTA information transmission rate of the system~\cite{dong2017online}.

The nearest problems to our problem are those tackled in the papers~\cite{gul2017average,gul2018asymptotically, dong2017online}. Gul et al. analyzed the average throughput performance of a round-robin~(RR)-based myopic policy~(MP) for an EH-enabled single-hop WSN, where the nodes have infinite-capacity batteries~\cite{gul2017average}. For a WSN similar to~\cite{gul2017average}, a simple and effective uniforming random ordered policy~(UROP) was proposed in~\cite{gul2018asymptotically}. In that paper, the authors showed that UROP achieves asymptotically optimal throughput
over the infinite time horizon under an infinite-capacity battery assumption. In addition, UROP achieved near-optimal throughput over finite time horizons even with a finite-capacity battery. Unlike~\cite{gul2017average,gul2018asymptotically}, the focus of this paper is on optimizing the average system throughput of a SWIPT-enabled relay communication system over an infinite time horizon with reasonable finite-capacity batteries. Moreover, OJPC-REH algorithm based on Lyapunov optimization was proposed by Dong et al., which optimized the LTA system information transmission rate of EH-enabled relay communication system over an infinite time horizon with finite-capacity batteries~\cite{dong2017online}; however,  the nodes' LTA battery energy constraints were not considered.

\section{Preliminary Models and Problem Formulation}
\label{sec_model_and_problem}
Here we first provide details about the SWIPT-enabled relay communication system studied in this paper. Next, we describe the energy harvesting and consumption model and system communication capacity of the considered communication system. Finally, we formulate the problem.

\subsection{System Model}
The SWIPT-enabled relay system focused on in this paper is shown in Fig.~\ref{fig_system model}, which consists of a source S, a relay R, and a destination D. Node R helps S to transmit information and energy to $D$. There are both energy flow and information flow between a node pair $(x,y)$, $x{\in}$\{S, R\}, $y{\in}$\{R, D\}. A link between nodes $x$ and $y$ is referred to as link $xy$. We assume that the PS-based SWIPT receiver architecture, as shown in Fig.~\ref{fig_PS}, is employed. For a PS receiver with splitting factor $\beta$, $0{\leq}\beta{\leq}1$, $\beta$ proportion of the total energy of the received signal is used for harvesting energy, whereas the remaining proportion of $1{-}\beta$ is used for information reception.

\begin{figure}[htb]
\centering
\includegraphics[width=0.48\textwidth]{fig1.eps}
\caption{SWIPT-enabled single AF relay system.}
\label{fig_system model}
\end{figure}

\begin{figure}[htb]
\centering
\includegraphics[width=0.48\textwidth]{fig2.eps}
\caption{Power splitting-based SWIPT receiver architecture. The power splitter splits a received signal into two portions, where the $\beta$ portion of the signal power is used for energy harvesting while the remaining $1{-}\beta$ portion is used for information reception.}
\label{fig_PS}
\end{figure}


The relay communication system works in slot mode with slot time $\tau$. Let us assume that the time slots are indexed from 0. Each time slot is split into two parts with length $\tau/2$. In the first part of time slot $t$, S broadcasts its information signal with power $P_S(t){=}P_S$, and both D and R can harvest energy and receive information from this signal. The power split factors of R and D are $\beta_R$ and $\beta_D$, respectively. In the second part, R amplifies the information signal and transmits it to D with a selected power. In both parts, the same power splitting factor $\beta_D$ is used by D, which combines all of the received signals from S and R using maximum ratio combing (MRC)~\cite{1998Microwave}. We assume that the links SD, SR, and RD are independent Rayleigh fading channels with mean gains $\lambda_{SD}$, $\lambda_{SR}$, and $\lambda_{RD}$, respectively. The channels, despite remaining unchanged in a time slot, may vary across time slots.



\subsection{Energy Harvesting and Consumption Model}
In our SWIPT-enabled relay communication system, both R and D can harvest energy and receive information simultaneously. We assume that both R and D adopt the HSU energy use mode. R only harvests energy in the first part of a time slot, and the harvested energy can be used in the second part of the same slot or even in future slots. In contrast, because D harvests energy in both parts, we assume that its harvested energy in the current slot can only be used in future time slots. Let $b_R^{\max}$ and $b_D^{\max}$ denote the battery capacities of R and D, respectively. When storing energy in a battery, energy exceeding the battery's capacity is assumed to be lost. We also assume that, to support normal operations, node D continuously consumes energy at power $P_D(t){=}\min\{P_D,b_D(t)/\tau\}$. Additionally, let $P_R(t)$ denote the power used by R for forwarding RF signals in time slot $t$.

Therefore, the battery energy updating formulas of R and D can be expressed as Eq.~\eqref{brt+1} and Eq.~\eqref{bdt+1}, where $W_{R}(t){=}P_{R}(t)\tau/2$ and $W_{D}(t){=}P_{D}(t)\tau$ are the energy values consumed in time slot $t$ by R and D, respectively.

\begin{equation}
\label{brt+1}
b_{R}(t+1){=}\min\{b_{R}(t){+}E_{R}(t){-}W_{R}(t),b_{R}^{\max}\}
\end{equation}
\begin{equation}
\label{bdt+1}
b_{D}(t+1){=}\min\{b_{D}(t){+}E_{D}(t){-}W_{D}(t),b_D^{\max}\}
\end{equation}

The energy harvested by R and D in time slot $t$ are as follows: 
\begin{equation}
\label{ert}
E_{R}(t){=}\frac{{\eta_R}P_S{\beta_R}h_{SR}(t){\tau}}{2(d_{SR})^\kappa},
\end{equation}
\begin{equation}
\label{edt}
E_{D}(t){=}\frac{{\eta_D}P_S{\beta_D}h_{SD}(t)\tau}{2(d_{SD})^\kappa}{+}\frac{{\eta_D}P_{R}(t){\beta_D}h_{RD}(t)\tau}{2(d_{RD})^\kappa},
\end{equation}
where $\kappa$ is the path loss exponent.

To support normal operations, we assume that the average battery energy values of R and D should not fall below the thresholds of $\delta_{R}$ and $\delta_{D}$, respectively; these are expressed as follows:

\begin{equation}
\label{brt}
{\lim_{T\to{+}\infty}\frac{1}{T}\sum_{t{=}0}^{T{-}1}b_{R}(t){\geq}\delta_{R}},
\end{equation}

\begin{equation}
\label{bdt}
{\lim_{T\to{+}\infty}\frac{1}{T}\sum_{t{=}0}^{T{-}1}b_{D}(t){\geq}\delta_{D}}.
\end{equation}

\subsection{System Communication Capacity}

According to \cite{hasna2004performance}, the signal-to-noise ratios (SNRs) of links SD, SR, RD, and SRD in time slot $t$ can be expressed as
\begin{equation}
\label{gammasd}
\gamma_{SD}(t)=\frac{P_S(1-\beta_D)h_{SD}(t)}{N_0},
\end{equation}
\begin{equation}
\label{gammasr}
\gamma_{SR}(t)=\frac{P_S(1-\beta_R)h_{SR}(t)}{N_0},
\end{equation}
\begin{equation}
\label{gammard}
\gamma_{RD}(t)=\frac{P_R(t)(1-\beta_D)h_{RD}(t)}{N_0},
\end{equation}

\begin{equation}
\label{gammasrd}
\gamma_{SRD}(t){=}\frac{\gamma_{SR}(t)\gamma_{RD}(t)}{\gamma_{SR}(t){+}\gamma_{RD}(t){+}1}.
\end{equation}

For the system considered in this paper, D combines all of the received signals from S and R using MRC. The system communication capacity can be expressed as follows~\cite{chen2009ergodic}:

\begin{equation}
\label{capacity}
C(t)=\frac{1}{2}B\log_2(1+\gamma_{SD}(t)+\gamma_{SRD}(t)).
\end{equation}

\subsection{Problem Formulation}
For the targeted SWIPT-enabled relay communication system, relay R is required to determine its transmitting power in every time slot. The objective is to maximize the LTA system capacity ${\lim_{T\to{+}\infty}\frac{1}{T}\sum_{t{=}0}^{T{-}1}C(t)}$ with the prerequisite that the average battery energy constraints of D and R (Eq.~\eqref{brt} and Eq.~\eqref{bdt}) are satisfied. Therefore, the problem can be formally expressed as P1 in Eq.~\eqref{op1}.

\begin{equation}
	\label{op1}
	\begin{aligned}
\textbf{(P1)}\quad&\max_{P_{R}(t)} \quad {\lim_{T\to+\infty}\frac{1}{T}\sum_{t=0}^{T-1}C(t)}\\
		&\begin{array}{r@{\quad}r@{}l@{\quad}l}
			s.t. &&\displaystyle C_1:{\lim_{T\to+\infty}\frac{1}{T}\sum_{t=0}^{T-1}b_{R}(t)\geq\delta_R},\\
				 &&\displaystyle C_2:{\lim_{T\to+\infty}\frac{1}{T}\sum_{t=0}^{T-1}b_{D}(t)\geq\delta_D},\\
				 && C_3:0\leq b_{R}(t)\leq b^{\max}_{R},\\
				 && C_4:0\leq b_{D}(t)\leq b^{\max}_{D},\\
				 && C_5:0\leq W_{R}(t)\leq b_{R}(t){+}E_{R}(t)\\
		\end{array}
	\end{aligned}
\end{equation}

The constraint $C_5$ in Eq.~\eqref{op1} results from the assumption that the energy harvested by node R in the first part in time slot $t$ can be used in the second part and even future time slots. The objective function and some constraints in P1 involve LTA expressions with time tending to infinity. P1 is called a global optimization problem because it is expressed from a global perspective.


\section{Problem Transformation Based on Lyapunov Optimization Framework}
\label{sec_problem_transformation}

Because both the objective function and some constraints in P1 involve LTA expressions, it is challenging to solve P1 using traditional optimization techniques. Inspired by related works~\cite{qiu2018lyapunov,qiu2018lyapunov1,dong2017online,albogamy2022real,shi2022joint,tong2022dynamic,kam2021role}, we adopt the Lyapunov optimization framework to solve P1. Lyapunov optimization is a novel optimization framework that is powerful in solving optimizing problems involving LTA expressions, which achieves this by transforming an original problem into slot-wise local optimization problems involving only local variables~\cite{qiu2018lyapunov1,dong2017online}.

To deal with the LTA constraints in Eq.~\eqref{op1}, we follow the Lyapunov optimization framework and define two virtual queues. They correspond to the energy threshold constraints $C_1$ and $C_2$ on nodes R and D, respectively. The two virtual queues are expressed as follows:

\begin{equation}
	\label{Brt+1}
	B_{R}(t+1)=\max \{B_{R}(t)+\delta_R-b_{R}(t+1),0\},
\end{equation}
\begin{equation}
	\label{Bdt+1}
	B_{D}(t+1)=\max \{B_{D}(t)+\delta_D-b_{D}(t+1),0\}.
\end{equation}

In general, the value of a virtual queue can be considered a measure of the degree that LTA constraints are satisfied in previous time slots. For our two virtual queues here, the value of a virtual queue is indeed a measure of the excessive amount of energy consumed by previous transmissions. This excessive consumption of energy leads to temporary violations of the energy threshold constraints. The value of a virtual queue will increase if the corresponding battery energy is less than the threshold at a time slot. The two virtual queues are used to remove the LTA expressions in P1.

By combining the two virtual queues, we construct a virtual union queue $\Theta(t){=}[B_{R}(t), B_{D}(t)]$, and define a Lyapunov function as
\begin{equation}
\label{lyapunovfunc}
L(\Theta(t)):=\frac{1}{2}B_{R}^2(t)+\frac{1}{2}B_{D}^2(t).
\end{equation}

Then, we define a Lyapunov drift function  (Eq.~\eqref{eq_delta_theta}), which is indeed the key for removing LTA expressions.
\begin{equation}
\label{eq_delta_theta}
\Delta\Theta(t)=L(\Theta(t+1))-L(\Theta(t))
\end{equation}

Following the Lyapunov optimization framework~\cite{dong2017online,neely2010stochastic}, we define a drift-plus-penalty function as follows: 

\begin{equation}
\label{Lpd}
Y(t):=\Delta\Theta(t)-V*C(t),
\end{equation}
where $V$ is a non-negative control parameter called the Lyapunov penalty factor, which adjusts the relative importance of maximizing the system communication capacity compared to assuring the constraints. A larger $V$ implies more preference for maximizing system capacity, whereas a smaller $V$ means that assuring the constraints is more critical. If $V{=}0$, the related constraints are completely ignored.

The drift-plus-penalty function is a weighted sum of the per-slot Lyapunov drift $\Delta\Theta(t)$ and the negative of the system communication capacity, i.e., capacity is used as a penalty term. In fact, the role of the drift-plus-penalty function in the Lyapunov optimization framework is similar to the Lagrange function in the Lagrange multiplier method~\cite{Juffin1975Lagrange}.

By minimizing the drift-plus-penalty function, the time-averaged objective function is optimized while the virtual queues are stabilized. Thus, the constraints in the original optimization problem are satisfied. This is demonstrated by Lemma~\ref{lemma2}.

Accordingly, the constraints $C_1$ and $C_2$ in P1 are \textit{absorbed} into the Lyapunov drift-plus-penalty function. As a result, problem P1 can be approximated as P2:

\begin{equation}
\label{op2}
\begin{aligned}
\textbf{(P2)}\quad&\min_{P_{R(t)}}\quad{Y(t)}\\
&\begin{array}{l@{\quad}l@{}l@{\quad}l}
			s.t.\quad C_3, C_4, C_5.
\end{array}
\end{aligned}
\end{equation}

P2 is defined for time slot $t$, and its objective function $Y(t)$ involves a variable $L(\Theta(t+1))$ of a future time slot $t{+}1$. Thus, P2 involves just two time slots, and hence we say that it is a local optimization problem.

However, involving variables of future time slots is challenging when running online. We follow the Lyapunov optimization framework~\cite{dong2017online,neely2010stochastic} to deal with this challenge. To be specific, we now begin to find an upper bound expression not involving future variables for the objective expression $Y(t)$. Then, we transform P2 into a new problem by replacing the original objective expression with the upper bound expression. By doing so, the new problem can be solved online; thus, the original problem is solved indirectly.

We first derive an upper bound expression not involving future variables for $Y(t)$. The following lemma provides an upper bound expression satisfying our requirements.

\begin{lemma}
\label{lemma_ojb_upperbound}
Eq.~\eqref{upbd} holds for all of the time slots, where $B_1{=}\delta_R^2{+}\delta_D^2{+}(E_{R}^{\max})^2{+}(b_{R}^{\max})^2{+}(E_{D}^{\max})^2{+}(b_{D}^{\max})^2$ ${+}2b_{R}^{\max}E_{R}^{\max}{+}2b_{D}^{\max}E_{D}^{\max}$, $B_2{=}2B_{R}(t)(\delta_R{-}b_{R}(t))$
${+}2B_{D}(t)(\delta_D{-}b_{D}(t))$, $E_{R}^{\max} {:=}\max\{E_{R}(t)|\forall{t}\}$ and $E_{D}^{\max} {:=}\max\{E_{D}(t)|\forall{t}\}$.
\end{lemma}

\begin{equation}
\label{upbd}
\begin{aligned}
Y(t)\leq&\frac{1}{2}\{B_1{+}B_2{+}W_{R}^2(t){-}2b_{R}(t)W_{R}(t)\\
&{+}2B_{R}(t)W_{R}(t){+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t)\\
&{+}2B_{D}(t)W_{D}(t){-}2V{*}C(t)\}
\end{aligned}
\end{equation}

\begin{IEEEproof}
By substituting Eq.~\eqref{lyapunovfunc} into Eq.~\eqref{eq_delta_theta}, we obtain
\begin{equation}
\label{eq_delta_theta2}
\Delta\Theta(t){=}\frac{1}{2}\left((B^2_R(t{+}1){-}B^2_R(t)){+}(B^2_D(t{+}1){-}B^2_D(t))\right).
\end{equation}

For the first part $B_{R}^2(t+1){-}B_{R}^2(t)$ in Eq.~\ref{eq_delta_theta2}, we have

\begin{equation}
	\label{brt+1-brt}
	\begin{aligned}
		B&_{R}^2(t+1){-}B_{R}^2(t)\\
		&=\max\{B_{R}(t){+}\delta_R{-}b_{R}(t{+}1),0\}^2{-}B_{R}^2(t)\\
		&\overset{\text{(a)}}{\leq}(B_{R}(t){+}\delta_R{-}b_{R}(t{+}1))^2{-}B_{R}^2(t)\\
		&=\delta_R^2{-}2\delta_R b_{R}(t{+}1){+}b_{R}^2(t{+}1){+}2B_{R}(t)(\delta_R{-}b_{R}(t{+}1))\\
		&\leq\delta_R^2{+}b_{R}^2(t+1){+}2B_{R}(t)(\delta_R{-}b_{R}(t{+}1))\\
		&=\delta_R^2{+}\min\{b_{R}(t){+}E_{R}(t){-}W_{R}(t),b_{R}^{\max}\}^2\\
		&\quad{+}2B_{R}(t)(\delta_R{-}\min\{b_{R}(t){+}E_{R}(t){-}W_{R}(t),b_{R}^{\max})\})\\
		&\overset{\text{(b)}}{\leq}\delta_R^2{+}(b_{R}(t){-}W_{R}(t))^2{+}E_{R}^2(t){+}2E_{R}(t)b_{R}(t)\\
		&\quad{+}2B_{R}(t)(\delta_R{-}b_{R}(t){+}W_{R}(t))\\
		&\leq\delta_R^2{+}(E_{R}^{\max})^2{+}(b_{R}^{\max})^2{+}W_{R}^2(t){-}2b_{R}(t)W_{R}(t)\\
		&\quad{+}2b_{R}^{\max}E_{R}^{\max}{+}2B_{R}(t)(\delta_R{-}b_{R}(t){+}W_{R}(t)),
	\end{aligned}
\end{equation}
where inequality (a) follows from the fact of $\max\{a,0\}^2{\leq}a^2$, and inequality (b) follows because $\min\{a,b\}{\leq}a$.

For the second part $B_{D}(t{+}1)^2{-}B_{D}(t)^2$ in Eq.~\ref{eq_delta_theta2}, a similar process can be performed to obtain

\begin{equation}
	\label{bdt+1-bdt}
	\begin{aligned}
		B&_{D}^2(t+1){-}B_{D}^2(t)\\
		&=\max\{B_{D}(t){+}\delta_D{-}b_D(t{+}1),0\}^2{-}B_{D}^2(t)\\
		&\leq\delta_D^2{+}(E_{D}^{\max})^2{+}(b_{D}^{\max})^2{+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t)\\
		&\quad{+}2b_{D}^{\max}E_{D}^{\max}{+}2B_{D}(t)(\delta_D{-}b_{D}(t){+}W_{D}(t)).
	\end{aligned}
\end{equation}

By substituting Eq.~\eqref{brt+1-brt} and Eq.~\eqref{bdt+1-bdt} into Eq.~\eqref{eq_delta_theta2}, we obtain

\begin{equation}
\label{upbd1}
\begin{aligned}
		&Y(t)\\
		&\leq \frac{1}{2}\{\delta_R^2{+}(E_{R}^{\max})^2{+}(b_{R}^{\max})^2{+}W_{R}^2(t){-}2b_{R}(t)W_{R}(t)\\
		&\quad{+}2b_{R}^{\max}E_{R}^{\max}{+}2B_{R}(t)(\delta_R{-}b_{R}(t){+}W_{R}(t))\\
		&\quad{+}\delta_D^2{+}(E_{D}^{\max})^2{+}(b_{D}^{\max})^2{+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t)\\
		&\quad{+}2b_{D}^{\max}E_{D}^{\max}{+}2B_{D}(t)(\delta_R{-}b_{D}(t){+}W_{D}(t))\}{-}V{*}C(t)\\
		&{=}\frac{1}{2}\{B_1{+}B_2{+}W_{R}^2(t){-}2b_{R}(t)W_{R}(t){+}2B_{R}(t)W_{R}(t)\\
		&\quad{+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t){+}2B_{D}(t)W_{D}(t){-}2V{*}C(t)\}.
\end{aligned}
\end{equation}
 According to Eq.~\eqref{upbd1}, we have lemma~\ref{lemma_ojb_upperbound}.
\end{IEEEproof}

In Eq.~\ref{upbd}, $B_1$ and $B_2$ are both constants in time slot $t$. By replacing $Y(t)$ in problem P2 with its upper bound provided in Lemma~\ref{lemma_ojb_upperbound}, and removing the time-dependent constants $B_1$ and $B_2$, we obtain problem P3 (Eq.~\eqref{op3}).

\begin{equation}
	\label{op3}
	\begin{aligned}
\textbf{(P3)}\quad\min_{P_{R}(t)}\quad J(W_R(t)){=}&W_{R}^2(t){-}2b_{R}(t)W_{R}(t)\\
		&\hspace{-20mm}{+}2B_{R}(t)W_{R}(t){+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t)\\
		&\hspace{-20mm}{+}2B_{D}(t)W_{D}(t){-}2V{*}C(t)\\
		s.t. \quad C_3, C_4, C_5
\end{aligned}
\end{equation}

P3 is a local optimization problem that depends only on the current time slot. We call this a slot-wise problem. In summary, the global optimization problem P1 is transformed into a slot-wise local optimization problem P3. Thus, the global optimization problem P1 can be solved online by addressing slot-wise problems slot by slot.

\section{Long-term Average Communication Capacity Optimization Based on Lyapunov Optimization Algorithm}
\label{sec_ltocl_algorithm}
To solve P3, we propose our LTCOL algorithm, which runs online slot by slot as time proceeds. The pseudo-code of LTCOL is shown in Algorithm~\ref{algorithm1}.

Because the objective function of P3 is a convex function (refer to Lemma~\ref{lemma_convex} in Section V.A), LTCOL uses the simple and efficient bisection idea to find its minimum value. First, let $X_L$ be $0$, $X_H$ be a sufficiently large value that is definitely larger than the maximum possible energy value of R, and $X_M{=}(X_L{+}X_H)/2$. Subsequently, the value of $X_L$ or $X_H$ is continuously updated according to the gradient at point $X_M$ (lines 7–11 in Algorithm~\ref{algorithm1}) until $X_H{-}X_L{\leq}\epsilon$. Then, the last $X_M{=}(X_L{+}X_H){/}2$ is the solution, i.e., the energy that should be used by R for forwarding.

\begin{algorithm}[ht]
	\label{algorithm1}
	\caption{LTCOL algorithm.}
	\LinesNumbered
	\KwIn{$\delta_R$, $\delta_D$, $V$, accuracy requirements $\epsilon$}
	\KwOut{$P^{opt}_{R}(t)$}
	$t{=}0$;\\
	$B_{R}(t){=}0$, $B_{D}(t){=}0$, $b_{R}(t){=}0$, $b_{D}(t){=}0$;\\
		\While{true}{
			$X_L{=}0$, $X_H{=}b_R(t){+}E_R(t)$,$X_M{=}(X_L{+}X_H)/2$;\\
			\While{$|X_H{-}X_L|{>}\epsilon$}{
				Calculate the gradient $G(X_M)$ of $J(W_R(t))$ at $W_R(t){=}X_M$;\\
                \eIf{$G(X_M){<}0$}{$X_L{=}X_M$;}{$X_H{=}X_M$;}
				$X_M{=}(X_L{+}X_H)/2$;\\
			}
			$X^{opt}{=}X_M$;$\quad$ $P^{opt}_R(t){=}2X^{opt}/\tau$; \\
            Output $P^{opt}_{R}(t)$;\\
			Update $b_{R}(t{+}1)$, $b_{D}(t{+}1)$, $B_{R}(t{+}1)$ and $B_{D}(t{+}1)$ according to Eq.~\eqref{brt+1}, Eq.~\eqref{bdt+1}, Eq.~\eqref{Brt+1} and Eq.~\eqref{Bdt+1}, respectively;\\
			$t{=}t{+}1$.
		}
\end{algorithm}

We analyze the time complexity of LTCOL as follows.

\begin{lemma}
\label{lemma4}
The time complexity of LTCOL in a time slot is $\mathcal{O}(\log_2(b_R^{\max}/\epsilon))$.
\end{lemma}
\begin{IEEEproof}
For any time slot $t$, initially the range of R is $[X_L,X_H]{=}[0,b_R(t){+}E_R(t)]$. LTCOL searches for $W^{opt}_R(t)$ in this range using a binary search strategy. In each while loop, the range $[X_L,X_H]$ shrinks
by half. The while loop continues until $X_h{-}X_l{\leq}\epsilon$. Hence, the while loop will run $\mathcal{O}(\log_2((b_R(t){+}E_R(t)/\epsilon))$ times. Considering that $b_R(t){+}E_R(t){\leq}b_R^{\max}$, the time complexity of LTCOL in a time slot can be obtained as $\mathcal{O}(\log_2(b_R^{\max}/\epsilon))$. Lemma~\ref{lemma4} follows.
\end{IEEEproof}


\section{Analyses of the LTCOL Properties}
\label{sec_ltcol_feature}
Here we show that LTCOL has two preferable properties.
\begin{itemize}
\item{\textbf{Property 1}}: LTCOL guarantees the LTA constraints on battery energy of node R and node D;
\item{\textbf{Property 2}}: LTCOL assures a controllable lower bound on its performance in terms of system communication capacity, and the lower bound can be easily adjusted by charging the Lyapunov penalty factor.
\end{itemize}

Prior to proving property 1, we first prove that LTCOL assures the virtual queues have finite upper bounds; this is required for proving property 1. Furthermore, property 1 is required to prove property 2.

\subsection{LTCOL Assures Virtual Queues Have Finite Upper Bounds}
Before proving that the virtual queues have finite upper bounds and deriving concrete expressions for the upper bounds, we first show that $J(W_R(t))$ is convex in terms of $W_R(t)$.
\begin{lemma}
\label{lemma_convex}
$J(W_R(t))$ is convex in terms of $W_R(t)$.
\end{lemma}
\begin{IEEEproof}
The first-order derivative of $J(W_{R}(t))$ with respect to $W_{R}(t)$ can be obtained as

\begin{equation}
	\label{1o}
	\begin{aligned}
		\frac{\mathrm{d}J(W_{R}(t))}{\mathrm{d}W_{R}(t)}&=2W_{R}(t){-}2b_R(t){+}2B_R(t){-}VB\left[\log_2(\frac{m}{n})\right]^{'}\\
		&\hspace{-10mm}=2W_{R}(t){-}2b_R(t){+}2B_R(t){-}VB[\log_2(m){-}\log_2(n)]^{'}\\
		&\hspace{-10mm}=2W_{R}(t){-}2b_R(t){+}2B_R(t)\\
		&\hspace{-5mm}{-}VB\left[\frac{(1{+}\gamma_{SR}(t){+}\gamma_{SD}(t))\gamma_{RD}^{'}(t)}{m\ln2}{-}\frac{\gamma_{RD}^{'}(t)}{n\ln2}\right],
	\end{aligned}
\end{equation}
where $m{=}1{+}\gamma_{SR}(t){+}\gamma_{RD}(t){+}\gamma_{SR}(t)\gamma_{RD}(t){+}\gamma_{SD}(t)$ ${*}(1{+}\gamma_{SR}(t){+}\gamma_{RD}(t))$, $n{=}1{+}\gamma_{SR}(t){+}\gamma_{RD}(t)$, $\gamma_{RD}(t){=}2W_R(t)(1{-}\beta_D)h_{RD}(t)/({\tau}N_0)$, and $\gamma_{RD}^{'}(t){=}2(1{-}\beta_D)h_{RD}(t)/({\tau}N_0)$.

Equation~\eqref{1o} can be simplified as
\begin{equation}
	\label{1o1}
	\begin{aligned}
		\frac{\mathrm{d}J(W_{R}(t))}{\mathrm{d}W_{R}(t)}{=}&2W_{R}(t){-}2b_R(t){+}2B_R(t)\\
		&\hspace{-6mm}{-}V{*}B\frac{2(1{-}\beta_D)h_{RD}(t)\gamma_{SR}(t)(1{+}\gamma_{SR}(t))}{{\tau}N_0mn\ln2}.
	\end{aligned}
\end{equation}

Furthermore, the second-order derivative of $J(W_{R}(t))$ with respect to $W_{R}(t)$ can be obtained as
\begin{equation}
	\label{2o}
	\begin{aligned}
		\frac{\mathrm{d}^2J(W_{R}(t))}{\mathrm{d}W_{R}(t)^2}&{=}2{-}VB\left[\frac{\gamma_{RD}^{''}(t)(1{+}\gamma_{SR}(t){+}\gamma_{SD}(t))m\ln2}{m^2\ln^22}\right.\\
		&\hspace{-16mm}{-}\frac{\gamma_{RD}^{'}(t)(1{+}\gamma_{SR}(t){+}\gamma_{SD}(t))m^{'}\ln2}{m^2\ln^22}\\
		&\hspace{-16mm}\left.{-}\frac{\gamma_{RD}^{''}(t)n\ln2{-}\gamma_{RD}^{'}(t)n^{'}\ln2}{n^2\ln^22}\right]\\
		&\hspace{-19mm}=2{+}V{*}B\left[\frac{\gamma_{RD}^{'}(t)^2(1{+}\gamma_{SR}(t){+}\gamma_{SD}(t))^2}{m^2\ln2}{-}\frac{\gamma_{RD}^{'}(t)^2}{n^2\ln2}\right]\\
		&\hspace{-19mm}=2{+}V{*}B\left[\frac{g(\gamma_{RD}^{'}(t)^2)}{m^2n^2\ln2}\right],\\
	\end{aligned}
\end{equation}
where $g(\gamma_{RD}^{'}(t)^2)=\gamma_{RD}^{'}(t)^2(1{+}\gamma_{SR}(t){+}\gamma_{SD}(t))^2n^2$ ${-}\gamma_{RD}^{'}(t)^2m^2$.

$g(\gamma_{RD}^{'}(t)^2)$ can be transformed as follows:
\begin{equation}
	\label{g}
	\begin{aligned}
		g(\gamma_{RD}^{'}(t)^2){=}&\gamma_{RD}^{'}(t)^2(2\gamma_{SR}(t)(1{+}\gamma_{RD}(t){+}\gamma_{RD}(t)^{2})\\
		&\hspace{-7mm}{+}\gamma_{SR}(t)^{2}(1{+}4\gamma_{RD}(t){+}4\gamma_{SD}(t){+}2\gamma_{SD}(t)\gamma_{RD}(t))\\
		&\hspace{-7mm}{+}\gamma_{SR}(t)^{3}(8{+}\gamma_{RD}(t){+}2\gamma_{SD}(t)){+}\gamma_{SR}(t)^{4})\\
		&\hspace{-10mm}{>}0.
	\end{aligned}
\end{equation}

By substituting Eq.~\eqref{g} into Eq.~\eqref{2o}, we obtain $\mathrm{d}^2J(W_{R}(t))/\mathrm{d}W_{R}(t)^2{>}0$; hence, lemma~\ref{lemma_convex} follows.
\end{IEEEproof}

Now we demonstrate that the virtual queues $B_{R}(t)$ and $B_{D}(t)$ have finite upper bounds.

\begin{lemma}
\label{lemma1}
The virtual queues $B_{R}(t)$ and $B_{D}(t)$ are upper bounded as Eq.~\eqref{eq_upperbound_brt} and Eq.~\eqref{eq_upperbound_bdt}, respectively.
\end{lemma}

\begin{equation}
\label{eq_upperbound_brt}
B_R(t){\leq}b_{R}^{\max}{+}VBZ_0^{\max}{+}T_0\delta_R
\end{equation}

\begin{equation}
\label{eq_upperbound_bdt}
B_D(t){\leq}b_{D}^{\max}{+}T_1\delta_D
\end{equation}

In Eq.~\eqref{eq_upperbound_brt} and Eq.~\eqref{eq_upperbound_bdt}, $T_0{=}\lceil\delta_R/E_{R}^{\min}\rceil$, $T_1{=}\lceil\delta_D/E_{D}^{\min}\rceil$, and $Z_0^{\max}{:=}\frac{(1{-}\beta_D)h_{RD}^{\max}\gamma_{SR}^{\max}}{{\tau}N_0(1{+}\gamma_{SR}^{\max})\ln2}$.

\begin{IEEEproof}
In this proof, we only prove the upper bound of $B_{R}(t)$. The upper bound of $B_{D}(t)$ can be proved similarly, and is hence omitted here.

By combining Eq.~\eqref{capacity} with the objective function $J(W_R(t))$ in problem P3, the objective function can be re-written as

\begin{equation}
	\label{jwrt}
	\begin{aligned}
	J(W_{R}(t))&{=}W_{R}^2(t){-}2b_{R}(t)W_{R}(t){+}2B_{R}(t)W_{R}(t)\\
	&\quad{+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t){+}2B_{D}(t)W_{D}(t)\\
	&\quad{-}V{*}B\log_2(1{+}\gamma_{SD}(t){+}\gamma_{SRD}(t)).
	\end{aligned}
\end{equation}

Let $Z{:=}\frac{(1{-}\beta_D)h_{RD}(t)\gamma_{SR}(t)(1{+}\gamma_{SR}(t))}{{\tau}N_0mn\ln2}$. Then, we obtain
\begin{equation}
	\label{Z}
	\begin{aligned}
	Z&{=}\frac{(1{-}\beta_D)h_{RD}(t)\gamma_{SR}(t)}{{\tau}N_0(1{+}\gamma_{SR}(t))\ln2}=\frac{(1{-}\beta_D)}{{\tau}N_0\ln2}*\frac{h_{RD}(t)}{\frac{1}{\gamma_{SR}(t)}{+}1}\\
	&{\leq}\frac{(1{-}\beta_D)}{{\tau}N_0\ln2}*\frac{h_{RD}^{\max}}{\frac{1}{\gamma_{SR}^{\max}}{+}1}=\frac{(1{-}\beta_D)h_{RD}^{\max}\gamma_{SR}^{\max}}{{\tau}N_0(1{+}\gamma_{SR}^{\max})\ln2}\\
    &{=}Z_0^{\max}.
	\end{aligned}
\end{equation}

According to Lemma~\ref{lemma_convex}, the first-order derivative of $J(W_{R}(t))$ with respect to $W_{R}(t)$ always increases as $W_{R}(t)$ increases. According to the value of $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)$ at ${W_{R}(t){=}0}$, which is denoted as $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}$, the situations can be classified into the following two cases: (1)$\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{<}0$, and (2) $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{\geq}0$. Now we analyze these two cases separately.

\emph{Case 1:}
$\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{<}0$. Since $J(W_{R}(t))$ is convex with respect to $W_{R}(t)$, the optimal value of $W_{R}(t)$, which is denoted as $W^{opt}_R(t)$, must make $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}W^{opt}_R(t)}{=}0$. Since $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{<}0$ and $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)$ increase as $W_{R}(t)$ increases, we must have $W^{opt}_R(t){>}0$.

By substituting Eq.~\eqref{1o1} into $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{<}0$, we obtain
\begin{equation}
\label{case1}
\begin{array}{rl}
B_{R}(t)&\hspace{-2mm}{<}b_{R}(t){+}V{*}B\frac{(1{-}\beta_D)h_{RD}(t)\gamma_{SR}(t)(1{+}\gamma_{SR}(t))}{{\tau}N_0mn\ln2}\\
&\hspace{-2mm}{=}b_{R}(t){+}V{*}B{*}Z.
\end{array}
\end{equation}

By combining Eq.~\eqref{case1}, Eq.~\eqref{Z}, and $b_{R}(t){\leq}b_{R}^{\max}$, we have
\begin{equation}
	\label{case1_f}
	B_{(R)}(t){<}b_{R}(t){+}V{*}B{*}Z\leq b_R^{\max}{+}V{*}B{*}Z_0^{\max}.
\end{equation}

\emph{Case 2:}
$\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{\geq}0$. In this case, upon substituting Eq.~\eqref{1o1} into $\mathrm{d}J(W_{R}(t))/\mathrm{d}W_{R}(t)|_{W_{R}(t){=}0}{\geq}0$, we obtain
\begin{equation}
	\label{case2_1}
	B_{R}(t){\geq}b_{R}(t){+}V{*}B{*}Z.
\end{equation}

Since $J(W_{R}(t))$ is convex and $\mathrm{d}^2J(W_{R}(t))/\mathrm{d}W_{R}(t)^2{>}0$, and considering the fact that $W_{R}(t){\in}[0,b_R^{\max}]$, we must have $W^{opt}_{R}(t){=}0$; i.e., R does not transmit a signal to D. In this case, when $b_R(t){\leq}\delta_R$, and according to the virtual queue update formula of Eq.~\eqref{Brt+1}, we have

\begin{equation}
B_{R}(t)\leq B_{R}(t{+}1){\leq} B_{R}(t){+}\delta_R.
\end{equation}

Since $W^{opt}_{R}(t){=}0$, the battery energy update formula Eq.~\eqref{brt+1} of R can be expressed as
\begin{equation}
	b_{R}(t{+}1){=}\min \{b_{R}(t){+}E_{R}(t), b_{R}^{\max}\}.
\end{equation}

Assuming that there is a time slot $t_0$ with $B_{R}(t_0){\leq}b_{R}(t_0){+}V{*}B{*}Z$ and $B_{R}(t_0{+}1){>}b_{R}(t_0{+}1)$ ${+}V{*}B{*}Z$, $B_{R}(t)$ must increase from $t{=}t_0$ in the following time slots. If $b_{R}(t_0){<}\delta_R$, then after at most $T_0{=}\lceil\delta_R/E_R^{\min}\rceil$ time slots, we will have $b_{R}(t_0{+}T_0){\geq}{\delta_R}$; then, $B_{R}(t)$ will decrease until Eq.~\eqref{case1} is satisfied. While $B_{R}(t)$ is increasing slot by slot, the increment of $B_{R}(t)$ in each time slot is at most $\delta_R$. Thus, we have

\begin{equation}
\label{case2}
\begin{aligned}
B_{R}(t)&\leq b_{R}(t){+}V{*}B{*}Z{+}T_0\delta_R\\
&\leq b_{R}^{\max}{+}V{*}B{*}Z_0^{\max}{+}T_0\delta_R.
\end{aligned}
\end{equation}

By combining Eq.~\eqref{case1_f} and Eq.~\eqref{case2}, we obtain $B_{R}(t){\leq}b_{R}^{\max}{+}V{*}B{*}Z_0^{\max}{+}T_0\delta_R $. This completes the proof.
\end{IEEEproof}

\subsection{LTCOL Guarantees the Constraints on Long-term Average Battery Energy}
\label{3.c}

Compared with the original optimization problem in Eq.~\eqref{op1}, the problem in Eq.~\eqref{op2} does not contain the two constraints $C_1$ and $C_2$. Here, we will show that, although $C_1$ and $C_2$ are not explicitly contained in Eq.~\eqref{op2}, they are still guaranteed by LTCOL. This is shown in the following lemma.

\begin{lemma}
\label{lemma2}
LTCOL assures that the LTA battery energy constraints of nodes R and D, shown as $C_1$ and $C_2$ in Eq.~\eqref{op1}, are satisfied.
\end{lemma}
\begin{IEEEproof}
Here we prove that the constraint on the LTA battery energy of R is guaranteed. The case for node D can be proved similarly, and hence is omitted here.

Equation~\eqref{Brt+1} can be rewritten as

\begin{equation}
\label{brt+12}
B_{R}(t{+}1){=}
\left\{
\begin{aligned}
	B_{R}(t){+}\delta_R{-}b_{R}&(t{+}1),\\
	&\text{if ${B_{R}(t){\geq}b_{R}(t{+}1){-}\delta_R}$}\\
	0,\hspace{19mm}\\
	&\text{if ${B_{R}(t){<} b_{R}(t{+}1){-}\delta_R}$}.
\end{aligned}
\right.
\end{equation}

Subtracting $B_{R}(t)$ from both sides of Eq.~\eqref{brt+12} gives

\begin{equation}
	\label{brt+1-brt2}
	\begin{aligned}
	B_{R}(t{+}1){-}B_{R}(t)&{=}
	\begin{cases}
		\delta_R{-}b_{R}(t{+}1),\\
		&\hspace{-10mm} \mbox{if ${B_{R}(t){\geq} b_{R}(t{+}1){-}\delta_R}$} \\
		{-}B_{R}(t),\\
		&\hspace{-10mm} \mbox{if ${B_{R}(t){<}b_{R}(t{+}1){-}\delta_R}$} \\
	\end{cases}\\
	&\hspace{-10mm}{=}\max\{\delta_R{-}b_{R}(t), {-}B_{R}(t)\}\overset{\text{(a)}}{\geq}\delta_R{-}b_{R}(t{+}1).
	\end{aligned}
\end{equation}

The inequality (a) in Eq.~\eqref{brt+1-brt2} holds for the fact that $\max\{x,y\}{\geq}x$.

Upon accumulating Eq.~\eqref{brt+1-brt2} from $t{=}0$ to $T{-}1$, we obtain

\begin{equation}
	\label{brt>}
	B_{R}(t){\geq }T\delta_R{-}\sum_{t=0}^{T{-}1}b_{R}(t).
\end{equation}

Then, by dividing both sides of Eq.~\eqref{brt>} by $T$, and letting $T$ tend to infinity, we have

\begin{equation}
\label{limB}
\lim_{T\to{+}\infty}\frac{B_{R}^{\max}}{T}\geq\lim_{T\to{+}\infty}\frac{B_{R}(t)}{T}\geq\delta_R{-}\lim_{T\to{+}\infty}\frac{1}{T}\sum_{t=0}^{T{-}1}b_{R}(t).
\end{equation}

According to Lemma \ref{lemma1}, virtual queue $B_{R}(t)$ has an finite upper bound $B_{R}^{\max}$. Therefore, we can obtain
\begin{equation}
	\label{limbrt}
	\lim_{t\to{+}\infty}\frac{B_{R}(t)}{t}\leq\lim_{t\to{+}\infty}\frac{B_{R}^{\max}}{t}{=}0.
\end{equation}

By combining Eq.~\eqref{limB} and Eq.~\eqref{limbrt}, we obtain Eq.~\eqref{32}, which completes the proof.
\begin{equation}
	\label{32}
	\lim_{T\to{+}\infty}\frac{1}{T}\sum_{t{=}0}^{T{-}1}b_{R}(t){\geq}\delta_R
\end{equation}
\end{IEEEproof}


\subsection{LTCOL Assures Controllable Lower Bound on Long-term Average System Communication Capacity}
In this section, we will show that, with LTCOL, the LTA system communication capacity has a lower bound independent of time. This lower bound is adjustable by modifying the Lyapunov penalty factor. In other words, LTCOL provides a guaranteed quality of performance, and, more importantly, the guaranteed quality is easily controllable.

\begin{lemma}
\label{lemma3}
Equation~\eqref{controlable_lower_bound} holds, where $C^{\min}{:=}\min\{C(t)|\forall{t}\}$, $B_{R}^{\max}{:=}\max\{B_{R}(t)|\forall{t}\}$ and $B_{D}^{\max}{:=}\max\{B_{D}(t)|\forall{t}\}$.
\end{lemma}

\begin{equation}
\label{controlable_lower_bound}
\begin{aligned}
\lim_{T\to{+}\infty}\frac{1}{T}\sum_{t{=}0}^{T{-}1}C(t){\geq}&C^{\min}{-}\frac{B_{D}^{\max}b_{D}^{\max}}{V}\\
&{-}\frac{B_1{+}2E_R^{\max}b_R^{\max}{+}{2}B_{R}^{\max}b_{R}^{\max}}{2V}
\end{aligned}
\end{equation}

\begin{IEEEproof}
$B_2$ in Eq.~\eqref{upbd} can be rewritten as follows:
\begin{equation}
		\label{B2}
			B_2{\leq} 2B_{R}^{\max}(\delta_R{-}b_{R}(t)){+}2B_{D}^{\max}(\delta_D{-}b_{D}(t)).
\end{equation}
Substituting Eq.~\eqref{B2} into Eq.~\eqref{upbd} gives us
	\begin{equation}
	\label{upbd2}
		\begin{aligned}
			Y(t)&{\leq}\frac{1}{2}[B_1{+}2B_{R}^{\max}(\delta_R{-}b_{R}(t))\\
			&\quad{+}2B_{D}^{\max}(\delta_D{-}b_{D}(t)){+}W_{R}(t)^2\\
			&\quad{-}2b_{R}(t)W_{R}(t){+}2B_{R}(t)W_{R}(t){+}W_{D}(t)^2\\
			&\quad{-}2b_{D}(t)W_{D}(t){+}2B_{D}(t)W_{D}(t)]{-}V{*}C(t).
		\end{aligned}
	\end{equation}

According to Lemma~\ref{lemma2}, $\mathbb{E}\{(\delta_R{-}b_{R}(t))\}\leq0$ and $\mathbb{E}\{(\delta_D{-}b_{D}(t))\}{\leq}0$, where $\mathbb{E}\{x\}$ denotes the mathematical expectation. Using the \emph{w-only} strategy~\cite{neely2010stochastic}, Eq.~\eqref{upbd2} can be rewritten as
\begin{equation}
\label{upbd3}
\begin{aligned}
		Y(t)&{\leq}\frac{1}{2}\left[B_1{+}W_{R}^2(t){-}2b_{R}(t)W_{R}(t){+}2B_{R}(t)W_{R}(t)\right.\\
		&\hspace{-7mm}\left.\quad{+}W_{D}^2(t){-}2b_{D}(t)W_{D}(t){+}2B_{D}(t)W_{D}(t)\right]{-}VC^{\min}.
\end{aligned}
\end{equation}

With some mathematical manipulations, Eq.~\eqref{upbd3} can be transformed as follows:

\begin{equation}
\label{lowbd}
\begin{aligned}
&C(t){\geq}C^{\min}+\frac{W_{R}(t)({-}W_{R}(t){+}2b_{R}(t){-}2B_{R}(t))}{2V}\\
&\quad{+}\frac{2\Delta\Theta(t){-}B_1}{2V}+\frac{W_{D}(t)({-}W_{D}(t){+}2b_{D}(t){-}2B_{D}(t))}{2V}\\
&\overset{\text{(a)}}{\geq}C^{\min}{+}\frac{2\Delta\Theta(t){-}B_1}{2V}{+}\frac{W_{R}(t)({-}E_R(t){+}b_{R}(t){-}2B_{R}(t))}{2V}\\
&\quad{+}\frac{W_{D}(t)(b_{D}(t){-}2B_{D}(t))}{2V}\\
&{\geq}C^{\min}{+}\frac{2\Delta\Theta(t){-}B_1}{2V}{-}\frac{E_R^{\max}b_R^{\max}{+}B_{R}^{\max}b_{R}^{\max}}{V}\\
&\quad{-}\frac{B_{D}^{\max}b_{D}^{\max}}{V}.
\end{aligned}
\end{equation}

The inequality (a) in Eq.~\eqref{lowbd} is obtained because $W_{R}(t){\leq}b_{R}(t){+}E_R(t)$ and $W_{D}(t){\leq}b_{D}(t)$ at all times.

By dividing both sides of Eq.~\eqref{lowbd} by $T$, and letting $T$ tend to infinity, we have Eq.~\eqref{lowbd2}, which completes the proof.
\begin{equation}
\label{lowbd2}
\begin{aligned}
	&\lim_{T\to{+}\infty}\frac{1}{T}\mathop{\sum}_{t{=}0}^{T{-}1}C(t)\\
	&\hspace{5mm}{\geq}C^{\min}{-}\frac{B_1{+}2E_R^{\max}b_R^{\max}{+}2B_{R}^{\max}b_{R}^{\max}}{2V}{-}\frac{B_{D}^{\max}b_{D}^{\max}}{V}\\
	&\hspace{5mm}\quad{+}\lim_{T\to+\infty}\frac{1}{T}\sum_{t{=}0}^{T{-}1}\frac{\Delta\Theta(t)}{V}\\
	&\hspace{5mm}{=}C^{\min}{-}\frac{B_1{+}2E_R^{\max}b_R^{\max}{+}2B_{R}^{\max}b_{R}^{\max}}{2V}{-}\frac{B_{D}^{\max}b_{D}^{\max}}{V}\\
	&\hspace{5mm}\quad{+}\lim_{T\to{+}\infty}\sum_{t{=}0}^{T{-}1}\frac{L(\Theta(t)){-}L(\Theta(0))}{VT}\\
	&\hspace{5mm}{=}C^{\min}{-}\frac{B_1{+}2E_R^{\max}b_R^{\max}{+}2B_{R}^{\max}b_{R}^{\max}}{2V}{-}\frac{B_{D}^{\max}b_{D}^{\max}}{V}
\end{aligned}
\end{equation}

\end{IEEEproof}

Lemma \eqref{lemma3} shows that the LTA system communication capacity has a lower bound independent of time. Furthermore, this lower bound is controllable because it can be adjusted by modifying $V$.

\section{Performance Evaluation}
\label{sec_sim}
Here simulation experiments are performed to comparatively evaluate the performance of the LTCOL algorithm. The performance metrics used include system communication capacity, and energy efficiency. In this text, system communication capacity is sometimes abbreviated as system capacity for short. Unless otherwise specified, the simulation parameters are set as follows: $P_S{=}$\SI{1}{\watt}, $d_{SD}{=}$\SI{3}{m}, $d_{SR}{=}$\SI{2}{m}, $d_{RD}{=}$\SI{1.5}{m}, $\kappa{=}2$, $B{=}$\SI{1}{MHz}, $b_{R}^{\max}{=}b_{D}^{\max}{=}$\SI{72}{J}, $\delta_R{=}$\SI{10}{J}, $\delta_D{=}$\SI{12}{J}, $P_D{=}$\SI{0.02}{\watt}, $N_0{=}$\SI{0.02}{\watt}, $\eta{=}\eta_R{=}\eta_D{=}$0.2, $\beta{=}\beta_R{=}\beta_D{=}$0.7, and $V{=}1000$.

\subsection{Queue Performance Analysis}

Figure~\ref{Virtualqueue} shows the variations in battery energy and virtual queue value of R over 100 time slots. When the battery energy of R drops below $\delta_R$, the value of the virtual queue increases rapidly, but then decreases in the following period. This is because when the virtual queue is too large, R will be constrained to reduce its transmission power to save energy, thereby reducing its virtual queue. When its battery energy is much greater than $\delta_R$, the virtual queue will decrease. To increase the system capacity, R can increase its transmission power when its channel is in good condition, even at the cost of temporarily sacrificing its battery threshold constraint, which may lead to an increase in its virtual queue.

\begin{figure}
	\centering
	\includegraphics[width=0.48\textwidth]{battery.png}
	\caption{Changes in the virtual queue and battery energy of R over 100 time slots.}
	\label{Virtualqueue}
\end{figure}

\subsection{Performance Comparison}
To comparatively evaluate the performance of LTCOL, we compare it with some typical and interesting algorithms: OJPC-REH~\cite{dong2017online}, the MDP-based algorithm~\cite{ku2015energy}, and the water-filling algorithm~\cite{ozel2011transmission}. These algorithms are selected as representatives of traditional algorithms and Lyapunov optimization algorithms for the EH-enabled relay communication system. The water-filling algorithm in~\cite{ozel2011transmission} is an offline algorithm, and cannot be directly used for our problem. Here, we run the water-filling algorithm with $T{=}10$ time slots, assuming that all of the information in these 10 time slots is known. Although this assumption is actually impractical, it is used as a baseline for comparison. We also construct a greedy-based algorithm, referred to as Greedy in the following text, for comparison. In Greedy, the relay exhausts all of its battery energy for its forwarding operation in the current time slot.

\begin{figure}
	\centering
	\includegraphics[width=0.5\textwidth]{round.png}
	\caption{Performance comparison in terms of average system communication capacity over different rounds of simulation.}
	\label{round}
\end{figure}

Our comparative experiment contains 40 rounds of simulations, with each simulation running for $3{\times}10^4$ time slots. The average system capacity of these $3{\times}10^4$ time slots is used as the final result. The results are shown in Fig.~\ref{round}. We can see that the Greedy algorithm has the lowest performance among the five algorithms, with its system capacity roughly stabilized at \SI{5.8e5}{bps}. Since the Greedy algorithm aggressively uses all of the available energy for transmitting, ignoring the current channel condition, it usually leads to undesirable long-term system communication capacity. The average system capacity achieved by the MDP-based algorithm is roughly stabilized at \SI{7.6e5}{bps}, which is lower than \SI{8.2e5}{bps}, \SI{9.2e5}{bps}, and \SI{10e5}{bps}, i.e., those of the water-filling algorithm, OJPC-REH, and LTCOL, respectively. The main reason that OJPC-REH and LTCOL perform better than the water-filling algorithm is that the water-filling algorithm cannot consider infinite time slots, and it needs to know the channel information in advance, which is generally impractical. As expected, LTCOL outperforms OJPC-REH. The explanation is as follows. OJPC-REH only guarantees that R and D have non-negative battery energy, without the additional thresholding requirements on their LTA energy values. Thus, it cannot well balance energy consumption between the current slot and the future slot. In contrast, LTCOL ensures that the nodes' LTA battery energy is higher than a certain threshold, and tries to achieve better long-term system communication capacity.

\begin{figure}
	\centering
	\includegraphics[width=0.5\textwidth]{SNR_bps.png}
	\caption{Performance comparison in terms of average system communication capacity at different SNRs.}
	\label{SNR_bps}
\end{figure}

Figure~\ref{SNR_bps} shows the performance comparison results in terms of average system communication capacity at different SNRs. It can be seen that the proposed algorithm achieves better performance than the other algorithms when the SNR is in the range [\SI{3}{dB}, \SI{33}{dB}], especially when it is greater than \SI{15}{dB}. This is primarily because a continuous energy management strategy is more advantageous than a discrete one at a higher SNR. In addition, the MDP-based algorithm performs much worse than the other algorithms at a high SNR. This is mainly because when the SNR is high, a small amount of additional energy for transmission may lead to considerable performance improvement; the energy-efficient strategy of the MDP-based algorithm may miss good transmission opportunities, which results in lower performance.

\begin{figure}
	\centering
	\includegraphics[width=0.48\textwidth]{ps_bps.png}
	\caption{Performance comparison in terms of average system communication capacity at different power splitting factors.}
	\label{ps_bps}
\end{figure}

Figure~\ref{ps_bps} shows the performance comparison results in terms of average system communication capacity at different power splitting factors. It can be seen that, for all of the algorithms, performance increases as $\beta$ increases at the beginning, but then there is a loss in performance as $\beta$ continues to increase. This is because as $\beta$ increases, more and more power is used to harvest energy, and R has more power for data transmission. However, as $\beta$ gets even larger, the power used to decode the information gradually decreases, leading to a decrease in performance. In addition, it can be seen that the proposed algorithm achieves better performance than other algorithms at different $\beta$.


\begin{figure}
	\centering
	\includegraphics[width=0.5\textwidth]{SNR_EE.png}
	\caption{Performance comparison in terms of average energy efficiency at different SNRs.}
	\label{SNR_EE}
\end{figure}

We also performed simulations to compare the algorithms in terms of node R's average energy efficiency (EE). Energy efficiency is defined as the ratio between the sum rate and the total power consumption~\cite{khodamoradi2022energy}. In these simulations, we collect energy efficiency results of R in $3{\times}10^4$ time slots for various $P_S/N_0$, and use the average as the final result. The simulation results are shown in Fig. ~\ref{SNR_EE}. It can be seen that the algorithms with LTA metrics as optimization objectives, i.e., LTCOL and OJPC-REH, achieve better average energy efficiency than the other algorithms. Because LTCOL has a more flexible LTA constraint on node energy, the node can store energy for future use when channels are poor, and thus achieve better performance than OJPC-REH. The water-filling algorithm is inferior to LTCOL and OJPC-REH because it optimizes locally over a limited time period. The MDP-based algorithm and Greedy algorithm, which do not have LTA expressions, have a lower average energy efficiency than LTCOL, OJPC-REH, and the water-filling algorithm. The simulation results also show that, although the MDP-based algorithm performs worse than Greedy in terms of average system communication capacity at a high SNR, it outperforms Greedy in terms of average energy efficiency.


\subsection{Effects of Parameters on Performance}
Some simulation experiments are conducted to evaluate the effects of parameters $\delta_R$, $\delta_D$, and $V$ on the algorithms.

\subsubsection{Effects of $\delta_R$ and $\delta_D$}
To investigate the effect of $\delta_R$ on average system communication capacity, we perform a simulation experiment by fixing $\delta_D{=}$\SI{12}{J}, and letting $\delta_R$ vary from \SI{1}{J} to \SI{30}{J}. The results are shown in Fig.~\ref{theta}. The average system communication capacity decreases rapidly from \SI{10.03e5}{bps} to \SI{9.53e5}{bps} as $\delta_R$ increases from \SI{1}{J} to \SI{30}{J}. This is because increasing $\delta_R$ forces R to take a more conservative energy management strategy, i.e., to allocate less energy for transmission in the current time slot, thus resulting in a smaller average system capacity.

Another simulation experiment is performed to inspect the effect of $\delta_D$. In this experiment, we fix $\delta_R{=}$ \SI{10}{J}, and let $\delta_D$ vary from \SI{1}{J} to \SI{30}{J}. The results are shown in Fig.~\ref{delta}. As $\delta_D$ increases, the average system communication capacity decreases from \SI{10.93e5}{bps} to \SI{7.29e5}{bps}, and then remains unchanged. Increasing $\delta_D$ forces D to conserve more energy for future use, thus leaving less energy for information reception; this in turn leads to a decrease in average system communication capacity. As $\delta_D$ increases further, to help D maintain the stability of the virtual queue, R will try to transmit all of its energy to D regardless of the channel condition. As a result, the average system communication capacity will roughly converge to a constant value, which is consistent with the analysis result in Lemma $\ref{lemma3}$.

\begin{figure}
\centering
\includegraphics[width=0.48\textwidth]{delta_R.png}
\caption{Effect of $\delta_R$ on average system communication capacity.}
\label{theta}
\end{figure}
\begin{figure}
	\centering
	\includegraphics[width=0.48\textwidth]{delta_D.png}
	\caption{Effect of $\delta_D$ on average system communication capacity.}
	\label{delta}
\end{figure}

\subsubsection{Effects of $V$}
To inspect the effect of $V$ on average system communication capacity, we perform simulations where $V$ is increased from $500$ to $10000$. The results are shown in Fig.~\ref{V}. According to Lyapunov optimization~\cite{lv2021contract,kam2021role}, the greater the penalty factor $V$, the more important the task of increasing system capacity compared to the requirement of stabilizing the virtual queues. As the penalty factor $V$ increases from $500$ to $6000$, decisions leading to larger average system communication capacity are more preferred, and therefore the system communication capacity will increase. However, when the value of $V$ is greater than $6000$, the performance gain from increasing $V$ is minimal, and eventually the average system capacity will gradually converge to \SI{1.16e6}{bps}.

\begin{figure}
\centering
\includegraphics[width=0.48\textwidth]{V.png}
\caption{Effect of $V$ on average system communication capacity.}
\label{V}
\end{figure}


\section{Conclusion}
\label{sec_conclusion}
In this paper, we focused on the SWIPT-enabled single AF relay communication system to investigate the energy management problem for the relay. The task was to determine the relay's transmission power for maximizing the LTA system communication capacity while guaranteeing some constraints on the LTA battery energy of the relay and the destination. As such, following the Lyapunov optimization framework, we transformed the original global optimization problem into a slot-wise local optimization problem, and then designed the LTCOL algorithm. LTCOL was run online to determine the relay's transmission power slot by slot; this was done by solving slot-wise local optimization problems. In addition, some important properties of LTCOL were provided and proved. The virtual queues were upper bounded; the LTA battery energy constraints of the relay and the destination were guaranteed; and the average system communication capacity was lower bounded and could be well controlled by adjusting the Lyapunov penalty factor. These properties showed that the LTCOL could accomplish the optimization task well while satisfying the LTA constraints. The final simulation results demonstrated the superiority of LTCOL over existing algorithms in terms of average system capacity and average energy efficiency.

The work in this paper only optimized the relay's transmission power. It can be seen from the final simulation results that many key parameters had an impact on the system's performance. In future work, we intend to jointly optimize some key variables, such as the relay's transmission power, power splitting factor, and Lyapunov penalty factor, to achieve better long-term system performance. Furthermore, we will also explore extending the problem from the single-hop case to the more practical multi-hop case. We hope that the approach in this work helps researchers to investigate similar long-term optimization problems in SWIPT-enabled WSNs.

\bibliography{mybibfile}

\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,keepaspectratio]{gzg.png}}]
{Zhenguo~Gao}
(M'18) Ph.D., Professor. He is now a Professor at Huaqiao University, Xiamen, China. He is also the Dean of Key Laboratory of Computer Vision and Machine Learning (Huaqiao University), at Fujian Province University. He has been a visiting scholar at the University of Illinois at Urbana–Champaign and University of Michigan in 2010 and 2011. He received his BS and MS degrees in Mechanical and Electrical Engineering from Harbin Institute of Technology, Harbin, China, in 1999 and 2001, respectively. Then he received his Ph.D. degree in Computer Architecture from Harbin Institute of Technology, Harbin, China, in 2006. His research interests include wireless ad hoc networks, cognitive radio networks, and network coding.
\end{IEEEbiography}

\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,keepaspectratio]{whj.jpg}}]
	{Haijun~Wang} is a graduate student at Huaqiao University, Xiamen, China. He received his B.E. degree in Internet of Things Engineering from Jiangxi University of Science and Technology. His research interests include internet of things and SWIPT.
\end{IEEEbiography}

\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,keepaspectratio]{cy.png}}]
	{Yan~Chen} is a graduate student at Huaqiao University, Xiamen, China. He received his B.E. degree in Software Engineering from Hebei University of Technology. His research interests include cooperative communication, and cognitive radio networks.

\end{IEEEbiography}

\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,keepaspectratio]{fll.jpg}}]
	{Liling Fan} is a graduate student at Huaqiao University, Xiamen, China. She received her B.E. degree in Internet of Things Engineering from Sanming University. Her research interests include cooperative communication, and cognitive radio networks.
\end{IEEEbiography}

\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,keepaspectratio]{zr.png}}]
	{Rui Zhao}(M'12) received double bachelor's degrees from the Harbin Institute of Technology in 2003, and M.S. and Ph.D. degrees in electrical engineering from Southeast University, China, in 2006 and 2010, respectively. After graduation, he joined the School of Information Science and Engineering, Huaqiao University, China, where he is currently an Associate Professor. From 2014 to 2015, he visited the Department of Electronic and Computer Engineering, the Hong Kong University of Science and Technology, Hong Kong, where he was a visiting research scholar studying the performance analysis of cooperative communication systems. His current research interests include cooperative communications, physical layer security communications, and MIMO communication systems. He has authored many papers in international journals, such as IEEE Transactions on Wireless Communications, and IEEE Transactions on Communications, and papers for conferences such as IEEE Globecom and IEEE ICC.
\end{IEEEbiography}


\bibliographystyle{IEEEtran}


\end{document}

