@article{SDPJDLSKT2024,
        author = {<PERSON><PERSON><PERSON><PERSON>~<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>~<PERSON> and <PERSON><PERSON>~<PERSON> and <PERSON><PERSON>~<PERSON>},
        title = {The flexible job shop scheduling problem: A review},
        journal = {European Journal of Operational Research},
        volume = {314}, 
        number = {2}, 
        month = {April}, 
        year = {2024}, 
        pages = {409-432},
}
   
   
   @ARTICLE{Qi2018,
   	author={Qi, Qinglin and Tao, Fei},
   	journal={IEEE Access}, 
   	title={Digital Twin and Big Data Towards Smart Manufacturing and Industry 4.0: 360 Degree Comparison}, 
   	year={2018},
   	volume={6},
   	number={},
   	pages={3585-3593},
   	doi={10.1109/ACCESS.2018.2793265}}
   
   
   @ARTICLE{Koo2023,
   	author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON>},
   	journal={IEEE Sensors Journal}, 
   	title={{Novel control theoretic consensus-based time synchronization algorithm for WSN in industrial applications: Convergence analysis and performance characterization}}, 
   	year={2023},
   	volume={23},
   	number={4},
   	pages={4159-4175},
   	doi={10.1109/JSEN.2022.3231726}}
   
   @INPROCEEDINGS{Prasad2021,
   	author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>erma, <PERSON>ak Kumar and Sarangi, Pradeepta and Singh, Sunny},
   	booktitle={Proc. 2021 International Conference on Computational Intelligence and Computing Applications (ICCICA)}, 
   	title={Disaster Management System using Wireless Sensor Network: A Review}, 
   	year={2021},
   	volume={},
   	number={},
   	pages={1-6},
   	keywords={Wireless sensor networks;Technological innovation;Protocols;Computer architecture;Alarm systems;Tsunami;Prediction algorithms;WSN;Disaster;ANP;ICT},
   	doi={10.1109/ICCICA52458.2021.9697236}}
   
   
   @ARTICLE{Liu2020,
   	author={Liu, Xiaowu and Yu, Jiguo and Li, Feng and Lv, Weifeng and Wang, Yinglong and Cheng, Xiuzhen},
   	journal={IEEE Internet of Things Journal}, 
   	title={Data Aggregation in Wireless Sensor Networks: From the Perspective of Security}, 
   	year={2020},
   	volume={7},
   	number={7},
   	pages={6495-6513},
   	keywords={Wireless sensor networks;Security;Data aggregation;Network topology;Sensors;Internet of Things;Electronic mail;Data aggregation (DA);security;wireless sensor network (WSN)},
   	doi={10.1109/JIOT.2019.2957396}}
   
   
   @ARTICLE{Tomkos2020,
   	author={Tomkos, Ioannis and Klonidis, Dimitrios and Pikasis, Evangelos and Theodoridis, Sergios},
   	journal={IT Professional}, 
   	title={{Toward the 6G network era: Opportunities and challenges}}, 
   	year={2020},
   	volume={22},
   	number={1},
   	pages={34-38},
   	keywords={5G mobile communication;Artificial intelligence;Training data;6G mobile communication;Wireless communication;Distributed databases;Edge computing},
   	doi={10.1109/MITP.2019.2963491}}
   
   @INPROCEEDINGS{Shabber2021,
   	author={Shabber, Shaik Mulla and Bansal, Mohan and Devi, P Mrudula and Jain, Prateek},
   	booktitle={Proc. 2021 IEEE International Symposium on Smart Electronic Systems (iSES)}, 
   	title={{iHAS}: An intelligent home automation based system for smart city}, 
   	year={2021},
   	volume={},
   	number={},
   	pages={48-52},
   	keywords={Home appliances;Home automation;Portable computers;Smart cities;Prototypes;Synchronization;Older adults;Home automation system;Smart city;Wi-Fi enabled micro-controller;Blynk App;Smart home technology;Internet of Things (IoT)},
   	doi={10.1109/iSES52644.2021.00023}}
   

@ARTICLE{Sharma2022,
	author={Sharma, Rashmi Priya and Ramesh, Dharavath and Pal, Pankaj and Tripathi, Sachin and Kumar, Chiranjeev},
	journal={IEEE Internet of Things Journal}, 
	title={{IoT-Enabled IEEE} 802.15.4 {WSN} Monitoring Infrastructure-Driven Fuzzy-Logic-Based Crop Pest Prediction}, 
	year={2022},
	volume={9},
	number={4},
	pages={3037-3045},
	keywords={Agriculture;Diseases;Monitoring;Humidity;Wireless sensor networks;Meteorology;Internet of Things;Fuzzy logic;genetic algorithm (GA);pest prediction;precision agriculture;WSN},
	doi={10.1109/JIOT.2021.3094198}}

    
    
    
    @ARTICLE{Ma2020,
    	author={Ma, Dong and Lan, Guohao and Hassan, Mahbub and Hu, Wen and Das, Sajal K.},
    	journal={IEEE Communications Surveys \& Tutorials}, 
    	title={{Sensing, computing, and communications for energy harvesting IoTs: A survey}}, 
    	year={2020},
    	volume={22},
    	number={2},
    	pages={1222-1250},
    	doi={10.1109/COMST.2019.2962526}
    }
    
    
    @ARTICLE{Mamat2023,
    	author={Mamat, Kritsada and Santipach, Wiroonsak},
    	journal={IEEE Transactions on Communications}, 
    	title={{Optimal transmit power and channel-information bit allocation with zeroforcing beamforming in MIMO-NOMA and MIMO-OMA downlinks}}, 
    	year={2023},
    	volume={71},
    	number={4},
    	pages={2028-2041},
    	keywords={Resource management;Array signal processing;NOMA;Transmitting antennas;Signal to noise ratio;Quantization (signal);Downlink;MIMO;NOMA;OMA;zeroforcing beamforming;transmit power allocation;CSI quantization;downlink;max-min fairness;geometric program},
    	doi={10.1109/TCOMM.2023.3240392}}
    
    
    
    @ARTICLE{Cannon2009,
    	author={Cannon, Benjamin L. and Hoburg, James F. and Stancil, Daniel D. and Goldstein, Seth Copen},
    	journal={IEEE Transactions on Power Electronics}, 
    	title={{Magnetic resonant coupling as a potential means for wireless power transfer to multiple small receivers}}, 
    	year={2009},
    	volume={24},
    	number={7},
    	pages={1819-1825},
    	doi={10.1109/TPEL.2009.2017195}
    }
    
    @ARTICLE{Gurakan2013,
    	author={Gurakan, Berk and Ozel, Omur and Yang, Jing and Ulukus, Sennur},
    	journal={IEEE Transactions on Communications}, 
    	title={{Energy cooperation in energy harvesting communications}}, 
    	year={2013},
    	volume={61},
    	number={12},
    	pages={4884-4898},
    	doi={10.1109/TCOMM.2013.110113.130184}
    }
    
    	
    	
    @ARTICLE{Guo2017,
    	author={Guo, Peng and Liu, Xuefeng and Tang, Shaojie and Cao, Jiannong},
    	journal={IEEE Transactions on Mobile Computing}, 
    	title={Concurrently Wireless Charging Sensor Networks with Efficient Scheduling}, 
    	year={2017},
    	volume={16},
    	number={9},
    	pages={2450-2463},
    	keywords={Wireless sensor networks;Electromagnetic interference;Genetic algorithms;Mobile communication;Greedy algorithms;Inductive charging;Energy harvesting;Wireless charging;wireless sensor networks (WSNs);scheduling;radio interference},
    	doi={10.1109/TMC.2016.2624731}}
    
    @ARTICLE{Gao2019,
    	author={Gao, Zhenguo and Chen, Danjie and Wu, Hsiao-Chun},
    	journal={IEEE Transactions on Vehicular Technology}, 
    	title={{Energy loss minimization for wireless power transfer based energy redistribution in WSNs}}, 
    	year={2019},
    	volume={68},
    	number={12},
    	pages={12271-12285},
    	keywords={Wireless sensor networks;Peer-to-peer computing;Energy loss;Schedules;Energy exchange;Optical wavelength conversion;Approximation algorithms;Wireless energy redistribution;wireless power transfer/charging;task scheduling problem},
    	doi={10.1109/TVT.2019.2946631}}
    	
 
    @ARTICLE{Gore2011,
    	author={Gore, Ashutosh Deepak and Karandikar, Abhay},
    	journal={IEEE Communications Surveys \& Tutorials}, 
    	title={Link Scheduling Algorithms for Wireless Mesh Networks}, 
    	year={2011},
    	volume={13},
    	number={2},
    	pages={258-273},
    	keywords={Scheduling algorithm;Wireless mesh networks;Time division multiple access;Signal to noise ratio;Costs;Spine;Telecommunication traffic;Throughput;Algorithm design and analysis;Interference;Spatial reuse;link scheduling;wireless networks;spatial TDMA},
    	doi={10.1109/SURV.2011.040510.00008}}
    	
    	
   @ARTICLE{He2018,
   	author={He, Qing and Yuan, Di and Ephremides, Anthony},
   	journal={IEEE Transactions on Information Theory}, 
   	title={Optimal Link Scheduling for Age Minimization in Wireless Systems}, 
   	year={2018},
   	volume={64},
   	number={7},
   	pages={5381-5394},
   	keywords={Optimal scheduling;Transmitters;Schedules;Wireless communication;Scheduling;Algorithm design and analysis;Information age;link scheduling;optimization;wireless networks},
   	doi={10.1109/TIT.2017.2746751}}
   
    	
  @ARTICLE{Wang2019,
  	author={Wang, Zijing and Qin, Xiaoqi and Liu, Baoling and Zhang, Ping},
  	journal={IEEE Wireless Communications Letters}, 
  	title={Joint Data Sampling and Link Scheduling for Age Minimization in Multihop Cyber-Physical Systems}, 
  	year={2019},
  	volume={8},
  	number={3},
  	pages={765-768},
  	keywords={Indexes;Minimization;Scheduling algorithms;Information age;Data integrity;Spread spectrum communication;Cyber-physical systems;Multi-hop cyber-physical system (CPS);age of information (AoI);data sampling;link scheduling},
  	doi={10.1109/LWC.2019.2891598}}
  	
  	
  	@ARTICLE{Choi2020,
  		author={Choi, Minseok and Molisch, Andreas F. and Kim, Joongheon},
  		journal={IEEE Transactions on Wireless Communications}, 
  		title={Joint Distributed Link Scheduling and Power Allocation for Content Delivery in Wireless Caching Networks}, 
  		year={2020},
  		volume={19},
  		number={12},
  		pages={7810-7824},
  		keywords={Wireless communication;Scheduling;Interference;Resource management;Peer-to-peer computing;Delays;Clustering algorithms;Belief propagation;link scheduling;power allocation;matching algorithm;wireless caching},
  		doi={10.1109/TWC.2020.3016562}}
  		
  		
  	@ARTICLE{Yu2020,
  		author={Yu, Jiguo and Yu, Kan and Yu, Dongxiao and Lv, Weifeng and Cheng, Xiuzhen and Chen, Honglong and Cheng, Wei},
  		journal={IEEE Transactions on Wireless Communications}, 
  		title={Efficient Link Scheduling in Wireless Networks Under Rayleigh-Fading and Multiuser Interference}, 
  		year={2020},
  		volume={19},
  		number={8},
  		pages={5621-5634},
  		keywords={Interference;Rayleigh channels;Approximation algorithms;Signal to noise ratio;Wireless communication;Receivers;Wireless network;link scheduling;Rayleigh-fading;interference model;distributed algorithms},
  		doi={10.1109/TWC.2020.2994998}}
  		
  	@ARTICLE{Yu2021,
  		author={Yu, Kan and Yu, Jiguo and Cheng, Xiuzhen and Yu, Dongxiao and Dong, Anming},
  		journal={IEEE/ACM Transactions on Networking}, 
  		title={Efficient Link Scheduling Solutions for the {Internet of Things} Under Rayleigh Fading}, 
  		year={2021},
  		volume={29},
  		number={6},
  		pages={2508-2521},
  		keywords={Interference;Rayleigh channels;Signal to noise ratio;Scheduling;Distributed algorithms;Scheduling algorithms;Partitioning algorithms;Link scheduling;Internet of Things;distributed algorithm;Rayleigh fading;SINR},
  		doi={10.1109/TNET.2021.3093306}}
  		
  	@ARTICLE{Zhao2023,
  		author={Zhao, Zhongyuan and Verma, Gunjan and Rao, Chirag and Swami, Ananthram and Segarra, Santiago},
  		journal={IEEE Transactions on Wireless Communications}, 
  		title={Link Scheduling Using Graph Neural Networks}, 
  		year={2023},
  		volume={22},
  		number={6},
  		pages={3997-4012},
  		keywords={Wireless networks;Processor scheduling;Complexity theory;Scheduling;Optimal scheduling;Ad hoc networks;Routing;MWIS;graph convolutional networks;wireless networks;scheduling;reinforcement learning},
  		doi={10.1109/TWC.2022.3222781}}
  		
  	@ARTICLE{Wang2021,
  		author={Wang, Luyao and Chin, Kwan-Wu and Soh, Sieteng},
  		journal={IEEE Transactions on Green Communications and Networking}, 
  		title={Link Schedulers for Green Wireless Networks With Energy Sharing}, 
  		year={2021},
  		volume={5},
  		number={3},
  		pages={1580-1593},
  		keywords={Schedules;Energy harvesting;Relays;Wireless networks;Signal to noise ratio;Protocols;Interference;RBC;wireless charging;heuristic;NP-hard;distributed protocol},
  		doi={10.1109/TGCN.2021.3081404}}
  		
  		
  	@ARTICLE{Cui2023,
  		author={Cui, Yuhan and Chin, Kwan-Wu and Soh, Sieteng and Ros, Montserrat},
  		journal={IEEE Internet of Things Journal}, 
  		title={{Novel task scheduling approaches in energy sharing solar-powered IoT networks}}, 
  		year={2023},
  		volume={10},
  		number={12},
  		pages={10970-10982},
  		keywords={Task analysis;Schedules;Radio frequency;Logic gates;Internet of Things;Energy harvesting;Resource management;Jobs flow;NP-hard;tasks;wireless charging},
  		doi={10.1109/JIOT.2023.3242995}}
  	
  		
  
@ARTICLE{Gurakan2016,
	author={Gurakan, Berk and Ozel, Omur and Ulukus, Sennur},
	journal={IEEE Transactions on Wireless Communications}, 
	title={Optimal Energy and Data Routing in Networks With Energy Cooperation}, 
	year={2016},
	volume={15},
	number={2},
	pages={857-870},
	keywords={Energy exchange;Routing;Delays;Energy harvesting;Wireless communication;Network topology;Joints;Energy cooperation;energy harvesting;wireless energy transfer;optimal routing;resource allocation;Energy cooperation;energy harvesting;wireless energy transfer;optimal routing;resource allocation},
	doi={10.1109/TWC.2015.2479626}}
	
	@ARTICLE{Varan2017,
		author={Varan, Burak and Yener, Aylin},
		journal={IEEE Transactions on Green Communications and Networking}, 
		title={{Matching games for ad hoc networks with wireless energy transfer}}, 
		year={2017},
		volume={1},
		number={4},
		pages={503-515},
		keywords={Receivers;Games;Transmitters;Energy exchange;Ad hoc networks;Wireless networks;Energy transfer;matching games;ad hoc networks;Vickrey auction;max-min fairness},
		doi={10.1109/TGCN.2017.2751643}}
	


    
  @ARTICLE{Jiao2020,
  	author={Jiao, Dongbin and Yang, Peng and Fu, Liqun and Ke, Liangjun and Tang, Ke},
  	journal={IEEE Internet of Things Journal}, 
  	title={{Optimal energy-delay scheduling for energy-harvesting WSNs with interference channel via negatively correlated search}}, 
  	year={2020},
  	volume={7},
  	number={3},
  	pages={1690-1703},
  	keywords={Optimization;Interference channels;Wireless sensor networks;Resource management;Delays;Data models;Capacity assignment problem;energy harvesting;interference channel;negatively correlated search (NCS);wireless sensor networks},
  	doi={10.1109/JIOT.2019.2954604}}
  	
@ARTICLE{Yin2020,
	author={Yin, Fangfang and Zeng, Minyin and Zhang, Zhilong and Liu, Danpu},
	journal={IEEE Transactions on Vehicular Technology}, 
	title={{Coded caching for smart grid enabled HetNets with resource allocation and energy cooperation}}, 
	year={2020},
	volume={69},
	number={10},
	pages={12058-12071},
	keywords={Smart grids;Resource management;Energy consumption;Renewable energy sources;Optimization;Green products;Simulation;Coded caching;energy cooperation;smart grid;matching;bandwidth allocation;user association},
	doi={10.1109/TVT.2020.3011518}}
  	
  @ARTICLE{Liang2020,
  	author={Liang, Hui and Zhao, Xiaohui and Li, Zan},
  	journal={IEEE Transactions on Vehicular Technology}, 
  	title={Optimal Energy Cooperation Policy in Fusion Center-Based Sustainable Wireless Sensor Networks}, 
  	year={2020},
  	volume={69},
  	number={6},
  	pages={6401-6414},
  	keywords={Wireless sensor networks;Wireless communication;Markov processes;Batteries;Energy harvesting;Throughput;Heuristic algorithms;Energy cooperation;Markov decision process;pairing algorithm;sustainable wireless sensor networks},
  	doi={10.1109/TVT.2020.2985704}}
 
  @ARTICLE{He2020,
  	author={He, Tengjiao and Chin, Kwan-Wu and Soh, Sieteng and Yang, Changlin},
  	journal={IEEE Transactions on Sustainable Computing}, 
  	title={On Optimizing Max Min Rate in Rechargeable Wireless Sensor Networks with Energy Sharing}, 
  	year={2020},
  	volume={5},
  	number={1},
  	pages={107-120},
  	keywords={Protocols;Wireless sensor networks;Routing;Radio frequency;Energy harvesting;Throughput;Relays;Linear program;optimization;data transmission and energy sharing protocol},
  	doi={10.1109/TSUSC.2018.2885049}}
   
   
   @ARTICLE{Li2020,
   	author={Li, Ya and Zhao, Xiaohui and Liang, Hui},
   	journal={IEEE Internet of Things Journal}, 
   	title={{Throughput maximization by deep reinforcement learning with energy cooperation for renewable ultradense IoT networks}}, 
   	year={2020},
   	volume={7},
   	number={9},
   	pages={9091-9102},
   	keywords={Reinforcement learning;Internet of Things;Throughput;Resource management;Base stations;Energy harvesting;Renewable energy sources;Deep reinforcement learning (DRL);energy cooperation;energy harvesting (EH);Internet of Things (IoT);renewable ultradense networks (UDNs);throughput maximization},
   	doi={10.1109/JIOT.2020.3002936}}
   
 @article{Patrik2004,
 	title = {{A column generation method for spatial TDMA scheduling in ad hoc networks}},
 	journal = {Ad Hoc Networks},
 	volume = {2},
 	number = {4},
 	pages = {405-418},
 	year = {2004},
 	issn = {1570-8705},
 	doi = {https://doi.org/10.1016/j.adhoc.2003.09.002},
 	author = {Patrik Björklund and Peter Värbrand and Di Yuan},
 }






@ARTICLE{Jiang2022,
	author={Jiang, Muchen and Chin, Kwan-Wu and He, Tengjiao and Soh, Sieteng and Wang, Luyao},
	journal={IEEE Internet of Things Journal}, 
	title={{Joint link scheduling and routing in two-tier RF-energy-harvesting IoT networks}}, 
	year={2022},
	volume={9},
	number={1},
	pages={800-812},
	keywords={Schedules;Routing;Interference;Sensors;Internet of Things;Wireless sensor networks;MIMO communication;Channel access;diversity gain;interference;optimization;protocol},
	doi={10.1109/JIOT.2021.3085862}}


@ARTICLE{Boshkovska2015,
	author={Boshkovska, Elena and Ng, Derrick Wing Kwan and Zlatanov, Nikola and Schober, Robert},
	journal={IEEE Communications Letters}, 
	title={{Practical non-linear energy harvesting model and resource allocation for SWIPT systems}}, 
	year={2015},
	volume={19},
	number={12},
	pages={2082-2085},
	keywords={Resource management;Wireless communication;Integrated circuit modeling;Receivers;Optimization;Wireless sensor networks;Simultaneous wireless information and power transfer;non-linear energy harvesting model},
	doi={10.1109/LCOMM.2015.2478460}}
	
@ARTICLE{Jiang2013,
	author={Jiang, Canming and Shi, Yi and Hou, Y. Thomas and Lou, Wenjing and Sherali, Hanif D.},
	journal={IEEE Transactions on Wireless Communications}, 
	title={{Throughput maximization for multi-hop wireless networks with network-wide energy constraint}}, 
	year={2013},
	volume={12},
	number={3},
	pages={1255-1267},
	keywords={Linear approximation;Throughput;Energy consumption;Optimized production technology;Spread spectrum communication;Energy optimization;total network energy;network throughput;multicriteria optimization;multi-hop wireless networks},
	doi={10.1109/TWC.2013.013013.120636}}
			
		
@ARTICLE{Peng2022,
	author={Peng, Xingxiang and Wu, Peiran and Tan, Hongzhou and Xia, Minghua},
	journal={IEEE Internet of Things Journal}, 
	title={{Optimization for IRS-assisted MIMO-OFDM SWIPT system with nonlinear EH model}}, 
	year={2022},
	volume={9},
	number={24},
	pages={25253-25268},
	keywords={Optimization;OFDM;Internet of Things;MIMO communication;Energy harvesting;Wireless communication;Simultaneous wireless information and power transfer;Intelligent reflecting surface (IRS);multiple-input and multiple-output (MIMO)-orthogonal frequency division multiplexing (OFDM);nonlinear energy harvesting (EH);power splitting (PS);Simultaneous wireless information and power transfer (SWIPT)},
	doi={10.1109/JIOT.2022.3195927}}
		
		
		
	
@ARTICLE{Shanin2022,
	author={Shanin, Nikita and Cottatellucci, Laura and Schober, Robert},
	journal={IEEE Transactions on Communications}, 
	title={{Optimal transmit strategy for multi-user MIMO WPT systems with non-linear energy harvesters}}, 
	year={2022},
	volume={70},
	number={3},
	pages={1726-1741},
	keywords={MIMO communication;Rectennas;Array signal processing;Integrated circuit modeling;MISO communication;Probability density function;Transmitting antennas;Wireless power transmission;energy harvesting (EH);multiple-input multiple-output (MIMO);signal deisgn;rectennas;rectifiers},
	doi={10.1109/TCOMM.2021.3134690}}



@ARTICLE{Gao2022,
  author={Gao, Juan and Wu, Runze and Hao, Jianhong and Xu, Chen and Guo, Haobo},
  journal={IEEE Sensors Journal}, 
  title={{SWIPT-based energy scheduling for solar-powered WSN in full-duplex mode}}, 
  year={2022},
  volume={22},
  number={13},
  pages={13668-13681},
  keywords={Wireless sensor networks;Sensors;Energy efficiency;Costs;Monitoring;Solar energy;Wireless communication;Solar-harvesting;SWIPT;full duplex mode;energy efficiency;self-sustainable},
  doi={10.1109/JSEN.2022.3174120}}

@ARTICLE{He2013,
  author={He, Shibo and Chen, Jiming and Jiang, Fachang and Yau, David K.Y. and Xing, Guoliang and Sun, Youxian},
  journal={IEEE Transactions on Mobile Computing}, 
  title={Energy Provisioning in Wireless Rechargeable Sensor Networks}, 
  year={2013},
  volume={12},
  number={10},
  pages={1931-1942},
  keywords={Wireless communication;Wireless sensor networks;Mathematical model;Antennas;Radiofrequency identification;RF signals;Wireless communication;Wireless sensor networks;Mathematical model;Antennas;Radiofrequency identification;RF signals;WISP;Wireless recharging;energy provisioning;sensor networks},
  doi={10.1109/TMC.2012.161}}

@ARTICLE{Ku2015,
  author={Ku, Meng-Lin and Chen, Yan and Liu, K. J. Ray},
  journal={IEEE Journal on Selected Areas in Communications}, 
  title={Data-Driven Stochastic Models and Policies for Energy Harvesting Sensor Communications}, 
  year={2015},
  volume={33},
  number={8},
  pages={1505-1520},
  keywords={Hidden Markov models;Energy harvesting;Batteries;Markov processes;Bit rate;Modulation;Energy harvesting;solar-powered communication;stochastic data-driven model;Markov decision process;transmission policy;Energy harvesting;solar-powered communication;stochastic data-driven model;Markov decision process;transmission policy},
  doi={10.1109/JSAC.2015.2391651}}


@INPROCEEDINGS{Nishimura2021,
  author={Nishimura, F. and Hirai, Y. and Kamitani, A. and Tanaka, A. and Utsunomiya, F. and Nishikawa, H. and Douseki, T.},
  booktitle={Proc. 2021 IEEE Sensors}, 
  title={Alive Monitoring Sensor System with 2.45-{GHz} Wireless Power Transfer for Self-powered Wireless Sensor}, 
  year={2021},
  volume={},
  number={},
  pages={1-4},
  keywords={Wireless communication;Wireless sensor networks;Rectennas;Dipole antennas;Transmitting antennas;Receiving antennas;Wireless power transfer;Alive monitoring;Wireless power transfer;Harmonic wave;Self-powered wireless sensor;Water-activated battery},
  doi={10.1109/SENSORS47087.2021.9639464}}


@ARTICLE{Lu2015,
  author={Lu, Xiao and Wang, Ping and Niyato, Dusit and Kim, Dong In and Han, Zhu},
  journal={IEEE Communications Surveys \& Tutorials}, 
  title={Wireless Networks With {RF} Energy Harvesting: A Contemporary Survey}, 
  year={2015},
  volume={17},
  number={2},
  pages={757-789},
  keywords={Radio frequency;Energy harvesting;Wireless sensor networks;Wireless communication;Energy exchange;RF signals;Couplings;RF energy harvesting;simultaneous wireless information and power transfer (SWIPT);receiver operation policy;beamforming;communication protocols;RF-powered Cognitive radio network;RF-powered Cognitive radio network;simultaneous wireless information and power transfer (SWIPT);receiver operation policy;beamforming;communication protocols;RF energy harvesting;magnetic MIMO},
  doi={10.1109/COMST.2014.2368999}}


@ARTICLE{Zhang2022,
  author={Zhang, Longji and Chin, Kwan-Wu and Wang, Luyao and Yang, Changlin},
  journal={IEEE Internet of Things Journal}, 
  title={Complete Targets Coverage in Energy-Harvesting {IoT} Networks With Dual Imperfect Batteries}, 
  year={2022},
  volume={9},
  number={8},
  pages={6199-6212},
  keywords={Batteries;Internet of Things;Energy harvesting;Sensors;Degradation;Transmitters;Throughput;Battery imperfections;heuristics;mathematical optimization;sensing},
  doi={10.1109/JIOT.2021.3109148}}


@ARTICLE{Sudevalayam2011,
  author={Sudevalayam, Sujesha and Kulkarni, Purushottam},
  journal={IEEE Communications Surveys and Tutorials}, 
  title={Energy Harvesting Sensor Nodes: Survey and Implications}, 
  year={2011},
  volume={13},
  number={3},
  pages={443-461},
  keywords={Batteries;Costs;Monitoring;Energy storage;Computer networks;Routing protocols;Energy states;Sensor systems and applications;Collaboration;Embedded computing;Sensor networks;Energy-aware systems;Energy harvesting},
  doi={10.1109/SURV.2011.060710.00094}}


@ARTICLE{Asiedu2019,
  author={Asiedu, Derek Kwaku Pobi and Lee, Hoon and Lee, Kyoung-Jae},
  journal={IEEE Internet of Things Journal}, 
  title={{Simultaneous wireless information and power transfer for decode-and-forward multihop relay systems in energy-constrained IoT networks}}, 
  year={2019},
  volume={6},
  number={6},
  pages={9413-9426},
  keywords={Relays;Sensors;Wireless sensor networks;Wireless communication;Internet of Things;Throughput;Antennas;Multihop decode-and-forward (DF) relays;power splitting (PS) ratio;simultaneous wireless information and power transfer (SWIPT);source transmit power minimization;system rate maximization},
  doi={10.1109/JIOT.2019.2937090}}

@ARTICLE{Choi2021,
  author={Choi, Hyun-Ho and Lee, Kisong},
  journal={IEEE Transactions on Vehicular Technology}, 
  title={Cooperative Wireless Power Transfer for Lifetime Maximization in Wireless Multihop Networks}, 
  year={2021},
  volume={70},
  number={4},
  pages={3984-3989},
  keywords={Spread spectrum communication;Wireless sensor networks;Wireless communication;Optimization;Batteries;Mobile ad hoc networks;RF signals;Cooperation;lifetime maximization;multihop network;wireless energy harvesting;wireless power transfer},
  doi={10.1109/TVT.2021.3068345}}

   @ARTICLE{Gao2015,
    	author={Gao, Zhenguo and Chen, Danjie and Chen, Bingcai and Lu, Zhimao and Lu, Zhimao},
    	journal={Wireless Personal Communications}, 
    	title={{Outage probability equivalency of three typical relay selection schemes for selective DF relay networks with selection combining}}, 
    	year={2015},
    	volume={85},
    	number={3},
    	pages={1205-1215},
    	doi={10.1007/s11277-015-2835-y}}

@ARTICLE{Liu2022,
  author={Liu, Wai-Xi and Lu, Jinjie and Cai, Jun and Zhu, Yinghao and Ling, Sen and Chen, Qingchun},
  journal={IEEE Transactions on Network and Service Management}, 
  title={{DRL-PLink: Deep reinforcement learning with private link approach for mix-flow scheduling in software-defined data-center networks}}, 
  year={2022},
  volume={19},
  number={2},
  pages={1049-1064},
  keywords={Reinforcement learning;Bandwidth;Switches;Schedules;Computer networks;Optimization;Dynamic scheduling;Deep reinforcement learning;mix-flow scheduling;private link;software-defined networks;data center networks},
  doi={10.1109/TNSM.2021.3128267}}


@ARTICLE{Abbasalizadeh2024,
  author={Abbasalizadeh, Maryam and Vellamchety, Krishnaa and Rayavaram, Pranathi and Narain, Sashank},
  journal={IEEE Open Journal of the Communications Society}, 
  title={{Dynamic link scheduling in wireless networks through fuzzy-enhanced deep learning}}, 
  year={2024},
  volume={5},
  number={},
  pages={6832-6848},
  keywords={Interference;Wireless networks;Dynamic scheduling;Deep learning;Training;Layout;Heuristic algorithms;Fuzzy logic;Scheduling algorithms;Resource management;Link scheduling;fuzzy logic;deep learning optimization;link quality probability},
  doi={10.1109/OJCOMS.2024.3484948}}




@ARTICLE{Wang2024,
  author={Wang, Guanhua and Yang, Fang and Song, Jian and Han, Zhu},
  journal={IEEE Transactions on Communications}, 
  title={{Optimization for dynamic laser inter-satellite link scheduling with routing: A multi-agent deep reinforcement learning approach}}, 
  year={2024},
  volume={72},
  number={5},
  pages={2762-2778},
  keywords={Satellites;Dynamic scheduling;Routing;Satellite broadcasting;Delays;Orbits;Low earth orbit satellites;Laser inter-satellite link;dynamic link;multi-agent deep reinforcement learning;link scheduling;routing strategy},
  doi={10.1109/TCOMM.2023.3347775}}




@ARTICLE{Ma2021,
  author={Ma, Kai and Li, Zhixue and Liu, Pei and Yang, Jie and Geng, Yafei and Yang, Bo and Guan, Xinping},
  journal={IEEE Internet of Things Journal}, 
  title={{Reliability-constrained throughput optimization of industrial wireless sensor networks with energy harvesting relay}}, 
  year={2021},
  volume={8},
  number={17},
  pages={13343-13354},
  keywords={Relays;Energy harvesting;Production;Wireless sensor networks;Throughput;Radio frequency;Resource management;Industrial wireless sensor networks (IWSNs);multiproduction monitoring area;power allocation;radio-frequency (RF) energy harvesting (EH);successive convex approximation (SCA)},
  doi={10.1109/JIOT.2021.3065966}
}




@ARTICLE{Gao2020,
  author={Gao, Zhenguo and Chen, Danjie and Wu, Hsiao Chun},
  journal={IEEE Transactions on Green Communications and Networking}, 
  title={{Graph coloring inspired approximate algorithm for wireless energy redistribution in WSNs}}, 
  year={2020},
  volume={4},
  number={1},
  pages={124-138},
  keywords={Wireless sensor networks;Schedules;Energy loss;Approximation algorithms;Optical wavelength conversion;Energy exchange;Wireless communication;Energy redistribution;wireless power transfer/charging;task scheduling problem;graph coloring problem},
  doi={10.1109/TGCN.2019.2947172}}




@ARTICLE{Gao2023,
  author={Gao, Zhenguo and Chen, Yan and Fan, Liling and Wang, Haijun and Huang, Scott Chih-Hao and Wu, Hsiao-Chun},
  journal={IEEE Internet of Things Journal}, 
  title={{Joint energy loss and time span minimization for energy-redistribution-assisted charging of WRSNs with a mobile charger}}, 
  year={2023},
  volume={10},
  number={5},
  pages={4636-4651},
  keywords={Energy loss;Schedules;Minimization;Approximation algorithms;Wireless sensor networks;Wireless communication;Task analysis;charge scheduling;energy loss;energy redistribution (ERD);ime span;Wireless rechargeable sensor network (WRSN)},
  doi={10.1109/JIOT.2022.3219061}}


@ARTICLE{Liu2019,
  author={Liu, Wei and Chau, K. T. and Lee, Christopher H. T. and Jiang, Chaoqiang and Han, Wei and Lam, W. H.},
  journal={IEEE Transactions on Magnetics}, 
  title={Multi-Frequency Multi-Power One-to-Many Wireless Power Transfer System}, 
  year={2019},
  volume={55},
  number={7},
  pages={1-9},
  keywords={Transmitters;Receivers;Wireless communication;Harmonic analysis;Inverters;Resonant frequency;Impedance;Multi-frequency multi-power (MFMP);one to many;single transmitter;wireless power transfer (WPT)},
  doi={10.1109/TMAG.2019.2896468}}


@ARTICLE{Ieperen2024,
  author={van Ieperen, Aris and Derammelaere, Stijn and Minnaert, Ben},
  journal={IEEE Open Journal of Power Electronics}, 
  title={Coupling-Independent Capacitive Wireless Power Transfer With One Transmitter and Multiple Receivers Using Frequency Bifurcation}, 
  year={2024},
  volume={5},
  number={},
  pages={891-901},
  keywords={Bifurcation;Resonant frequency;Receivers;Couplings;Transmitters;Wireless power transfer;Admittance;Capacitive wireless power transfer;frequency bifurcation;single input multiple outputs},
  doi={10.1109/OJPEL.2024.3414172}}

@book{neely2010stochastic,
  title={Stochastic network optimization with application to communication and queueing systems},
  author={Neely, Michael J},
  year={2010},
  publisher={Morgan \& Claypool Publishers}
}

@ARTICLE{Gao2024SWIPT,
  author={Gao, Zhenguo and Wang, Haijun and Chen, Yan and Fan, Liling and Zhao, Rui},
  journal={IEEE Internet of Things Journal}, 
  title={Maximizing Long-Term Average System Communication Capacity of SWIPT-Enabled AF Relay System via Lyapunov Optimization}, 
  year={2024},
  volume={11},
  number={3},
  pages={4679-4692},
  abstract={For a simultaneous wireless information and power transfer-enabled single amplify-and-forward relay communication system, we investigate the relay’s transmission power management problem for maximizing the long-term average (LTA) system communication capacity while guaranteeing some constraints on LTA battery energy for the relay and destination. Because LTA expressions are involved in this problem, the Lyapunov optimization framework is adopted. We first formulate the problem, and then we construct two virtual queues for the battery energy constraints of the relay and the destination. Next, by constructing a drift-plus-penalty function that combines the original objective function and the LTA constraints, the original problem is transformed into a global optimization problem without the LTA constraints. The new global optimization problem is further transformed into a slotwise local optimization problem by replacing the objective function with an upper bound expression involving only the current time slot. We propose an algorithm named LTA communication capacity optimization based on Lyapunov optimization (LTCOL), which runs online to determine the relay’s transmission power slot by slot through solving slotwise local problems. Some important properties of LTCOL, including guaranteed LTA constraints and an assured controllable lower bound on system communication capacity, are provided and proved. Simulation results demonstrate the superiority of LTCOL over existing algorithms.},
  keywords={Relays;Optimization;Batteries;RF signals;Wireless sensor networks;Wireless communication;Internet of Things;Amplify-and-forward (AF) relay system;energy management;Lyapunov optimization;simultaneous wireless information and power transfer (SWIPT);system communication capacity},
  doi={10.1109/JIOT.2023.3299560},
  ISSN={2327-4662},
  month={Feb}
}

@book{meyn2012markov,
  title={Markov chains and stochastic stability},
  author={Meyn, Sean P and Tweedie, Richard L},
  year={2012},
  publisher={Springer Science \& Business Media}
}

