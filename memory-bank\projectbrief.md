# 项目简报

*此文件是 Memory Bank 的基础。它定义了项目的核心需求和目标，并作为项目范围的真实来源。*

## 核心需求

*   设计并实现一个调度算法，用于能量收集无线传感器网络 (EH-WSN)。
*   该算法需要联合优化数据传输和能量协作。
*   主要目标是最大化网络的长期平均 (LTA) 吞吐量。
*   必须保证数据队列的稳定性和节点的 LTA 能量维持（高于阈值）。

## 项目目标

*   提出一种基于 Lyapunov 优化的在线调度算法 (Lyapunov-MEC)，结合多播能量协作 (M-EC)。
*   对所提出算法进行理论分析，证明其稳定性并推导性能界限。
*   通过仿真验证算法的有效性，并与相关基准算法进行性能比较。
*   完成一篇高质量的学术论文，详细阐述系统模型、问题、算法、分析和结果。
*   目标是在 IEEE 一区顶级期刊上发表该论文。

## 范围

*   研究范围集中在静态 EH-WSN 场景。
*   考虑半双工通信、Rician 衰落信道、非线性 RF 能量收集模型。
*   主要关注 LTA 性能指标（吞吐量、队列稳定性、能量维持）。
*   开发和分析 Lyapunov-MEC 算法及其变种。
*   进行仿真实验以评估性能并进行参数影响分析。
*   不包括移动节点、复杂的干扰模型或具体的硬件实现细节。
