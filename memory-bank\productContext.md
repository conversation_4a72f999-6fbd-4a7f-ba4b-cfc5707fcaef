# 产品背景

*此文件描述了项目存在的原因、它解决的问题、它应该如何工作以及用户体验目标。*

## 项目存在的原因

*   能量收集无线传感器网络 (EH-WSN) 在持续监测应用中具有巨大潜力，但其性能受到节点能量有限且不稳定的限制。
*   传统的调度方法可能无法有效利用节点间的能量差异或机会性地共享能量，导致网络性能受限或节点过早失效。
*   需要一种能够智能地联合调度数据传输和能量协作的机制，以最大化网络效用（如吞吐量）并保证长期稳定运行。

## 解决的问题

*   **最大化吞吐量：** 在能量收集和消耗的动态约束下，最大化网络成功传输到汇聚节点的数据总量。
*   **保证稳定性：** 防止数据在节点缓冲区无限积压（队列稳定性），并确保节点长期维持足够的能量水平以持续工作（LTA 能量约束）。
*   **联合调度复杂性：** 解决同时决定哪些节点传输数据、哪些节点传输/接收能量、以及使用多少功率的复杂优化问题，特别是引入了多播能量协作 (M-EC) 的场景。

## 工作原理

*   采用 **Lyapunov 优化框架**，将长期随机优化问题转化为一系列逐时隙的确定性优化问题。
*   利用 **数据队列** 和 **虚拟能量赤字队列** 来跟踪系统状态（数据积压和能量不足程度）。
*   在每个时隙，**Lyapunov-MEC 算法** 根据当前的队列状态、电池电量和信道条件，做出在线决策：
    1.  **能量协作阶段：** 启发式地选择能量发送节点和传输功率，优先考虑能量充足且能量赤字小的节点向能量匮乏且能量赤字大的邻居进行多播能量传输 (M-EC)。
    2.  **数据传输阶段：** 启发式地选择激活的数据链路、传输功率（动态计算以满足最低 SNR 阈值，即 $p_{\text{required\_min\_W}}$）和数据流，优先考虑能够最大化“加权数据传输效益”（考虑队列差异和吞吐量贡献 $V$）同时最小化“加权能量成本”（考虑发送/接收能耗和能量赤字）的链路。
*   目标是近似最小化 **Lyapunov 漂移加惩罚函数**，从而隐式地最大化吞吐量并稳定所有队列。

## 用户体验目标

*   **高吞吐量：** 实现尽可能高的长期平均数据传输速率。
*   **网络稳定性：** 确保数据队列和能量水平保持稳定，避免数据丢失或节点失效。
*   **可持续运行：** 满足长期平均能量维持要求，保证网络的持续工作能力。
*   **算法效率：** 提出的 Lyapunov-MEC 算法应具有较低的计算复杂度，适合在线实现。
