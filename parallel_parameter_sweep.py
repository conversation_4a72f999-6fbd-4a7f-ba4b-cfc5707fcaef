# parallel_parameter_sweep.py
# This script will implement parallel execution of parameter sweeps for the simulation.

import multiprocessing
import time
import numpy as np
import matplotlib.pyplot as plt
import os
import copy
import itertools
import simulation
import comparison_algorithms

# --- Sweep Configuration (Copied from parameter_sweep.py) ---
# Define multiple parameter sweeps
# TODO: Restore all sweep configurations for parallel execution
SWEEP_CONFIGS = [
    {
        'param_name': 'BATTERY_MAX_J',
        'param_values': [1.0, 2.0, 3.0, 4.0, 5.0, 10.0, 15.0, 20.0, 25.0], # Restored full range from parameter_sweep.py
        'xlabel': 'Maximum Battery Capacity (J)',
        'title_suffix': 'vs. Battery Capacity',
        'is_scale_factor': False
    },
    {
        'param_name': 'V_CONTROL',
        'param_values': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], # Restored full range from parameter_sweep.py
        'xlabel': 'Lyapunov Control Parameter (V)',
        'title_suffix': 'vs. Lyapunov V',
        'is_scale_factor': False
    },
    {
        'param_name': 'NUM_NODES',
        'param_values': [20, 30, 40, 50], # Restored NUM_NODES sweep
        'xlabel': 'Number of Nodes (N)',
        'title_suffix': 'vs. Number of Nodes',
        'is_scale_factor': False
    },
]

# Algorithms to compare in the sweep
ALGORITHMS_TO_COMPARE = {
    "Lyapunov-MEC": simulation.lyapunov_mec_scheduling,
    "Lyapunov-NoEC": simulation.lyapunov_noec_scheduling,
    "Lyapunov-UEC": simulation.lyapunov_uec_scheduling, # Assuming UEC is available in simulation.py
    "GMW": comparison_algorithms.gmw_scheduling,
    "EAG": comparison_algorithms.eag_scheduling,
    "RAND": comparison_algorithms.rand_scheduling,
    "GDTS": comparison_algorithms.gdts_scheduling,           # 新集成的GDTS算法 (参数已修正)
    "GDTS-NoEC": comparison_algorithms.gdts_no_ec_scheduling # 新集成的GDTS无能量合作算法 (参数已修正)
}

# Number of simulation runs to average for each parameter setting
NUM_SWEEP_RUNS = 30 # Restored to 30 for reliable results (original value from parameter_sweep.py)

# Plotting configurations (Copied from parameter_sweep.py)
LINESTYLES = ['-', '--', '-.', ':', (0, (3, 1, 1, 1)), (0, (5, 10)), (0, (3, 5, 1, 5)), (0, (1, 1))]
MARKERS = ['o', 's', '^', 'v', 'd', 'x', 'p', '*'] # circle, square, triangle_up, triangle_down, diamond, x, pentagon, star
ALGO_NAMES_ORDERED = list(ALGORITHMS_TO_COMPARE.keys()) # Get a fixed order of algorithm names

# --- Parameter Management Functions (Copied and adapted from parameter_sweep.py) ---
# These functions are primarily for the main process to manage baseline simulation parameters
# when switching between different sweep configurations.
# Child processes in the pool will receive a full copy of parameters for each task.

original_simulation_parameters = {}
def store_original_parameters():
    """Stores the initial values of parameters that might be changed by sweeps in the main process."""
    global original_simulation_parameters
    # Define parameters that are globally set in simulation.py and might be altered by a sweep's setup.
    # This list should cover all parameters listed in SWEEP_CONFIGS 'param_name'.
    params_to_store = [
        'P_MIN_dBm', 'P_MAX_dBm', 'P_MIN_W', 'P_MAX_W',
        'ARRIVAL_PROB', 'PACKET_SIZE_MEAN_Mbit', 'PACKET_SIZE_MEAN_bits',
        'ENV_EH_MEAN_W', 'NUM_NODES', 'BATTERY_MAX_J', 'V_CONTROL',
        'TOTAL_TIME_SLOTS', 'SIMULATION_TIME_SLOTS', 'NUM_CHANNELS', 'SLOT_DURATION_S',
        'CARRIER_FREQ_GHz', 'PATH_LOSS_EXPONENT', 'RICIAN_FACTOR_dB',
        'NOISE_POWER_dBm_Hz', 'NOISE_POWER_W_Hz', 'BANDWIDTH_MHz', 'NOISE_FIGURE_dB',
        'TOTAL_NOISE_POWER_dBm', 'TOTAL_NOISE_POWER_W', 'SNR_THRESHOLD_dB', 'SNR_THRESHOLD_LINEAR',
        'MAX_HOPS', 'MAX_QUEUE_SIZE_PACKETS', 'ENERGY_COST_TX_J_PER_SLOT',
        'ENERGY_COST_RX_J_PER_SLOT', 'ENERGY_COST_EC_TX_J_PER_SLOT', 'ENERGY_COST_EC_RX_J_PER_SLOT',
        'ENERGY_COST_SENSING_J_PER_SLOT', 'RF_EH_EFFICIENCY', 'RF_EH_EFFICIENCY_DATA_SIG',
        'EH_HARVESTER_NONLINEAR_A', 'EH_HARVESTER_NONLINEAR_B', 'EH_HARVESTER_NONLINEAR_M',
        'LTA_ENERGY_THRESHOLD_FACTOR', 'DELTA_I', 'INITIAL_BATTERY_J',
        'GRID_SIZE', 'NODE_POSITIONS', 'SINK_POSITIONS', 'DISTANCE_MATRIX',
        'NEIGHBOR_THRESHOLD_M', 'NEIGHBOR_MATRIX', 'SINK_NEIGHBOR_MATRIX',
        'EH_MARKOV_STATES_W', 'EH_MARKOV_TRANSITION_PROB'
        # Add any other global simulation parameters that might be touched.
    ]
    # Filter to only store parameters that actually exist in the simulation module
    for param in params_to_store:
        if hasattr(simulation, param):
            value = getattr(simulation, param)
            original_simulation_parameters[param] = copy.deepcopy(value) # Use deepcopy for safety
    print("Stored original simulation parameters for the main process.")

def restore_all_original_parameters():
    """Restores all parameters in the main process to their initially stored values."""
    global original_simulation_parameters
    if not original_simulation_parameters:
        print("Warning: No original parameters were stored. Cannot restore.")
        return
    print("Restoring ALL original simulation parameters in the main process...")
    for param_name, value in original_simulation_parameters.items():
        if hasattr(simulation, param_name):
            setattr(simulation, param_name, copy.deepcopy(value)) # Use deepcopy for safety
    print("Restored original simulation parameters in the main process.")

# --- Helper to update parameters and dependencies (for main process before starting a sweep type) ---
# This is less critical for parallel execution if each task gets all params,
# but can be useful for setting up the baseline for a sweep type.
def update_simulation_parameter_main_process(param_name, value):
    """Updates a parameter in the simulation module (main process) and its known dependencies."""
    print(f"  Main Process: Setting {param_name} = {value}")
    setattr(simulation, param_name, value)

    # Update known dependencies (mirroring logic from parameter_sweep.py)
    if param_name == 'P_MIN_dBm':
        new_p_min_w = 10**(value / 10) / 1000
        setattr(simulation, 'P_MIN_W', new_p_min_w)
    elif param_name == 'P_MAX_dBm':
         new_p_max_w = 10**(value / 10) / 1000
         setattr(simulation, 'P_MAX_W', new_p_max_w)
    elif param_name == 'PACKET_SIZE_MEAN_Mbit':
         new_mean_bits = value * 1e6
         setattr(simulation, 'PACKET_SIZE_MEAN_bits', new_mean_bits)
    elif param_name == 'BATTERY_MAX_J':
         new_delta_i = simulation.LTA_ENERGY_THRESHOLD_FACTOR * value
         setattr(simulation, 'DELTA_I', new_delta_i)
    elif param_name == 'NUM_NODES':
        # This requires careful handling. If NUM_NODES changes, many dependent
        # parameters (NODE_POSITIONS, DISTANCE_MATRIX, etc.) need re-initialization.
        # The simulation.initialize_network() function is expected to handle this
        # when called within run_single_simulation.
        # For the main process, just setting it might be enough if child processes
        # always re-initialize.
        print(f"    NUM_NODES changed to {value}. Ensure simulation.initialize_network() is called in workers.")
        pass
    # Add other dependencies as needed.

# --- Parallel Task Execution Unit ---
def execute_single_run(task_args):
    """
    Worker function to execute a single simulation run.
    This function will be called by each process in the multiprocessing pool.

    Args:
        task_args (tuple): A tuple containing:
            - param_name_being_swept (str): Name of the parameter being swept.
            - current_param_value (any): Current value of the swept parameter.
            - algo_name (str): Name of the algorithm being run.
            - scheduling_function (callable): The scheduling function for the algorithm.
            - run_index (int): The index of the current run (for seeding).
            - base_seed (int): A base seed for random number generation.
            - all_original_sim_params_dict (dict): A dictionary of all original simulation parameters.
                                                  This ensures each worker starts with a clean slate.
    Returns:
        dict: A dictionary containing the results:
              {'throughput': float, 'status': str, 'params': dict, 'algo_name': str, 'param_value': any, 'run_idx': int}
              'status' can be 'success' or 'error'.
              'params' can store the specific (swept_param_name, value) for easy aggregation.
    """
    param_name_being_swept, current_param_value, algo_name, \
    scheduling_function, run_index, base_seed, all_original_sim_params_dict = task_args

    # 1. Restore all simulation parameters to their original state for this worker
    # This is crucial for isolation between tasks.
    for p_name, p_val in all_original_sim_params_dict.items():
        if hasattr(simulation, p_name):
            setattr(simulation, p_name, copy.deepcopy(p_val)) # Deepcopy for safety

    # 2. Apply the specific parameter value for the current sweep point
    #    and update its dependencies within this worker's context.
    #    This mimics the behavior of update_simulation_parameter_main_process but locally.
    if hasattr(simulation, param_name_being_swept):
        setattr(simulation, param_name_being_swept, current_param_value)
        # Update known dependencies based on the swept parameter
        if param_name_being_swept == 'P_MIN_dBm':
            new_p_min_w = 10**(current_param_value / 10) / 1000
            setattr(simulation, 'P_MIN_W', new_p_min_w)
        elif param_name_being_swept == 'P_MAX_dBm':
            new_p_max_w = 10**(current_param_value / 10) / 1000
            setattr(simulation, 'P_MAX_W', new_p_max_w)
        elif param_name_being_swept == 'PACKET_SIZE_MEAN_Mbit':
            new_mean_bits = current_param_value * 1e6
            setattr(simulation, 'PACKET_SIZE_MEAN_bits', new_mean_bits)
        elif param_name_being_swept == 'BATTERY_MAX_J':
            # Ensure LTA_ENERGY_THRESHOLD_FACTOR is from the restored original params
            lta_factor = all_original_sim_params_dict.get('LTA_ENERGY_THRESHOLD_FACTOR', simulation.LTA_ENERGY_THRESHOLD_FACTOR)
            new_delta_i = lta_factor * current_param_value
            setattr(simulation, 'DELTA_I', new_delta_i)
        elif param_name_being_swept == 'NUM_NODES':
            # The simulation.initialize_network() called within run_single_simulation
            # should handle the re-initialization of NUM_NODES dependent structures.
            pass # No direct update here, handled by initialize_network
        # Add other dependencies if param_name_being_swept matches them
    else:
        # This case should ideally not happen if SWEEP_CONFIGS are correct
        print(f"Warning: Worker for {algo_name}, {param_name_being_swept}={current_param_value}, run {run_index}: "
              f"Parameter {param_name_being_swept} not found in simulation module.")
        # Fallback: try to set it anyway, might be a new parameter not in the default list
        try:
            setattr(simulation, param_name_being_swept, current_param_value)
        except Exception as e_set:
            print(f"Error setting fallback param {param_name_being_swept} in worker: {e_set}")


    # 3. Manage random seed for reproducibility and independence
    #    Each unique combination of (param_value, algo_name, run_index) should have a unique seed.
    #    A simple way is to combine base_seed with these identifiers.
    #    The exact method of combining can be refined.
    #    Using hash for simplicity, but ensure it's deterministic.
    #    A more robust approach might be: seed = base_seed + run_index + hash(algo_name) + hash(str(current_param_value))
    #    For now, a simpler combination:
    process_seed = base_seed + run_index + sum(ord(c) for c in algo_name) + int(os.getpid())
    np.random.seed(process_seed)
    # Note: simulation.py might also set its own internal seeds.
    # Ensure simulation.initialize_network() or run_single_simulation() uses this seed if needed.
    # The current simulation.py seems to use np.random directly.

    # 4. Call the simulation
    try:
        # Ensure simulation.initialize_network() is called if NUM_NODES or other topology params changed.
        # The current run_single_simulation structure calls initialize_network at its start.
        sim_result = simulation.run_single_simulation(scheduling_function)
        throughput = sim_result['final_avg_throughput_mbps']
        # delay = sim_result['final_avg_packet_delay'] # If collecting delay
        return {
            'throughput': throughput,
            'status': 'success',
            'params': {param_name_being_swept: current_param_value},
            'algo_name': algo_name,
            'param_value': current_param_value, # For easier sorting/grouping later
            'run_idx': run_index
        }
    except Exception as e:
        print(f"ERROR in worker: Algo={algo_name}, Param={param_name_being_swept}={current_param_value}, Run={run_index}: {e}")
        # import traceback
        # traceback.print_exc() # For more detailed error in worker output
        return {
            'throughput': np.nan,
            'status': 'error',
            'error_message': str(e),
            'params': {param_name_being_swept: current_param_value},
            'algo_name': algo_name,
            'param_value': current_param_value,
            'run_idx': run_index
        }

# --- Main Parallel Control Logic ---
if __name__ == "__main__":
    multiprocessing.freeze_support() # Good practice for Windows compatibility

    overall_start_time = time.time()
    anova_raw_data_collection = {} # Initialize dict to store raw data for ANOVA
    
    # Restore original simulation duration for comprehensive analysis
    print("Using original simulation parameters for comprehensive analysis...")
    # simulation.TOTAL_TIME_SLOTS should remain at its original value (10000)
    print(f"TOTAL_TIME_SLOTS: {simulation.TOTAL_TIME_SLOTS}")

    # Store initial state of simulation parameters in the main process
    # This dictionary will be passed to each worker.
    store_original_parameters()
    # Make a copy of the stored original parameters to pass to workers,
    # ensuring the global original_simulation_parameters in the main process
    # isn't inadvertently modified if a worker somehow could.
    # And also, this ensures that the dict passed to workers is the one captured
    # *before* any main-process modifications for specific sweep types.
    main_process_original_params_snapshot = copy.deepcopy(original_simulation_parameters)

    all_sweep_results_data = {} # Store results for all sweeps

    # 1. Determine parallelism
    try:
        num_processes = os.cpu_count()
        print(f"Detected {num_processes} CPU cores. Using this for parallelism.")
    except NotImplementedError:
        num_processes = 4 # Fallback if cpu_count() is not available
        print(f"Could not detect CPU cores. Defaulting to {num_processes} processes.")

    # Generate a base seed for this entire script execution
    # This helps in making the overall sequence of random numbers reproducible
    # if the script is run again, while still allowing workers to have unique seeds.
    script_base_seed = int(time.time()) # Or a fixed integer for full reproducibility

    # 2. Main loop: Iterate over each sweep configuration
    for config_idx, config in enumerate(SWEEP_CONFIGS):
        param_name_being_swept = config['param_name']
        param_values_to_sweep = config['param_values']
        xlabel = config['xlabel']
        title_suffix = config['title_suffix']
        # is_scale_factor = config.get('is_scale_factor', False) # Not directly used in parallel worker logic this way

        print(f"\n--- Starting PARALLEL Parameter Sweep for: {param_name_being_swept} ---")
        print(f"Values: {param_values_to_sweep}")
        print(f"Algorithms: {list(ALGORITHMS_TO_COMPARE.keys())}")
        print(f"Runs per setting: {NUM_SWEEP_RUNS}")

        sweep_start_time = time.time()

        # Before starting tasks for this specific sweep config, if there's any
        # global setup needed in the simulation module based on param_name_being_swept
        # (e.g., if it's 'NUM_NODES' and some main-process global needs to reflect the largest N),
        # it could be done here. However, the worker `execute_single_run` is designed
        # to be self-contained by restoring all_original_sim_params_dict and then setting
        # the specific current_param_value.
        # For safety, restore main process to pristine state before generating tasks for this sweep type.
        if config_idx > 0: # No need to restore before the first sweep
             restore_all_original_parameters() # Restores main process's simulation module
        # And then, if this sweep type requires a specific baseline (e.g. NUM_NODES = 50 for a V_CONTROL sweep)
        # it would be set here using update_simulation_parameter_main_process.
        # For now, assume workers handle all necessary parameter states.

        # 3. Generate task list for the current sweep configuration
        all_tasks_for_current_sweep_config = []
        for p_value in param_values_to_sweep:
            for algo_name, sched_func in ALGORITHMS_TO_COMPARE.items():
                for r_idx in range(NUM_SWEEP_RUNS):
                    # task_arg: (param_name_being_swept, current_param_value, algo_name,
                    #            scheduling_function, run_index, base_seed, all_original_sim_params_dict)
                    task = (
                        param_name_being_swept,
                        p_value,
                        algo_name,
                        sched_func,
                        r_idx, # run_index
                        script_base_seed, # base_seed for reproducibility
                        main_process_original_params_snapshot # Pass the snapshot of original params
                    )
                    all_tasks_for_current_sweep_config.append(task)
        
        print(f"Generated {len(all_tasks_for_current_sweep_config)} tasks for this sweep.")

        # 4. Execute parallel tasks
        raw_results_from_pool = []
        if all_tasks_for_current_sweep_config:
            with multiprocessing.Pool(processes=num_processes) as pool:
                # Using map, starmap might be slightly cleaner if task_args was a list of lists/tuples
                # For map, execute_single_run receives one item from all_tasks_for_current_sweep_config at a time.
                raw_results_from_pool = pool.map(execute_single_run, all_tasks_for_current_sweep_config)
        
        print(f"Finished {len(raw_results_from_pool)} tasks from pool.")

        # 5. Result Aggregation
        # Initialize storage for aggregated results for this sweep config
        aggregated_results_for_sweep = {name: {'means': [], 'stds': [], 'n_runs': []}
                                        for name in ALGORITHMS_TO_COMPARE}
        
        # Group results by (param_value, algo_name)
        # A dictionary to hold lists of throughputs for each (p_value, algo_name)
        # Key: (p_value, algo_name), Value: list of throughputs from successful runs
        temp_aggregation_store = {}

        for res in raw_results_from_pool:
            if res['status'] == 'success':
                key = (res['param_value'], res['algo_name'])
                if key not in temp_aggregation_store:
                    temp_aggregation_store[key] = []
                temp_aggregation_store[key].append(res['throughput'])
            else:
                # Log error or handle as needed
                print(f"Task failed: Algo={res['algo_name']}, Param_Val={res['param_value']}, Run={res['run_idx']}, Error: {res.get('error_message', 'Unknown error')}")

        # Calculate mean, std, n_runs for each param_value in the sweep
        for p_value in param_values_to_sweep: # Iterate in the order of the sweep
            for algo_name in ALGO_NAMES_ORDERED: # Iterate in the defined algorithm order
                key = (p_value, algo_name)
                throughputs_for_key = temp_aggregation_store.get(key, []) # Get list of throughputs, or empty if no successful runs

                if throughputs_for_key: # If there were successful runs
                    # Store raw data for ANOVA
                    # Structure: anova_raw_data_collection[sweep_param_name][param_value][algo_name] = [run1_tp, run2_tp, ...]
                    if param_name_being_swept not in anova_raw_data_collection:
                        anova_raw_data_collection[param_name_being_swept] = {}
                    if p_value not in anova_raw_data_collection[param_name_being_swept]:
                        anova_raw_data_collection[param_name_being_swept][p_value] = {}
                    # Ensure storing a list of standard Python floats if numpy types are involved
                    anova_raw_data_collection[param_name_being_swept][p_value][algo_name] = [float(tp) for tp in throughputs_for_key]

                    avg_throughput = np.nanmean(throughputs_for_key)
                    std_throughput = np.nanstd(throughputs_for_key)
                    actual_n_runs = len(throughputs_for_key) # All here are successful
                else: # No successful runs for this (p_value, algo_name)
                    avg_throughput = np.nan
                    std_throughput = np.nan
                    actual_n_runs = 0
                
                aggregated_results_for_sweep[algo_name]['means'].append(avg_throughput)
                aggregated_results_for_sweep[algo_name]['stds'].append(std_throughput)
                aggregated_results_for_sweep[algo_name]['n_runs'].append(actual_n_runs)
                print(f"  Aggregated for {algo_name} at {param_name_being_swept}={p_value}: "
                      f"Mean={avg_throughput:.4f}, Std={std_throughput:.4f}, N_Runs={actual_n_runs}")

        # 6. Store aggregated results for this sweep configuration
        all_sweep_results_data[param_name_being_swept] = {
            'results': aggregated_results_for_sweep,
            'param_values': param_values_to_sweep, # Original list of param values for this sweep
            'xlabel': xlabel,
            'title_suffix': title_suffix
        }
        
        sweep_end_time = time.time()
        print(f"--- PARALLEL Sweep for {param_name_being_swept} Finished. Time: {sweep_end_time - sweep_start_time:.2f}s ---")

    # Restore original parameters in the main process one last time after all sweeps are done.
    restore_all_original_parameters()

    overall_end_time = time.time()
    print(f"\n--- All PARALLEL Parameter Sweeps Finished. Total Time: {overall_end_time - overall_start_time:.2f}s ---")

    # Save raw data for ANOVA
    import json # Make sure json is imported
    anova_output_filename = "anova_raw_throughput_data.json"
    try:
        with open(anova_output_filename, 'w') as f_json:
            json.dump(anova_raw_data_collection, f_json, indent=4)
        print(f"Successfully saved raw throughput data for ANOVA to {anova_output_filename}")
    except Exception as e_json:
        print(f"Error saving ANOVA raw data to JSON: {e_json}")

    # Phase D: Plotting results (to be added next)
    # For now, just print that it's done with computation.
    # print("\n--- Computation finished. Plotting phase is next. ---") # Replaced by actual plotting

    # --- Plotting Results for Each Sweep (Adapted from parameter_sweep.py) ---
    print("\n--- Plotting All Sweep Results ---")

    # Ensure ALGO_NAMES_ORDERED, LINESTYLES, MARKERS are defined (they are at the top of the script)

    for param_name_swept, sweep_data in all_sweep_results_data.items():
        print(f"Plotting results for sweep: {param_name_swept}")
        results_dict = sweep_data['results'] # This is aggregated_results_for_sweep
        param_values_plot = sweep_data['param_values'] # This is param_values_to_sweep
        xlabel_plot = sweep_data['xlabel']
        title_suffix_plot = sweep_data['title_suffix']

        plt.figure(figsize=(10, 6))
        for i, algo_name_plot in enumerate(ALGO_NAMES_ORDERED):
            if algo_name_plot not in results_dict:
                print(f"Warning: Algorithm {algo_name_plot} not found in results for sweep {param_name_swept}. Skipping.")
                continue
            
            algo_data = results_dict[algo_name_plot]
            means = np.array(algo_data['means'])
            stds = np.array(algo_data['stds'])
            n_runs_array = np.array(algo_data['n_runs'])

            # Filter out potential NaN values if all runs for a point failed (based on mean being NaN)
            # param_values_plot should align with means, stds, n_runs_array as they were populated in order
            valid_indices = [j for j, m in enumerate(means) if not np.isnan(m) and n_runs_array[j] > 0]
            
            if not valid_indices:
                print(f"No valid data points to plot for {algo_name_plot} in sweep {param_name_swept}.")
                continue

            valid_params_plot = np.array([param_values_plot[j] for j in valid_indices])
            valid_means = means[valid_indices]
            valid_stds = stds[valid_indices]
            valid_n_runs = n_runs_array[valid_indices]
            
            linestyle_idx = i % len(LINESTYLES) # Cycle through linestyles
            marker_idx = i % len(MARKERS) # Cycle through markers
            
            line, = plt.plot(valid_params_plot, valid_means, marker=MARKERS[marker_idx], linestyle=LINESTYLES[linestyle_idx], label=algo_name_plot)
            
            # Calculate 95% CI
            # se = stds / sqrt(n_runs)
            # For safety against n_runs being zero (already filtered by valid_n_runs > 0)
            se = np.divide(valid_stds, np.sqrt(valid_n_runs), out=np.zeros_like(valid_stds, dtype=float), where=valid_n_runs > 0)
            ci_half_width = 1.96 * se # t-distribution with df_i-1, for large n_runs_array (>30) z=1.96 is a good approx.
            
            plt.fill_between(valid_params_plot, valid_means - ci_half_width, valid_means + ci_half_width, color=line.get_color(), alpha=0.2)

        plt.xlabel(xlabel_plot)
        plt.ylabel("Average Throughput (Mbps)") # Assuming throughput is the primary metric
        plt.title(f"Parallel Sweep: Throughput {title_suffix_plot}")
        plt.grid(True)
        plt.legend(loc='best')
        plt.tight_layout()
        
        # Ensure the 'fig' directory exists
        fig_dir = 'fig_parallel' # Save to a different directory to avoid overwriting sequential script's figs
        if not os.path.exists(fig_dir):
            try:
                os.makedirs(fig_dir)
                print(f"Created directory: {fig_dir}")
            except OSError as e:
                print(f"Error creating directory {fig_dir}: {e}. Figures will be saved in current directory.")
                fig_dir = '.' # Fallback to current directory

        plot_filename = os.path.join(fig_dir, f"par_sweep_throughput_vs_{param_name_swept.replace(' ', '_').replace('.', '_')}.png")
        try:
            plt.savefig(plot_filename)
            print(f"Saved plot to {plot_filename}")
        except Exception as e:
            print(f"Error saving plot {plot_filename}: {e}")
        plt.close() # Close the figure

    print("\n--- All Plotting Finished ---")
