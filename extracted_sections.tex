\documentclass[journal]{IEEEtran}
%\documentclass[journal,11pt,draftclsnofoot,onecolumn]{IEEEtran}
\usepackage{amsmath,amsfonts}
%\usepackage{algorithmic}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{bm}
\usepackage{graphicx}
%\usepackage{algorithm}
%\usepackage{algpseudocode}
\usepackage[colorlinks, linkcolor=blue, citecolor=blue]{hyperref}
\usepackage{amsmath}
\usepackage{epstopdf}
\usepackage{booktabs}  
\usepackage{multirow}   
\usepackage{array}     
\usepackage{csquotes}
\usepackage{color}
\usepackage{amssymb}
\usepackage{enumerate}
\usepackage[numbers,sort&compress]{natbib}

\graphicspath{{fig/}}

%\renewcommand{\algorithmicrequire}{\textbf{Input:}}  
%\renewcommand{\algorithmicensure}{\textbf{Output:}} 
\usepackage[ruled,linesnumbered]{algorithm2e}
\newtheorem{proposition}{Proposition}
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{corollary}{Corollary}
\newtheorem{remark}{Remark}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}
\newenvironment{proof}{{\quad \it Proof:}}{$\hfill\blacksquare$\par}
\hyphenation{}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
		T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\usepackage{balance}
\setlength{\parindent}{1em}


\IEEEpubid{\begin{minipage}{\textwidth}\ \\[30pt] \centering
        Copyright \copyright~2025 IEEE. Personal use of this material is permitted. \\However, permission to use this material for any other purposes must be obtained from the IEEE by sending a <NAME_EMAIL>.
\end{minipage}}

\begin{document}

	\section{System Models}
	\label{sec_model}
	\subsection{Network Model}
	We consider an EH-WSN composed of $N$ static nodes in the set $\mathcal{N}{=}\{1,2,\ldots,N\}$ and $K$ sinks in the set $\mathcal{K} {=}\{1,2,\ldots, K\}$. 
    The network topology is represented by a graph $\mathcal{G} {=} (\mathcal{V}, \mathcal{E})$, where $\mathcal{V}{=}\mathcal{N}{\cup}\mathcal{K}$ and $\mathcal{E}{=}\{1,2,\ldots,E\}{\subseteq}\mathcal{V}{\times}\mathcal{V}$ denotes the set of communication links (also called as data links).
	
	
	The nodes in the EH-WSN sense the environment and generate data that needs to be routed, potentially through multi-hop relaying, to one of the sinks. We assume the network is connected, ensuring that each node has routes to all sinks. The sinks collect the incoming data, which is subsequently forwarded to remote back-end servers via long-range links for further processing.
    
    In this work, we consider a time-slotted periodic data collection application in EH-WSNs, where each operational cycle is divided into three stages: task assignment, data sensing, and data routing. In the task assignment stage, the system allocates a set of data collection tasks to selected nodes. Each task is represented as a four-tuple $(src,sink,time,size)$, where $src$ specifies the node assigned to the task, $sink$ indicates the destination node for the task's data, $time$ represents the time slot in which the task is assigned, and $size$ denotes the data size to be collected, measured in bits. During the data sensing stage, nodes sense and collect the required amount of data, grouping them into packets for transmission. When the data size is large, multiple packets may be generated. Let $\mathcal{Q}{=}\{1,2,\ldots,Q\}$ denote the set of all data packets generated by the task set. In the subsequent data routing stage, the prepared data packets in $\mathcal{Q}$ are routed to their respective destinations. A task is considered complete only when all data collected in the sensing stage successfully reach their assigned sinks. This paper addresses the problem of minimizing the number of time slots required to complete all tasks by jointly optimizing data transmission and energy cooperation schedules using M-EC. The time slots for the data routing stage are represented as $\mathcal{T}{=}\{1,2,\ldots,T\}$. For simplicity, we assume each time slot has a unit time length, allowing power and energy to be used interchangeably in the subsequent discussions.


	\subsection{Data Communication Model}
	Regarding data communication, we make the following preliminary assumptions: (1) Data communication operates in a half-duplex mode, meaning a node cannot transmit and receive data simultaneously; (2) All data communications are unicast; (3) A node cannot transmit or receive different data over multiple outgoing or incoming links at the same time; (4) Channel interference among data links activated in the same time slot is negligible, achieved through methods such as using orthogonal frequency channels and allowing only far-apart links to share the same channel~\cite{Mamat2023}.  

    Let $a_{i,k}^t$ denote the amount of data generated by node $i$ in time slot $t$, destined for sink $k$. Let $p_{\text{sense}}$ denote the energy consumed by a node to sense one unit of data, and let $s_i^t$ represent the amount of energy consumed by node $i$ for data sensing in time slot $t$. Consequently, the total energy consumed for data sensing is given by
	\begin{equation}
		\label{eq_s}
		\begin{aligned}
			s_i^t = \sum_{k \in \mathcal{K}}p_\text{sense}\cdot a_{i,k}^t,\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	

    Let $\mathbb{S}^t_D$ denote the set of active links in time slot $t$, and let $p_{l}^t$ represent the transmission power allocated to link $l$ in time slot $t$. Let $p_{\text{max}}$ and $p_{\text{min}}$ denote the maximum and minimum transmission powers of a node for data communication, respectively. Then, the transmission power of each active link must satisfy the following constraint.
	\begin{equation}
		\label{eq_p_control}
		\begin{aligned}
			p_\text{min}\leq p_{l}^t\leq p_\text{max}, {\quad}{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	

We assume all data links follow a Rician fading channel~\cite{Gao2015}. For link $l(i,j)$, the probability density function of its channel power gain $g_{i,j}^t$ (or $g_{l}^t$) is given by Eq.~\eqref{eq_g_ij_rician}, where $\overline{g}_{i,j}^t$ represents the expected value of $g_{i,j}^t$, $K_{i,j}$ is the Rician K-factor, defined as the ratio of the power in the dominant line-of-sight component to the total power in the scattered components. The variable $x$ is a random variable drawn from an exponential distribution with a mean of one, and $I_0(\cdot)$ denotes the zero-order modified Bessel function \cite{Gao2015}.
    \begin{equation} 
	\label{eq_g_ij_rician}
 \begin{aligned}
	   f_{g_{i,j}^t}(x) = &\frac{K_{i,j}+1}{\overline{g}_{i,j}^t} e^{-x(K_{i,j}+1)/\overline{g}_{i,j}^t - K_{i,j}}\\
    &{\cdot}I_0 \left( \sqrt{\frac{4K_{i,j}(K_{i,j}+1)x}{\overline{g}_{i,j}^t}} \right), \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{aligned}
    \end{equation} 


We assume that, for link $l(i,j)$, the expected channel power gain $\overline{g}_{i,j}^t$ follows an adjusted Friis free-space equation, as expressed in Eq.~\eqref{eq_g_ij}. Here, $d_{i,j}$ represents the distance between nodes $i$ and $j$, $G_i$ and $G_j$ represent the transmitter and receiver antenna gains, respectively, $L_p$ accounts for polarization losses, $\lambda$ is the signal wavelength, and $\beta$ is an adjustment factor introduced for short-range transmission scenarios \cite{He2013}.
    \begin{equation} 
	\label{eq_g_ij}
	   \overline g_{i,j}^t = \frac{G_i G_j}{L_p} \left( \frac{\lambda}{4 \pi (d_{i,j} + \beta)} \right)^2,\quad \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{equation} 


	Let $\sigma_{l}^t$ denote the corresponding noise power. The Signal-to-Noise Ratio (SNR) of the received signal on link $l$ is then given by Eq.~\eqref{eq_SNR}. To ensure that the received signal on link $l$ can be decoded correctly, the corresponding SNR must not fall below a specified threshold $\gamma_{\text{min}}$, as in Eq.~\eqref{eq_SNR_control}~\cite{Patrik2004}.
    \begin{align}		
		\mathrm{SNR}_{l}^t=&\frac{p_{l}^tg_{l}^t}{\sigma_{l}^t},&{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR}\\
        \mathrm{SNR}_{l}^t \geq& \gamma_{\min}, &{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR_control} 
	\end{align}	


	Let $r_{l}^t$ represent the data transmission rate on link $l$ during time slot 
$t$, which can be expressed as Eq.~\eqref{eq_r}, where $W$ represents the frequency channel bandwidth of the link. We assume all links have the same bandwidth $W$. Since each time slot lasts for one unit time length, $r_l^t$ effectively represents the capacity of link $l$ in time slot $t$.
	\begin{equation}
		\label{eq_r}	
		\begin{aligned}
			r_{l}^t=W\log_2\left(1+	\mathrm{SNR}_{l}^t\right),{\quad}{\forall}l{\in}\mathbb{S}^t_D,t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	 
	
To facilitate data routing, we assume that each node is equipped with a data buffer of capacity  $q_{\text{max}}$, where it maintains $K$ separate data queues, one for each sink. Let $q_{i,k}^t$ denote the queue length of data destined for sink $k$ at node $i$. Similarly, let $f_{l,k}^t$ denote the amount of data transmitted over link $l$ for sink $k$ in time slot $t$. Naturally, data transmission is constrained by both the link capacity and the buffer sizes at the corresponding nodes, so we have constraints as the following equations from Eq.~\eqref{eq_f_r} to Eq.~\eqref{eq_f_q_j}, where $b_j^t$ denotes the amount of energy in battery of node $j$ at time slot $t$, and $p_{\text{rcv}}$ denotes the energy consumed by a node to receive a unit of data.
    \begin{align}
f_{l,k}^t{\leq}&r_{l}^t,{\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_r}\\
f_{l,k}^t{\leq}&q_{i,k}^t, {\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_q_i}\\
f_{l,k}^t{\leq}&\frac{b_{j}^t}{p_\text{rcv}},& \forall  l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_b}\\
f_{l,k}^t{\leq}&q_\text{max}{-}\sum_{k{\in}\mathcal{K}}q_{j,k}^t & \forall l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.\label{eq_f_q_j}
    \end{align}
    
	
    Under the above constraints, the parameter $\gamma_{\text{min}}$ in constraint Eq.~\eqref{eq_SNR_control} under limits the the SNR of the link, ensuring efficient link capacity, as indicated in Eq.~\eqref{eq_r}. This, in turn, enables higher data flow rates, as described in Eq.~\eqref{eq_f_r}.
	
    Combining Eq.~\eqref{eq_p_control}, Eq.~\eqref{eq_SNR_control}, and Eq.~\eqref{eq_f_r}, the power $p_l^t$ allowed for data transmission on link $l$ can be expressed as
	\begin{equation}
		\label{eq_p_f}
			p_l^t{=}{\max}\left\{p_{\min},\frac{\sigma_{l}{\cdot}\gamma_{\min}}{g_l^t}, \frac{(2^{\frac{\sum_{k{\in}\mathcal{K}}f_{l,k}^t}{W}}{-}1)\sigma_l^t}{g_l^t}\right\}, {\forall}l{\in}\mathbb{S}^t_D,t{\in}\mathcal{T}.
	\end{equation}

    Then, the energy amounts consumed by node $i$ in time slot $t$ for data transmission and data reception, denoted respectively as $\hat{p}_i^t$ and $\check{p}_i^t$, are given by Eq.~\eqref{eq_p_out} and Eq.~\eqref{eq_p_in}, respectively.

	\begin{align}
	\hat p_i^t =&\sum_{l\in\mathcal{O}_i}p_l^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_p_out}\\
    \check p_i^t = &\sum_{l\in\mathcal{I}_i}\sum_{k{\in}\mathcal{K}}p_\text{rcv}{\cdot}f_{l,k}^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_p_in}
	\end{align}

	The queue length update formula for node $i$, corresponding to data destined for sink $k$ in time slot $t$, is given by
	\begin{equation}
		\label{eq_q}
		\begin{aligned}
			q_{i,k}^{t{+}1}=q_{i,k}^{t}+a_{i,k}^{t}+\sum_{l\in \mathcal{I}_i}f_{l,k}^t-\sum_{l\in \mathcal{O}_i}f_{l,k}^t,\\{\forall}i{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}

    
	\subsection{Energy Harvesting Model}
   Each node is equipped with a rechargeable battery with a capacity of $b_{\max}$ for energy storage. We assume that the nodes are powered by energy harvested using specialized devices from the environmental sources, such as solar energy. In this work, the energy harvesting process of a node from the environment is modeled using the hidden Markov chain framework described in~\cite{Ku2015}. In this model, the energy harvesting conditions at a node are classified into four distinct states: \textit{Excellent}, \textit{Good}, \textit{Fair}, and \textit{Poor}, indexed as 1 through 4, respectively. For each state $c{\in}\{1,2,3,4\}$, the amount of energy harvested in a time slot $t$ by a node $i$, denoted as $h_i^t$, is modeled as a Gaussian distribution with mean $\mu_c$ and variance $\rho_c$. Additionally, the probability of transitioning from state $c$ to state $c'$ is denoted by $P_{cc'}$. These parameters are derived from actual solar irradiance measurements~\cite{Ku2015}.


    In addition to harvesting energy from environmental sources, nodes can also harvest RF energy from the radio signals of neighboring nodes to supplement their energy reserves. These radio signals include both intentional energy transmission signals for energy cooperation via WPT and unicast data transmission signals intended for specific destinations, which are unintentionally received by the current node due to the broadcast nature of radio signals. We assume that a node's energy cooperation neighbors are also its communication neighbors. To prevent interference from energy signal to data signal, we assume that energy cooperation are conducted on different frequency channels from data collection, whereas the energy antenna can harvest energy from both energy transmission and data transmission signals. 
    	Let $\mathbb{S}_E^t$ denote the set of nodes engaged in energy transmission in time slot $t$, and let $e_{\max}$ and $e_{\min}$ respectively denote the maximum and minimum energy transmission powers of nodes. Let $\hat{e}_i^t$ denote the energy amount utilized for energy transmission by node $i$ in time slot $t$, then we have Eq.~\eqref{eq_epower_bounds_first}.
	\begin{equation}
		\label{eq_epower_bounds_first}
		e_\text{min}\leq \hat{e}_i^t\leq e_\text{max}, \quad {\forall} i{\in} \mathbb{S}_E^t, t{\in}\mathcal{T}.
	\end{equation}
	
	Let $\hat{p}_j^t$ and $\hat{e}_j^t$ respectively denote the energy transmit power and data transmission of node $i$ in time slot $t$, then the accumulated RF signal power at node $i$ in time slot $t$, denoted as $Rf_i^t$, follows Eq.~\eqref{eq_Rf}.
	\begin{equation}
		\label{eq_Rf}
		Rf_i^t = 
		\left\{
		\begin{aligned}
			&\sum_{j{\in}\mathcal{N}_i} \hat{p}_j^t g_{j,i}^t + \sum_{j{\in}\mathcal{N}_i} \hat{e}_j^t g_{j,i}^t, && \text{if } i{\notin}\mathbb{S}^t_E, \\
			&0, && \text{if } i{\in}\mathbb{S}^t_E.
		\end{aligned}
		\right.
	\end{equation}

        Because of the energy loss incured in energy harvesting process, node $i$ can only harvest a small part of the accumulated RF signal power $Rf_i^t$. Let $\check{e}_i^t$ denote the energy amount harvested by node $i$ from $Rf_i^t$, then $\check{e}_i^t$ can be expressed as Eq.~\eqref{eq_EH}, where $\Omega_{i}$ and $\Psi_{i}^{t}$ are provided in Eq.~\eqref{eq_omega} and Eq.~\eqref{eq_psi}, $\exp(\cdot)$ denotes the exponentiation with the base of the natural logarithm $e$, constant $e_\text{mp}$ represents the upper limit of energy power harvested by a node from RF signals in a time slot (referred as maximum energy harvesting power from RF signals for short), and $\mu_{i}$ and $\nu_{i}$ are fixed parameters determined by the involved hardware components~\cite{Boshkovska2015}.
\begin{subequations}
\label{eq_EH1}
\begin{align}
        \check{e}_{i}^{t}=&\frac{\left[\Psi_{i}^{t}-e_\text{mp}\Omega_{i}\right]}{1-\Omega_{i}},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_EH}\\
	\Omega_{i}=& \frac{1}{1+\exp(\mu_{i}\nu_{i})},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_omega}\\
	\Psi_{i}^{t}=&\frac{e_\text{mp}}{1+\exp\left(-\mu_{i}(Rf_{i}^t-\nu_{i})\right)},& \forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_psi}
\end{align}
\end{subequations}

	
	
	Considering the battery capacity $b_\text{max}$ of a node, the actual energy obtained by node $i$ in time slot $t$, denoted as $\bar{e}_i^t$, should satisfies the constraints as follows. 
	\begin{align}
		\bar{e}_i^t{\leq}&\check{e}_i^t{+}h_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T},\label{eq_e_eh}\\
		\bar{e}_i^t{\leq}&b_{\text{max}}{-}b_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.\label{eq_e_b}
	\end{align}  
	
    Considering the causality of energy usage, we assume that the energy harvested in a time slot can only be used in subsequent time slots. Therefore, the energy consumption behavior of a node during a time slot must satisfy Eq.~\eqref{eq_energy_control}.
	\begin{equation}
		\label{eq_energy_control}
		s_{i}^t+\hat{p}_i^t+\check{p}_i^t+\hat{e}_{i}^t\leq b_i^t, \quad {\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
    In summary, the evolution formula for the battery energy of node $i$ is given by
	\begin{equation}
		\label{eq_b}
		\begin{aligned}
			b_i^{t+1}=b_i^t+\bar e_i^t-s_i^t-\hat p_i^t-\check p_i^t-\hat{e}_{i}^t\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	
	
\subsection{Schedule Model for Data Communication and Energy Cooperation}
    In the energy cooperation-assisted data collection paradigm, actions for energy cooperation and data transmission must be carefully scheduled across time slots. For each time slot, the energy cooperation schedule determines which nodes transmit or receive energy and how much power is used, while the data communication schedule determines which links to activate, which data to transmit, and how much power to use. The activation of a link implicitly determines the corresponding sender and receiver. In brief, the energy cooperation schedule assigns nodes to time slots for energy transmission/reception, while the data communication schedule assigns links to time slots for data transmission. These are referred to as node-based and link-based allocations, respectively, in \cite{Patrik2004}. Since data collection is essentially achieved through data transmissions over links directed towards sinks according to the data communication schedule, the terms \textit{data collection schedule} and \textit{data communication schedule} are used interchangeably. The combination of an energy cooperation schedule and a data communication schedule is called an Energy Cooperation-assisted Data Collection (ECaDC) joint schedule.

   Now, we define some decision variables to describe an ECaDC schedule. Let $x_{l,k}^t{\in}\{0,1\}$ denote whether link $l$ is activated in time slot $t$ for transmitting data packets targeted for sink $k$, where 1 indicates that the link is activated and 0 indicates that it is not. Let $y_i^t{\in}\{0,1\}$ represent the energy reception/transmission state of node $i$ in time slot $t$, where $y_i^t{=}1$ means that node $i$ should transmit energy, and $y_i^t{=}0$ means that it should be in the energy reception state. Let $\textbf{x}{:=}[x_{l,k}^t| l{\in} \mathcal{E}, k {\in}\mathcal{K}, t {\in}\mathcal{T}]$, $\textbf{y}{:=}[y_i^t| i {\in} \mathcal{N}, t{\in}\mathcal{T}]$, $\textbf{p} {:=} [p_l^t | l {\in} \mathcal{E}, t {\in} \mathcal{T}]$, and $\textbf{e}{:=} [\hat{e}_i^t|i {\in} \mathcal{N}, t {\in} \mathcal{T}]$. Then $\textbf{x}$, $\textbf{y}$, $\textbf{p}$, $\textbf{e}$  together form an ECaDC schedule, which is expressed as ECaDC($\textbf{x,y,p,e}$) to emphasize its components $\textbf{x,y,p,e}$.

    We assume that a node can only transmit one type of data packet per time slot. Considering the half-duplex mode of data communication, the activation status of the links must satisfy the following constraint.
	
	\begin{equation}
		\label{eq_x}
		\sum_{k\in \mathcal{K}}\sum_{l\in \mathcal{I}_i}x_{l,k}^t+\sum_{k\in \mathcal{K}}\sum_{l\in\mathcal{O}_i}x_{l,k}^t\leq1,  \quad{\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
	Using the decision variables in $\textbf{x}$, the constraint on $p_l^t$ in Eq.~\eqref{eq_p_control} can be re-expressed as Eq.~\eqref{eq_p_x}. 
	\begin{equation}
		\label{eq_p_x}
		\begin{aligned}
			\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{min}\leq p_{l}^t\leq\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{max},\quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	The SNR constraints in Eq.~\eqref{eq_SNR_control} and Eq.~\eqref{eq_SNR} can be combined as Eq.~\eqref{eq_SNR_x_control}, where a large enough constant $\phi_1 {\gg} \gamma_\text{min}$ is required to deactivate the constraint when the link is not active. 
	\begin{equation} 
		\label{eq_SNR_x_control}
		p_{l}^tg_{l}^t+\phi_1(1-\sum_{k\in \mathcal{K}}x_{lk}^t)\geq\gamma_\text{min}\sigma_{l}^t, \quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
	\end{equation}	
	 
	The constraint in Eq.~\eqref{eq_epower_bounds_first} for controlling \(\hat{e}_i^t\) can be re-expressed as
	\begin{equation}
		\label{eq_y}
		y_{i}^t{\cdot}e_\text{min}\leq \hat{e}_i^t\leq y_{i}^t{\cdot}e_\text{max}, \quad \forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.
	\end{equation}
	
	With the assumption that a node can receive energy signals only when it is in the energy reception state, Eq.~\eqref{eq_Rf} about \(Rf_i^t\) can be re-expressed as Eq.~\eqref{eq_Rf_in_y}, where  $\phi_2$ is a large positive number.
	\begin{equation}
		\label{eq_Rf_in_y}
		Rf_i^t\leq(1{-}y_i^t)\phi_2,  \quad \forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.
	\end{equation}

	
	\section{Problem Formulation for LTA Throughput Maximization}
	\label{sec_problem_def}
	
	In this paper, we adopt the Lyapunov optimization framework to address the joint scheduling of multicast energy cooperation and data collection. The primary objective is to maximize the Long-Term Average (LTA) system throughput, while ensuring the stability of data queues and maintaining LTA battery energy levels above certain thresholds.
	
	The LTA system throughput, denoted as $\bar{\mathcal{D}}$, is defined as the time-averaged rate at which data packets are successfully delivered to their respective sinks:
	\begin{equation}
		\label{eq_lta_throughput}
		\bar{\mathcal{D}} = \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E\left[ \sum_{k \in \mathcal{K}} \sum_{l=(i,k) \in \mathcal{I}_k} f_{l,k}^t \right]
	\end{equation}
    where $\mathcal{I}_k$ denotes the set of incoming links to sink $k$, and $f_{l,k}^t$ is the amount of data for sink $k$ transmitted over link $l$ in time slot $t$. The expectation $E[\cdot]$ is taken over the random channel states and energy harvesting processes.

    We aim to maximize $\bar{\mathcal{D}}$ subject to the following constraints:
    \begin{itemize}
        \item \textbf{Data Queue Stability:} All data queues $q_{i,k}(t)$ must be stable, meaning their long-term average size is finite. This implicitly ensures that all generated data is eventually delivered.
        \item \textbf{LTA Battery Energy Constraint:} The LTA battery energy level of each node $i$ must be maintained above a predefined threshold $\delta_i$:
        \begin{equation}
            \label{eq_lta_energy}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge \delta_i, \quad \forall i \in \mathcal{N}
        \end{equation}
        \item \textbf{Instantaneous Constraints:} All system model constraints defined in Section~\ref{sec_model} must be satisfied in each time slot $t$. These include energy causality (Eq.~\eqref{eq_energy_control}), battery capacity (Eq.~\eqref{eq_e_b}), power limits (Eqs.~\eqref{eq_p_x}, \eqref{eq_y}), half-duplex operation (Eq.~\eqref{eq_x}), data flow constraints (Eqs.~\eqref{eq_f_r}-\eqref{eq_f_q_j}), SNR requirements (Eq.~\eqref{eq_SNR_x_control}), etc.
    \end{itemize}

    Formally, the optimization problem can be stated as:
	\begin{equation}
		\label{eq_P1_prime}
		\begin{array}{rrl}
			\textbf{(\textbf{P1'})} &\max\limits_{\textbf{x,y,p,e}} & \bar{\mathcal{D}} \\
			& \text{s.t.}& \text{Data Queue Stability for all } q_{i,k}(t) \\
            && \text{LTA Battery Energy Constraint (Eq.~\eqref{eq_lta_energy}) for all } i \in \mathcal{N} \\
			&& \text{Instantaneous Constraints (Eqs}.~\eqref{eq_s}-\eqref{eq_Rf_in_y}) \text{ for all } t
		\end{array}
	\end{equation}
    
    Problem P1' is a stochastic network optimization problem involving LTA objectives and constraints, making it suitable for the Lyapunov optimization framework. The NP-hardness proof for the original MECADCS problem (Lemma~\ref{proposition_1}) likely still applies or can be adapted, as the underlying scheduling complexity remains.

    Directly solving P1' is challenging due to the LTA nature and the complex interplay between decisions across time slots. Therefore, we will employ the Lyapunov optimization technique to transform this problem into a sequence of per-slot optimization problems that can be solved online.

	\begin{lemma}
		\label{proposition_1}
		The underlying scheduling problem remains NP-Hard even with the LTA objective.
	\end{lemma}
	
	\begin{proof}
		We prove the NP-hardness of MECADCS by providing a reduction from the Flexible Job Shop Scheduling (FJSP) problem~\cite{SDPJDLSKT2024}, which is NPC, to a sub-problem of MECADCS, where slot time is small sufficient, all nodes initially have sufficient energy that makes M-EC unnecessary, have no data buffer, and they always use a fixed transmission power. The sub-problem is called the Data Collection Scheduling (DCS) problem. 
		
		\textbf{The DCS problem:} This problem can be stated as follows: Given a network with graph $G(V,E)$, a set of data packets $P$ where $i{\in}P$ is generated at $s_{i}$ and should be transmitted to its destination $d_{i}$ following a set of predefined routes $R_{i}$ with transmission time on node $k$ as $t_i^k$, the objective is to find a link scheduling solution with the shortest time required to route all the packets to their destinations.  
		
		\textbf{The FJSP problem:} According to~\cite{SDPJDLSKT2024}, given a set of jobs $\mathcal{J}$, a set of machines $\mathcal{M}$, and a set of operations $\mathcal{O}$. Each job $j{\in}\mathcal{J}$ consists of a series of $n_j$ consecutive operations in set $\mathcal{O}$, where each operation $i{\in}\mathcal{O}$ can be performed on any machine from a machine set $\mathcal{M}_i{\subseteq}\mathcal{M}$.  
		
		Let $\tau_i^k$ be the processing time of operation $i$ on machine $k{\in}\mathcal{M}_i$. Each operation $i$ has a direct predecessor $pr(i)$ and a direct successor $fr(i)$ according to the technological processing order of the job (referred to as routing or path). The sets $\mathcal{P}\mathcal{R}(i)$ and $\mathcal{F}\mathcal{R}(i)$ represent all predecessor and successor operations of operation $i$ in all its routes, respectively.
		
        It also assumes that: (1) Each machine processes only one job at a time; (2) Each job runs uninterrupted on a single machine; (3) All machines and jobs are available at time 0.
		
		Let $\mathcal{S}$ be the set of all valid solutions, and let $s{\in}\mathcal{S}$ be a solution (which consists of a sequence of operations and a set of operation-machine assignments), let $C_j(s)$ denote job $j$'s completion time in the solution $s$, let $C_{\max}(s){=}\max\{C_j(s)|j{\in}\mathcal{J}\}$, then the objective of FJSP is to find an optimal solution $s^*{=}\arg\min_{s{\in}\mathcal{S}}(C_{\text{max}}(s))$.
		
		\textbf{Reduction from an FJSP instance to a DCS Instance:}
		
		\begin{itemize}
			\item \textbf{$\mathcal{M}{\cup}\mathcal{J}{\rightarrow}V$:} 
			
			For each machine $k{\in}\mathcal{M}$ in FJSP, create a node $v_{Mk}$. For each job $j{\in}\mathcal{J}$, create two nodes $v^s_{Jj}$, $v^d_{Jj}$, which will be assigned as the source and destination of a packet corresponding to task $j$. All such nodes make up the node set $V{=}\{v_{Mk}|k{\in}\mathcal{M}\}{\cup}\{v^s_{Jj},v^d_{Jj}|j{\in}\mathcal{J}\}$ in the DCS problem instance.
			
			\item \textbf{$\mathcal{J}{\rightarrow}P$:} 
			
			
			For each job $j{\in}\mathcal{J}$, create a packet $p_{j}$ source at node $v_{Jj}$. All such packets make up the packet set $P{=}\{p_{j}|j{\in}\mathcal{J}\}$ in the DCS problem instance.
			
			\item \textbf{$\mathcal{O}$ and $\{\mathcal{M}_i|i{\in}\mathcal{J}\} {\rightarrow}\{R_i|i{\in}\mathcal{J}\}$:} 
			
			For each job $i$ in the FJSP instance, assume job $i$ has technological processing sequence $[O_1,O_2,\ldots,O_{n_i}]$, we create a set of routes $R_{pi}$ for routing packet $i$ as follows. Using $v^s_{Ji}$ and $v^d_{Ji}$ respectively as the only source and the destination of packet $p_i{\in}P$, using the nodes in set $\{v_{Mj}|j{\in}\mathcal{M}_k\}$ as the $k$-th hop nodes, $k{\in}\{1,2,\ldots,n_i\}$. All such routes make up the route set $R_i$ for packet $p_i{\in}P$. 
			\item \textbf{$\tau_i^k{\rightarrow}t_i^k$:} 
			
			The processing time $\tau_i^k$ of operation $i$ on machine $k{\in}\mathcal{M}_i$ is mapped to the packet transmission time for node $v_{Mk}$ forwarding the packet $p_i{\in}P$ along the routes.
		\end{itemize}
		
		Through above steps, we convert an FJSP instance into a DCS instance in polynomial time. Furthermore, it is easy to notice the fact that there is a one-to-one map between the solutions of the FJSP instance and those of the DCS instance. Since FJSP is NPC, our DCS problem is NP-hard. The NP-hardness of MECADCS is established.
	\end{proof}
	
	\section{Lyapunov Optimization Framework}
    \label{sec_lyapunov}
    To solve the stochastic optimization problem P1' defined in Eq.~\eqref{eq_P1_prime}, we employ the Lyapunov optimization framework~\cite{neely2010stochastic}.
    This framework transforms the LTA problem into a series of deterministic optimization problems that need to be solved in each time slot.

    \subsection{Virtual Queues}
    The Lyapunov framework utilizes virtual queues to manage LTA constraints. The data queues $q_{i,k}(t)$ defined in Eq.~\eqref{eq_q} already track the data backlog. To handle the LTA battery energy constraint (Eq.~\eqref{eq_lta_energy}), we introduce a virtual energy deficit queue $B_i(t)$ for each node $i \in \mathcal{N}$. This queue measures the cumulative deficit of the battery energy level $b_i(t)$ relative to the threshold $\delta_i$. The update rule for $B_i(t)$ is given by:
    \begin{equation}
        \label{eq_virtual_energy_queue}
        B_i(t+1) = \max\{ B_i(t) + \delta_i - b_i(t+1), 0 \}
    \end{equation}
    where $b_i(t+1)$ is the actual battery energy level at the beginning of slot $t+1$, calculated using Eq.~\eqref{eq_b}. Intuitively, if $b_i(t+1)$ falls below $\delta_i$, the deficit queue $B_i(t)$ tends to increase, indicating a violation of the LTA target. Queue stability for $B_i(t)$ implies that the LTA constraint Eq.~\eqref{eq_lta_energy} is satisfied~\cite{neely2010stochastic}.

    Let $\Theta(t) = (\mathbf{q}(t), \mathbf{B}(t))$ represent the combined state vector of all data queues $\mathbf{q}(t) = [q_{i,k}(t)]$ and energy deficit queues $\mathbf{B}(t) = [B_i(t)]$ at the beginning of time slot $t$.

    \subsection{Lyapunov Function and Drift}
    We define a quadratic Lyapunov function $L(\Theta(t))$ to measure the total queue backlog:
    \begin{equation}
        \label{eq_lyapunov_function}
        L(\Theta(t)) = \frac{1}{2} \sum_{i \in \mathcal{N}} \sum_{k \in \mathcal{K}} q_{i,k}(t)^2 + \frac{1}{2} \sum_{i \in \mathcal{N}} B_i(t)^2
    \end{equation}
    Note that weights can be introduced for different queues if needed, but we omit them here for simplicity.

    The one-slot conditional Lyapunov drift, $\Delta L(\Theta(t))$, measures the expected change in the Lyapunov function in one time slot, given the current state $\Theta(t)$:
    \begin{equation}
        \label{eq_lyapunov_drift}
        \Delta L(\Theta(t)) = E[ L(\Theta(t+1)) - L(\Theta(t)) | \Theta(t) ]
    \end{equation}
    Minimizing the drift tends to stabilize the queues.

    \subsection{Drift-Plus-Penalty and Problem Transformation}
    To incorporate the objective of maximizing LTA throughput (Eq.~\eqref{eq_lta_throughput}) while managing the LTA constraints implicitly through queue stability, we use the drift-plus-penalty approach~\cite{neely2010stochastic}.
    The core idea is to minimize a weighted sum of the Lyapunov drift and the negative of the desired objective in each time slot. This transforms the original LTA problem P1' into a new problem focused on per-slot decisions.

    Specifically, we aim to minimize the drift-plus-penalty function $Y(t)$ defined as:
    \begin{equation}
        \label{eq_drift_plus_penalty_func}
        Y(t) := \Delta L(\Theta(t)) - V \cdot E[\text{DeliveredData}(t) | \Theta(t)]
    \end{equation}
    where $\text{DeliveredData}(t) = \sum_{k \in \mathcal{K}} \sum_{l=(i,k) \in \mathcal{I}_k} f_{l,k}^t$ is the total data delivered to sinks in slot $t$, and $V \ge 0$ is a non-negative control parameter that balances the trade-off between queue stability (minimizing drift $\Delta L$) and throughput maximization (maximizing $E[\text{DeliveredData}]$). A larger $V$ prioritizes throughput. Minimizing $Y(t)$ tends to push the system towards states with lower queue backlogs while simultaneously maximizing the objective function.

    By minimizing $Y(t)$ in each slot, the LTA constraints $C_1$ and $C_2$ in P1' (represented by the stability of queues $q_{i,k}$ and $B_i$) are effectively managed. Thus, problem P1' can be approximated by the following problem P2, which aims to minimize the drift-plus-penalty function subject to instantaneous constraints:
    \begin{equation}
        \label{eq_P2}
        \begin{array}{rrl}
            \textbf{(P2)} &\min\limits_{\textbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t} & Y(t) \\
            & \text{s.t.}& \text{Instantaneous Constraints (Eqs}.~\eqref{eq_s}-\eqref{eq_Rf_in_y}) \text{ for slot } t
        \end{array}
    \end{equation}
    However, P2 still involves the drift term $\Delta L(\Theta(t))$, which depends on the state $\Theta(t+1)$ in the next time slot (via $L(\Theta(t+1))$). To enable online decision-making based only on the current state $\Theta(t)$, we derive an upper bound for $Y(t)$ that depends only on current variables.

    We first derive an upper bound for the one-slot Lyapunov drift $\Delta L(\Theta(t))$. The drift can be decomposed into contributions from the data queues and the energy deficit queues:
    \begin{equation}
        \Delta L(\Theta(t)) = \Delta L_Q(t) + \Delta L_B(t)
    \end{equation}
    where
    \begin{align}
        \Delta L_Q(t) &= E\left[\frac{1}{2} \sum_{i,k} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) \bigg| \Theta(t) \right] \\
        \Delta L_B(t) &= E\left[\frac{1}{2} \sum_i (B_i(t+1)^2 - B_i(t)^2) \bigg| \Theta(t) \right]
    \end{align}

    \subsubsection{Bounding Data Queue Drift \texorpdfstring{$\Delta L_Q(t)$}{Delta LQ(t)}}
    Consider a single data queue $q_{i,k}(t)$. Its update rule is $q_{i,k}(t+1) = \max\{0, q_{i,k}(t) - \text{Outflow}_{i,k}^t \} + A_{i,k}(t)$, where $A_{i,k}(t) = a_{i,k}^t + \sum_{l'=(j,i) \in \mathcal{I}_i} f_{l',k}^t$ is the total inflow and $\text{Outflow}_{i,k}^t = \sum_{l=(i,j) \in \mathcal{O}_i} f_{l,k}^t$ is the outflow in slot $t$.
    Using a standard result from Lyapunov optimization theory (e.g., Lemma 4.2 in~\cite{neely2010stochastic}), under the assumption that the second moments of arrivals $A_{i,k}(t)$ and service opportunities $\text{Outflow}_{i,k}^t$ are bounded, the one-slot drift of a single queue $q_{i,k}(t)$ can be bounded as:
    \begin{align}
        & \frac{1}{2} E\left[q_{i,k}(t+1)^2 - q_{i,k}(t)^2 | \Theta(t) \right] \nonumber \\
        & \quad \le C_{i,k} + E\left[ q_{i,k}(t) \left(A_{i,k}(t) - \text{Outflow}_{i,k}^t\right) | \Theta(t) \right] \label{eq_single_queue_drift_bound}
    \end{align}
    where $C_{i,k}$ is a constant related to the bounds on $E[A_{i,k}(t)^2]$ and $E[(\text{Outflow}_{i,k}^t)^2]$.
    Summing over all $i, k$ and taking conditional expectation:
    \begin{align}
        \Delta L_Q(t) \le & E\left[ \sum_{i,k} \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \bigg| \Theta(t) \right] \nonumber \\
        & + E\left[ \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \bigg| \Theta(t) \right]
    \end{align}
    Assuming bounded second moments for arrivals $a_{i,k}^t$ and bounded service rates (e.g., due to $r_{\max}$), the first term can be bounded by a constant $C_q$: $E[\sum_{i,k} \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) | \Theta(t)] \le C_q$.
    The second term can be rewritten by separating the exogenous arrivals $a_{i,k}^t$ and rearranging the flow terms:
    \begin{align}
        & \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \nonumber \\
        &= \sum_{i,k} q_{i,k}(t) a_{i,k}^t + \sum_{i,k} q_{i,k}(t) \left( \sum_{l'=(j,i)} f_{l',k}^t - \sum_{l=(i,j)} f_{l,k}^t \right) \nonumber \\
        &= \sum_{i,k} q_{i,k}(t) a_{i,k}^t + \sum_{l=(i,j) \in E} \sum_{k \in K} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t
    \end{align}
    (Assuming $q_{\text{sink},k}(t)=0$).
    Thus, the data queue drift is bounded by:
    \begin{align}
        \Delta L_Q(t) \le C_q &+ \sum_{i,k} q_{i,k}(t) E[a_{i,k}^t | \Theta(t)] \nonumber \\
        &+ E\left[ \sum_{l=(i,j) \in E} \sum_{k \in K} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \bigg| \Theta(t) \right] \label{eq_delta_lq_bound}
    \end{align}

    \subsubsection{Bounding Energy Deficit Queue Drift \texorpdfstring{$\Delta L_B(t)$}{Delta LB(t)}}
    Consider a single energy deficit queue $B_i(t)$. Its update rule is $B_i(t+1) = \max\{ B_i(t) + \delta_i - b_i(t+1), 0 \}$.
    Let $x = B_i(t) \ge 0$ and $y = \delta_i - b_i(t+1)$. We use the standard inequality $(\max\{0, x+y\})^2 \le (x+y)^2$.
    Thus,
    \begin{align}
        B_i(t+1)^2 &= (\max\{0, B_i(t) + \delta_i - b_i(t+1)\})^2 \nonumber \\
                   &\le (B_i(t) + \delta_i - b_i(t+1))^2 \nonumber \\
                   &= B_i(t)^2 + (\delta_i - b_i(t+1))^2 + 2 B_i(t) (\delta_i - b_i(t+1))
    \end{align}
    Rearranging gives:
    \begin{equation}
        \label{eq_energy_drift_base} % Label kept, ensure it's unique or update if needed
        \frac{1}{2} (B_i(t+1)^2 - B_i(t)^2) \le \frac{1}{2} (\delta_i - b_i(t+1))^2 + B_i(t) (\delta_i - b_i(t+1))
    \end{equation}
    Since $0 \le b_i(t+1) \le b_{\max}$ and assuming $\delta_i$ is bounded, the term $(\delta_i - b_i(t+1))^2$ is bounded. Summing over $i$ and taking conditional expectation, the expectation of the first term can be bounded by a constant $C_b$: $E[\sum_i \frac{1}{2} (\delta_i - b_i(t+1))^2 | \Theta(t)] \le C_b$. Thus,
    \begin{equation}
        \label{eq_delta_lb_step1}
        \Delta L_B(t) \le C_b + E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \Theta(t) \right]
    \end{equation}
    Now, we focus on the second term $E[\sum_i B_i(t) (\delta_i - b_i(t+1)) | \Theta(t)]$. Substitute the battery dynamics $b_i(t+1) = b_i(t) + \bar{e}_i^t - \text{Cons}_i^t$, where $\text{Cons}_i^t = s_i^t + \hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t$ is the total energy consumption, and $\bar{e}_i^t$ is the actual energy gain (capped by $b_{\max}$, $\bar{e}_i^t = \min(\check{e}_i^t + h_i^t, b_{\max} - (b_i(t) - \text{Cons}_i^t))$).
    We have $\delta_i - b_i(t+1) = \delta_i - b_i(t) - \bar{e}_i^t + \text{Cons}_i^t$.
    Directly handling the $\min$ function in $\bar{e}_i^t$ to obtain a drift upper bound is complex. To simplify the analysis and derive a structurally tractable per-slot optimization objective, we employ a common approximation: **we ignore the saturation effect of the battery capacity limit $b_{\max}$ on energy storage**, assuming $\bar{e}_i^t \approx \check{e}_i^t + h_i^t$. This approximation is reasonable in scenarios where the battery rarely reaches its capacity limit and preserves the key relationship where energy consumption and harvesting affect the energy deficit.
    Proceeding with this approximation:
    \begin{equation}
        \delta_i - b_i(t+1) \approx \delta_i - b_i(t) - (\check{e}_i^t + h_i^t) + \text{Cons}_i^t
    \end{equation}
    Since $B_i(t) \ge 0$, multiplying by $B_i(t)$ and taking expectation yields:
    \begin{align}
        & E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \Theta(t) \right] \nonumber \\
        &\approx E\left[ \sum_i B_i(t) (\delta_i - b_i(t) - \check{e}_i^t - h_i^t + \text{Cons}_i^t) \bigg| \Theta(t) \right] \nonumber \\
        &= \sum_i B_i(t) (\delta_i - b_i(t)) - E\left[\sum_i B_i(t) \check{e}_i^t \bigg| \Theta(t)\right] \nonumber \\
        &- E\left[\sum_i B_i(t) h_i^t \bigg| \Theta(t)\right] + E\left[\sum_i B_i(t) \text{Cons}_i^t \bigg| \Theta(t)\right] \nonumber \\
        &= \sum_i B_i(t) (\delta_i - b_i(t)) - E\left[\sum_i B_i(t) \check{e}_i^t \bigg| \Theta(t)\right] \nonumber \\
        &- E\left[\sum_i B_i(t) h_i^t \bigg| \Theta(t)\right] + E\left[\sum_i B_i(t) s_i^t \bigg| \Theta(t)\right] \nonumber \\
        & + E\left[ \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t) \bigg| \Theta(t) \right] \label{eq_energy_term_approx}
    \end{align}
    Substituting Eq.~\eqref{eq_energy_term_approx} into Eq.~\eqref{eq_delta_lb_step1}, we obtain an approximate upper bound on the energy deficit queue drift:
    \begin{align}
        \Delta L_B(t) \lesssim{}& C_b + \sum_i B_i(t) (\delta_i - b_i(t)) \nonumber \\
        & + E\left[\sum_i B_i(t) s_i^t \bigg| \Theta(t)\right] - E\left[\sum_i B_i(t) h_i^t \bigg| \Theta(t)\right] \nonumber \\
        & + E\left[ \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \bigg| \Theta(t) \right] \label{eq_delta_lb_bound}
    \end{align}
    where $\lesssim$ indicates that the bound relies on the approximation ignoring the battery capacity limit.
    \begin{remark}[On the Impact of Approximation]
    It is important to emphasize that the upper bound in Eq.~\eqref{eq_delta_lb_bound} relies on the approximation that ignores the battery capacity limit $b_{\max}$. While this approximation simplifies the derivation and preserves the intuitive structure of the final optimization objective $W^*(t)$—where energy costs and gains are weighted by the energy deficit $B_i(t)$—the bound might not be tight in scenarios where batteries frequently reach full capacity or overflow. This could potentially affect the algorithm's performance under such specific conditions. However, the Lyapunov optimization framework often exhibits robustness to such approximations, especially when the control parameter $V$ is large, as system performance becomes increasingly driven by the $V$-weighted throughput term.
    \end{remark}

    \subsubsection{Total Drift Bound and Per-Slot Optimization}
    Combining the bounds Eq.~\eqref{eq_delta_lq_bound} and Eq.~\eqref{eq_delta_lb_bound}, the total drift $\Delta L(\Theta(t)) = \Delta L_Q(t) + \Delta L_B(t)$ is bounded by:
    \begin{align}
        \Delta L(\Theta(t)) \le C &+ \sum_{i,k} q_{i,k}(t) E[a_{i,k}^t | \Theta(t)] + \sum_i B_i(t) (\delta_i - b_i(t)) \nonumber \\
        &+ E\left[\sum_i B_i(t) s_i^t \bigg| \Theta(t)\right] - E\left[\sum_i B_i(t) h_i^t \bigg| \Theta(t)\right] \nonumber \\
        &+ E \bigg[ \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
        & \quad + \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \bigg| \Theta(t) \bigg] \label{eq_drift_upper_bound_detailed}
    \end{align}
    where $C = C_q + C_b$ is a constant.

    Substituting the drift upper bound Eq.~\eqref{eq_drift_upper_bound_detailed} into the drift-plus-penalty function Eq.~\eqref{eq_drift_plus_penalty_func}, we get an upper bound for $Y(t)$:
    \begin{align}
        Y(t) \le C'(\Theta(t)) &+ E \bigg[ \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
        & \quad + \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \nonumber \\
        & \quad - V \sum_{k, l=(i,k)} f_{l,k}^t \bigg| \Theta(t) \bigg] \label{eq_Y_upper_bound}
    \end{align}
    where $C'(\Theta(t))$ includes the constant $C = C_q + C_b$ and terms involving $q_{i,k}(t)$ and $B_i(t)$ weighted by expected arrivals $E[a_{i,k}^t | \Theta(t)]$, sensing costs $E[s_i^t | \Theta(t)]$, environmental harvesting $E[h_i^t | \Theta(t)]$, and current battery levels $b_i(t)$. Crucially, $C'(\Theta(t))$ does not depend on the control decisions $(\mathbf{x}^t, \mathbf{y}^t, \mathbf{p}^t, \mathbf{e}^t)$ made in the current slot $t$.

    Instead of minimizing the exact drift-plus-penalty $Y(t)$ (problem P2), which is difficult due to the expectation and dependence on future states, the Lyapunov optimization approach minimizes its upper bound in Eq.~\eqref{eq_Y_upper_bound}. Since $C'(\Theta(t))$ is independent of the current control action, minimizing the upper bound is equivalent to minimizing the expectation term within the square brackets. Minimizing this term is equivalent to maximizing its negative. Thus, we arrive at the per-slot optimization problem P3:
    \begin{equation}
        \label{eq_P3}
        \begin{array}{rrl}
            \textbf{(P3)} &\max\limits_{\textbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t} & W^*(t) \\
            & \text{s.t.}& \text{Instantaneous Constraints (Eqs}.~\eqref{eq_s}-\eqref{eq_Rf_in_y}) \text{ for slot } t
        \end{array}
    \end{equation}
    where the objective function $W^*(t)$ is defined as:
    \begin{align}
        W^*(t) := & V \sum_{k, l=(i,k)} f_{l,k}^t - \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
        & - \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \label{eq_maximize_target_W_detailed}
    \end{align}
    Rearranging the terms gives the form used in the algorithm design:
    \begin{align}
        W^*(t) = & \underbrace{\sum_{l=(i,j) \in E} \sum_{k \in K} (q_{i,k}(t) - q_{j,k}(t)) f_{l,k}^t + V \sum_{k, l=(i,k)} f_{l,k}^t}_{\text{Term 1: Weighted Data Transport Benefit}} \nonumber \\
        & + \underbrace{\sum_{i \in N} B_i(t) (\check{e}_i^t - (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t))}_{\text{Term 2: Weighted Net Energy Gain (excluding env. harvest)}} \label{eq_W_star_final}
    \end{align}
    Problem P3 is a deterministic optimization problem that needs to be solved in each time slot $t$ based only on the observed current state $\Theta(t)$, current battery levels $b(t)$, and current channel conditions $g^t$. The online algorithm Lyapunov-MEC proposed in the next section aims to solve (or approximately solve) P3 in each slot.

	\section{The Lyapunov-based MEC Algorithm (Lyapunov-MEC)} 
	\label{sec_lyapunov_algo} % Changed label
    Directly maximizing the expression $W^*(t)$ in Eq.~\eqref{eq_W_star_final} within each time slot is computationally challenging due to the coupled discrete decisions ($\mathbf{x}^t, \mathbf{y}^t$) and continuous power allocation ($\mathbf{p}^t, \mathbf{e}^t$), as well as the non-linear dependencies in energy harvesting ($\check{e}_i^t$) and data rates ($r_l^t$). Therefore, we propose a low-complexity heuristic algorithm, Lyapunov-MEC, which runs online and makes decisions greedily based on the structure of $W^*(t)$.

    The Lyapunov-MEC algorithm operates in each time slot $t$ by sequentially determining the energy cooperation actions and then the data transmission actions, aiming to maximize the weighted sum $W^*(t)$ while satisfying instantaneous constraints.

    \subsection{Algorithm Outline}
    At the beginning of each time slot $t$, the algorithm observes the current state $\Theta(t) = (\mathbf{q}(t), \mathbf{B}(t))$, battery levels $\mathbf{b}(t)$, and channel gains $\mathbf{g}^t$. It then proceeds in two main phases:

    \subsubsection{Phase 1: Energy Cooperation Decision \texorpdfstring{$(\mathbf{y}^t, \mathbf{e}^t)$}{(y\textasciicircum t, e\textasciicircum t)}}
    The goal of this phase is to decide which nodes should transmit energy ($y_i^t=1$) and with what power ($\hat{e}_i^t$). This decision primarily affects the second term (Weighted Net Energy Gain) in $W^*(t)$ (Eq.~\eqref{eq_W_star_final}). The decision is made heuristically:
    \begin{enumerate}
        \item \textbf{Identify Potential Senders:} Node $i$ is identified as a potential energy sender if it satisfies the energy condition $b_i(t) > \alpha \cdot b_{\max}$ and the energy deficit condition $B_i(t) < \beta'$. Here, $\alpha \in (0, 1]$ and $\beta' \ge 0$ are input parameters to the algorithm.
        \item \textbf{Identify Potential Receivers:} Node $j \in \mathcal{N}_i$ is identified as a potential energy receiver if it satisfies the energy condition $b_j(t) < \gamma \cdot b_{\max}$ and the energy deficit condition $B_j(t) > \zeta'$. Here, $\gamma \in [0, 1)$ and $\zeta' \ge 0$ are input parameters to the algorithm.
        \item \textbf{Estimate Gain:} For each potential sender $i$, estimate the marginal gain in $W^*(t)$ from transmitting energy with a trial power $e_{\text{trial}} \in [e_{\min}, e_{\max}]$. This gain is calculated as:
        \begin{equation}
            \Delta W_i^{\text{EC}}(e_{\text{trial}}) = \sum_{j \in \mathcal{N}_i, y_j^t=0} B_j(t) \Delta \check{e}_j^t(e_{\text{trial}}) - B_i(t) e_{\text{trial}}
            \label{eq_ec_gain_est} % Keep existing label if appropriate
        \end{equation}
        where $\Delta \check{e}_j^t(e_{\text{trial}})$ is the estimated increase in RF energy harvested by neighbor $j$. This estimation uses a simplified approach ignoring interference: assume only node $i$ transmits energy with power $e_{\text{trial}}$, calculate the received RF power at node $j$ as $Rf_j^{\text{est}} = e_{\text{trial}} g_{i,j}^t$, and then use the non-linear RF energy harvesting model (Eq.~\eqref{eq_EH1}) to compute the corresponding harvested energy $\check{e}_j^{\text{est}} = \text{EH\_Model}(Rf_j^{\text{est}})$. The estimated energy increment is then $\Delta \check{e}_j^t(e_{\text{trial}}) = \check{e}_j^{\text{est}}$ (assuming zero baseline harvesting). This method does not account for interference from other potential concurrent energy or data transmissions.
        \item \textbf{Iterative Greedy Selection:} An iterative approach is used to select energy senders. In each iteration, select the node $i^*$ from the set of available potential senders that provides the maximum positive gain $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*)$ and satisfies the energy causality constraint $e_{\text{trial}}^* \le b_{i^*}^t$. If such an $i^*$ exists and $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*) > 0$, set $y_{i^*}^t = 1$ and $\hat{e}_{i^*}^t = e_{\text{trial}}^*$. Mark node $i^*$ as busy for this time slot (based on a Phase 1 priority mechanism, this node cannot participate in data transmission or reception). Repeat this iterative process until no potential sender can provide a positive gain.
        \item Nodes not selected as senders are potential receivers ($y_i^t=0$).
    \end{enumerate}

    \subsubsection{Phase 2: Data Transmission Decision \texorpdfstring{$(\mathbf{x}^t, \mathbf{p}^t, \mathbf{f}^t)$}{(x\textasciicircum t, p\textasciicircum t, f\textasciicircum t)}}
    Given the energy cooperation decisions $(\mathbf{y}^t, \mathbf{e}^t)$ (which determine the RF interference/harvesting environment for this phase), the goal is to select data links to activate ($x_{l,k}^t=1$), allocate power ($p_l^t$), and determine data flow ($f_{l,k}^t$) to maximize the relevant terms in $W^*(t)$.
    \begin{enumerate}
        \item \textbf{Calculate Link Weights:} For each potential link $l=(i,j)$ (where node $i$ is not transmitting energy ($y_i^t=0$) and node $j$ is not transmitting energy ($y_j^t=0$)), calculate a weight reflecting its contribution to $W^*(t)$. A common metric is the weighted queue difference plus the throughput bonus:
        \begin{equation}
            W_{l,k} = q_{i,k}(t) - q_{j,k}(t) + V \cdot \mathbb{I}(j=k)
            \label{eq_link_weight} % Keep existing label if appropriate
        \end{equation}
        where $\mathbb{I}(j=k)$ is 1 if the receiver $j$ is the final sink $k$, and 0 otherwise. This weight represents the data transport benefit per unit of flow $f_{l,k}^t$.
        \item \textbf{Calculate Net Weight:} Activating link $l=(i,j)$ with power $p_l^t$ for flow $f_{l,k}^t$ not only yields data transport benefits (captured by $W_{l,k}$) but also incurs energy costs (reflected by $-B_i(t) \hat{p}_i^t - B_j(t) \check{p}_j^t$). To evaluate the overall value of activating a link, we calculate its net weight $\text{NetWeight}_{l,k}(p_l^t)$:
        \begin{equation}
            \text{NetWeight}_{l,k}(p_l^t) = (W_{l,k}) f_{l,k}^t(p_l^t) - B_i(t) p_l^t - B_j(t) p_{\text{rcv}} f_{l,k}^t(p_l^t)
            \label{eq_net_link_weight} % Keep existing label if appropriate
        \end{equation}
        where $f_{l,k}^t(p_l^t)$ is the actual data flow from node $i$ via link $l$ towards sink $k$, given transmission power $p_l^t$. This flow is limited by the link capacity $r_l^t(p_l^t)$ (calculated using Eq.~\eqref{eq_r}), the sender's queue $q_{i,k}^t$, the receiver $j$'s available energy for reception, and the receiver $j$'s available buffer space. Specifically, it is calculated as:
        \begin{equation}
            f_{l,k}^t(p_l^t) = \min\left(r_l^t(p_l^t), q_{i,k}^t, \lfloor b_j^t / p_{\text{rcv}} \rfloor, q_{\max} - \sum_{k' \in \mathcal{K}} q_{j,k'}^t\right)
            \label{eq_f_l_k_calc} % New label for flow calculation
        \end{equation}
        \item \textbf{Iterative Greedy Link Selection:}
            a. Identify the set of potential data transmission links $\mathcal{L}_{\text{pot}} = \{l=(i,j) | i,j \text{ did not participate in energy transmission } (y_i^t=0, y_j^t=0) \text{ and } \exists k, q_{i,k}(t)>0\}$. Maintain a set of nodes currently available for data communication $\mathcal{N}_{\text{avail}}^{\text{data}}$, initially containing all nodes not involved in energy transmission.
            b. For each potential link $l=(i,j) \in \mathcal{L}_{\text{pot}}$ and relevant sink $k$ (i.e., $q_{i,k}(t)>0$), calculate its net weight $\text{NetWeight}_{l,k}(p_{\min})$ using a fixed trial power $p_{\text{trial}} = p_{\min}$ (using Eq.~\eqref{eq_net_link_weight} and Eq.~\eqref{eq_f_l_k_calc}). If $p_{\min} > b_i^t$, the link is infeasible, and its weight is considered negative infinity.
            c. Rank all feasible (link, sink) pairs $(l,k)$ based on their calculated $\text{NetWeight}_{l,k}(p_{\min})$ in descending order.
            d. Iterate through the ranked pairs $(l^*, k^*)$, where $l^*=(i^*, j^*)$:
            e. \textbf{Check Node Availability:} If both the sender $i^*$ and receiver $j^*$ are still in $\mathcal{N}_{\text{avail}}^{\text{data}}$.
            f. \textbf{Determine Final Power and Flow:} Determine the actual transmission power $p_{l^*}^t$ (set to $p_{\min}$ in this simplified scheme if feasible) and the corresponding actual flow $f_{l^*,k^*}^t$ (calculated using Eq.~\eqref{eq_f_l_k_calc}).
            g. \textbf{Activate Link:} If the calculated $\text{NetWeight}_{l^*,k^*}(p_{l^*}^t) > 0$ and the energy causality constraint $p_{l^*}^t \le b_{i^*}^t$ is met, activate the link: set $x_{l^*,k^*}^t = 1$, record $p_{l^*}^t$ and $f_{l^*,k^*}^t$.
            h. \textbf{Mark Nodes Busy:} Remove nodes $i^*$ and $j^*$ from $\mathcal{N}_{\text{avail}}^{\text{data}}$, as they cannot participate in other data link transmissions or receptions in this slot due to the single-link activation constraint per node.
            i. Continue processing the next pair in the ranked list until the list is exhausted.
    \end{enumerate}

    \subsection{Algorithm Pseudocode}
    The overall procedure is summarized in Algorithm~\ref{alg_lyapunov_mec}.

\begin{algorithm}
\caption{Lyapunov-MEC Algorithm}  
\label{alg_lyapunov_mec}  
\KwIn{Network $\mathcal{G}$, thresholds $\delta_i$, parameter $V$;}
Initialize $t=0$, $q_{i,k}(0)=0$, $B_i(0)=0$, $b_i(0)$;\\
\While{true}{
    Observe state $\Theta(t)=(\mathbf{q}(t), \mathbf{B}(t))$, $\mathbf{b}(t)$, and channel gains $\mathbf{g}^t$;\\
    Initialize decisions $\mathbf{x}^t=\mathbf{0}, \mathbf{y}^t=\mathbf{0}, \mathbf{p}^t=\mathbf{0}, \mathbf{e}^t=\mathbf{0}$;\\
    Set of available nodes $\mathcal{N}_{\text{avail}} = \mathcal{N}$; Set of activated data links $\mathbb{S}_D^t = \emptyset$;
    
    \tcp{Phase 1: Energy Cooperation Decision}
    Identify potential senders $\mathcal{S}_{\text{pot}}$ and receivers $\mathcal{R}_{\text{pot}}$;\\
    Calculate potential gains $\Delta W_i^{\text{EC}}$ for $i \in \mathcal{S}_{\text{pot}}$ using Eq.~\eqref{eq_ec_gain_est} (heuristic estimation);\\
    Select a set of energy senders $\mathbb{S}_E^t \subseteq \mathcal{S}_{\text{pot}}$ based on gains and constraints (e.g., greedy selection);\\
    \For{$i \in \mathbb{S}_E^t$}{
        Set $y_i^t = 1$, determine $\hat{e}_i^t \in [e_{\min}, \min(e_{\max}, b_i^t)]$;\\
        $\mathcal{N}_{\text{avail}} = \mathcal{N}_{\text{avail}} \setminus \{i\}$; \tcp{Mark sender as busy}
    }
    Update estimated RF environment based on $\mathbf{y}^t, \mathbf{e}^t$;
    
    \tcp{Phase 2: Data Transmission Decision}
    Identify potential data links $\mathcal{L}_{\text{pot}} = \{l=(i,j) | i,j \in \mathcal{N}_{\text{avail}}, \exists k, q_{i,k}(t)>0\}$;\\
    Calculate $\text{NetWeight}_{l,k}$ for $(l,k)$ pairs using Eq.~\eqref{eq_net_link_weight} (heuristic evaluation, e.g., using trial power);\\
    Sort potential $(l,k)$ pairs by $\text{NetWeight}_{l,k}$ in descending order;\\
    \For{each ranked pair $(l^*, k^*)$ where $l^*=(i^*, j^*)$}{
        \If{$i^* \in \mathcal{N}_{\text{avail}}$ and $j^* \in \mathcal{N}_{\text{avail}}$}{
            Determine optimal feasible power $p_{l^*}^t$ and flow $f_{l^*,k^*}^t$ for this link;\\
            \If{Activation is feasible and beneficial ($\text{NetWeight}>0$)}{
                Set $x_{l^*,k^*}^t = 1$, store $p_{l^*}^t$, $f_{l^*,k^*}^t$;\\
                $\mathbb{S}_D^t = \mathbb{S}_D^t \cup \{l^*\}$;\\
                $\mathcal{N}_{\text{avail}} = \mathcal{N}_{\text{avail}} \setminus \{i^*, j^*\}$; \tcp{Mark nodes as busy}
            }
        }
    }
    Calculate final $\hat{p}_i^t, \check{p}_i^t$ based on activated links $\mathbb{S}_D^t$;
    
    \tcp{Update State}
    Calculate actual energy harvested $\check{e}_i^t$ based on final $\mathbf{y}^t, \mathbf{e}^t, \mathbf{p}^t$;\\
    Calculate total consumption $\text{Cons}_i^t = \hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t + s_i^t$;\\
    Calculate actual energy stored $\bar{e}_i^t = \min(\check{e}_i^t + h_i^t, b_{\max} - (b_i(t) - \text{Cons}_i^t))$;\\
    Update $b_i(t+1) = b_i(t) - \text{Cons}_i^t + \bar{e}_i^t$;\\
    Update $q_{i,k}(t+1)$ using Eq.~\eqref{eq_q} with final $f_{l,k}^t$;\\
    Update $B_i(t+1)$ using Eq.~\eqref{eq_virtual_energy_queue};\\
    $t = t+1$;
}
\end{algorithm}

    \subsection{Complexity Analysis}
    The complexity of Lyapunov-MEC per time slot depends heavily on the heuristics used in Phase 1 and Phase 2. 
    Phase 1 involves identifying potential senders/receivers ($\mathcal{O}(N^2)$ or $\mathcal{O}(E)$) and estimating gains. If gain estimation involves iterating through neighbors, it could be $\mathcal{O}(N^2)$. Greedy selection might take $\mathcal{O}(N \log N)$.
    Phase 2 involves calculating weights for potential links ($\mathcal{O}(E \cdot K)$). Evaluating NetWeight might require checking power levels. Sorting takes $\mathcal{O}(EK \log(EK))$. The greedy selection loop iterates at most $\mathcal{O}(E)$ times, with constraint checks potentially taking $\mathcal{O}(N)$ or $\mathcal{O}(E)$ time within the loop.
    Overall, a rough estimate might be dominated by the sorting step, leading to a per-slot complexity around $\mathcal{O}(EK \log(EK))$, similar to GDTS, although the constant factors might differ based on the complexity of the gain/weight calculations. A precise analysis requires specifying the heuristics in more detail.

	
	The Lyapunov-MEC algorithm provides an online control policy that dynamically adapts to network conditions (queue backlogs, energy levels, channel states) to optimize LTA throughput while managing energy constraints. Its performance relative to the original GDTS algorithm and theoretical bounds needs to be evaluated through simulations.

    \section{Theoretical Analysis of Lyapunov-MEC}
    \label{sec_analysis}
    In this section, we provide theoretical guarantees for the proposed Lyapunov-MEC algorithm (Algorithm~\ref{alg_lyapunov_mec}). We demonstrate that the algorithm ensures the stability of all network queues (data and virtual energy deficit queues) and achieves an LTA throughput close to the optimal value. The analysis follows the standard methodology of Lyapunov optimization~\cite{neely2010stochastic}.

    \subsection{Queue Stability Guarantee}
    We first establish the stability of all queues under the Lyapunov-MEC algorithm. Queue stability is fundamental as it ensures bounded data delays and guarantees that the LTA energy constraints are met.

    \begin{lemma}[Queue Stability]
        \label{lemma_queue_stability}
        Assume the vector of average data arrival rates $\lambda = (E[a_{i,k}^t])$ is strictly within the stability region $\Lambda$ of the network. The stability region $\Lambda$ is the set of all arrival rate vectors for which there exists some stationary randomized policy $\pi^*$ that can satisfy all network constraints and stabilize all queues. If the Lyapunov-MEC algorithm (Algorithm~\ref{alg_lyapunov_mec}) is executed with parameter $V > 0$, then all data queues $q_{i,k}(t)$ and energy deficit queues $B_i(t)$ are strongly stable. That is, their long-term time average values are bounded:
        \begin{align}
            \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} \sum_{i,k} E[q_{i,k}(t)] &< \infty \label{eq_data_queue_stable}\\
            \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} \sum_{i} E[B_i(t)] &< \infty \label{eq_energy_queue_stable}
        \end{align}
    \end{lemma}
    \begin{proof}
        The proof relies on analyzing the Lyapunov drift-plus-penalty expression. From Section V.C, the algorithm minimizes the drift-plus-penalty upper bound in each slot. Let the minimized value be $Y_{min}(t)$ and the value under the optimal stationary policy $\pi^*$ be $Y^*(t)$. We have:
        \begin{equation}
            \Delta L(\Theta(t)) - V E[\text{DeliveredData}(t) | \Theta(t)] \le Y_{min}(t)
        \end{equation}
        Since Lyapunov-MEC minimizes the expression over all feasible policies for the current slot, its performance is at least as good as that of the stationary policy $\pi^*$ applied to the current state:
        \begin{equation}
            Y_{min}(t) \le Y^*(t) = E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
        \end{equation}
        where $\Delta L^*(\Theta(t))$ is the drift under policy $\pi^*$.
        Combining these, we get:
        \begin{align}
            \Delta L(\Theta(t)) &- V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
            &\le E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
        \end{align}
        Since $\lambda$ is strictly inside the stability region $\Lambda$, there exists an $\epsilon > 0$ such that policy $\pi^*$ satisfies the stability conditions:
        \begin{align}
            E[A_{i,k}^*(t) | \Theta(t)] &\le E[\text{Outflow}_{i,k}^*(t) | \Theta(t)] + E[a_{i,k}^t] - \epsilon \\
            E[b_i^*(t+1) | \Theta(t)] &\ge \delta_i + \epsilon
        \end{align}
        Using these conditions, it can be shown (following standard Lyapunov analysis~\cite{neely2010stochastic})
        that the drift under $\pi^*$ satisfies:
        \begin{equation}
            E[\Delta L^*(\Theta(t)) | \Theta(t)] \le C^* - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t))
        \end{equation}
        for some constants $C^*, \epsilon' > 0$.
        Substituting this back into the drift-plus-penalty inequality:
        \begin{align}
            & \Delta L(\Theta(t)) - V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
            &\le C^* - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t)) - V E[\text{DeliveredData}^*(t) | \Theta(t)]
        \end{align}
        Since $E[\text{DeliveredData}(t)]$ and $E[\text{DeliveredData}^*(t)]$ are non-negative, we can bound the expression:
        \begin{equation}
            \Delta L(\Theta(t)) \le C^{**} - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t))
        \end{equation}
        where $C^{**} = C^* + V \cdot \text{MaxPossibleThroughput}$ is a constant (assuming throughput is bounded).
        This inequality shows that the drift is negative whenever the weighted sum of queue lengths $\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t)$ is sufficiently large (larger than $C^{**}/\epsilon'$). According to the Foster-Lyapunov stability criterion~\cite{meyn2012markov},
        this negative drift condition implies that the system state (represented by the queues) is positive recurrent and thus stable. Strong stability (bounded average queue size) follows under mild conditions on the second moments of the arrival and service processes.
    \end{proof}

    \subsection{LTA Energy Constraint Satisfaction}
    The stability of the virtual energy deficit queues $B_i(t)$ directly implies that the LTA energy constraints are satisfied, as shown in the following corollary, similar to Lemma 2 in~\cite{IOT_manuscript_0306}.

    \begin{corollary}[LTA Energy Constraint Satisfaction]
        \label{corollary_energy_constraint}
        Under the conditions of Lemma~\ref{lemma_queue_stability}, the Lyapunov-MEC algorithm ensures that the LTA battery energy constraint (Eq.~\eqref{eq_lta_energy}) is satisfied for all nodes $i \in \mathcal{N}$:
        $$ \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge \delta_i $$
    \end{corollary}
    \begin{proof}
        From the update rule of the virtual queue $B_i(t)$ (Eq.~\eqref{eq_virtual_energy_queue}), we have $B_i(t+1) \ge B_i(t) + \delta_i - b_i(t+1)$.
        Rearranging this gives:
        \begin{equation}
            b_i(t+1) \ge B_i(t) - B_i(t+1) + \delta_i
        \end{equation}
        Summing this inequality from $t=0$ to $T-1$:
        \begin{align}
            \sum_{t=0}^{T-1} b_i(t+1) &\ge \sum_{t=0}^{T-1} (B_i(t) - B_i(t+1)) + \sum_{t=0}^{T-1} \delta_i \nonumber \\
            &= (B_i(0) - B_i(1)) + (B_i(1) - B_i(2)) + \dots + (B_i(T-1) - B_i(T)) + T \delta_i \nonumber \\
            &= B_i(0) - B_i(T) + T \delta_i
        \end{align}
        Taking expectation on both sides:
        \begin{equation}
            \sum_{t=0}^{T-1} E[b_i(t+1)] \ge E[B_i(0)] - E[B_i(T)] + T \delta_i
        \end{equation}
        Dividing by $T$:
        \begin{equation}
            \frac{1}{T} \sum_{t=1}^{T} E[b_i(t)] \ge \frac{E[B_i(0)]}{T} - \frac{E[B_i(T)]}{T} + \delta_i
        \end{equation}
        Taking the limit inferior as $T \to \infty$:
        \begin{equation}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=1}^{T} E[b_i(t)] \ge \liminf_{T \to \infty} \left( \frac{E[B_i(0)]}{T} - \frac{E[B_i(T)]}{T} + \delta_i \right)
        \end{equation}
        Since $B_i(0)$ is a finite initial value, $\lim_{T \to \infty} E[B_i(0)]/T = 0$. From Lemma~\ref{lemma_queue_stability}, $B_i(t)$ is strongly stable, which implies that the average queue size is bounded, $ \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[B_i(t)] < \infty$. This further implies that $\lim_{T \to \infty} E[B_i(T)]/T = 0$ (otherwise the average would grow unboundedly).
        Therefore, we have:
        \begin{equation}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge 0 - 0 + \delta_i = \delta_i
        \end{equation}
        This confirms that the LTA battery energy constraint is satisfied.
    \end{proof}

    \subsection{LTA Throughput Performance Bound}
    Finally, we establish a lower bound on the LTA throughput achieved by the Lyapunov-MEC algorithm relative to the optimal possible throughput, similar to Lemma 3 in~\cite{IOT_manuscript_0306}.

    \begin{lemma}[LTA Throughput Bound]
        \label{lemma_throughput_bound}
        Let $\mathcal{D}^*$ be the maximum achievable LTA throughput subject to the network constraints (potentially under an optimal, possibly clairvoyant policy $\pi^*$). The LTA throughput $\bar{\mathcal{D}}$ achieved by the Lyapunov-MEC algorithm (Algorithm~\ref{alg_lyapunov_mec}) satisfies:
        \begin{equation}
            \label{eq_throughput_lower_bound}
            \bar{\mathcal{D}} = \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \ge \mathcal{D}^* - \frac{C}{V}
        \end{equation}
        where $C$ is the constant from the drift bound (Eq.~\eqref{eq_drift_upper_bound_detailed}) related to the bounds on squared arrivals and service rates, and $V$ is the control parameter.
    \end{lemma}
    \begin{proof}
        From the proof of Lemma~\ref{lemma_queue_stability}, we have the drift-plus-penalty bound:
        \begin{align}
                     \Delta L(\Theta(t)) &- V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
                     &\le E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
                \end{align}
        Taking expectation over $\Theta(t)$:
        \begin{align}
                     E[\Delta L(\Theta(t))] &- V E[\text{DeliveredData}(t)] \nonumber \\
                     &\le E[\Delta L^*(\Theta(t))] - V E[\text{DeliveredData}^*(t)]
                \end{align}
        The term $E[\Delta L^*(\Theta(t))]$ represents the expected drift under the optimal stationary policy $\pi^*$. For any stationary policy that stabilizes the system, the long-term average drift must be non-positive. Furthermore, under standard assumptions, it can be bounded by a constant, say $C_{drift}^*$. Thus, $E[\Delta L^*(\Theta(t))] \le C_{drift}^*$. Also, by definition, $E[\text{DeliveredData}^*(t)] = \mathcal{D}^*$.
        Substituting these into the inequality:
        \begin{equation}
             E[L(\Theta(t+1)) - L(\Theta(t))] - V E[\text{DeliveredData}(t)] \le C_{drift}^* - V \mathcal{D}^*
        \end{equation}
        Summing from $t=0$ to $T-1$:
        \begin{align}
            & \sum_{t=0}^{T-1} (E[L(\Theta(t+1))] - E[L(\Theta(t))]) - V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
            &\le T C_{drift}^* - T V \mathcal{D}^*
        \end{align}
        The first sum is a telescoping sum: $E[L(\Theta(T))] - E[L(\Theta(0))]$.
        \begin{align}
                    E[L(\Theta(T))] &- E[L(\Theta(0))] \nonumber \\
                    &- V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
                    &\le T C_{drift}^* - T V \mathcal{D}^*
                \end{align}
        Since $L(\Theta(t)) \ge 0$, we have $- E[L(\Theta(0))] - V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \le T C_{drift}^* - T V \mathcal{D}^*$.
        Rearranging and dividing by $VT$ (assuming $V>0$):
        \begin{equation}
            \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \ge \mathcal{D}^* - \frac{C_{drift}^*}{V} - \frac{E[L(\Theta(0))]}{VT}
        \end{equation}
        Taking the limit inferior as $T \to \infty$:
        \begin{align}
            \bar{\mathcal{D}} &= \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
            &\ge \mathcal{D}^* - \frac{C_{drift}^*}{V} + \liminf_{T \to \infty} \left( - \frac{E[L(\Theta(0))]}{VT} \right)
        \end{align}
        Since $L(\Theta(0))$ is finite, $\lim_{T \to \infty} E[L(\Theta(0))]/(VT) = 0$.
        Thus, we obtain the desired result:
        \begin{equation}
            \bar{\mathcal{D}} \ge \mathcal{D}^* - \frac{C_{drift}^*}{V}
        \end{equation}
        We can replace the constant $C_{drift}^*$ with the constant $C$ from the general drift bound (Eq.~\eqref{eq_drift_upper_bound_detailed}), as they represent bounds on similar system dynamics, yielding $\bar{\mathcal{D}} \ge \mathcal{D}^* - C/V$.
    \end{proof}

    Lemma~\ref{lemma_throughput_bound} quantifies the performance guarantee. It shows that the LTA throughput achieved by Lyapunov-MEC can be made arbitrarily close to the optimal throughput $\mathcal{D}^*$ by choosing a sufficiently large value for the control parameter $V$. The term $C/V$ represents the optimality gap. However, increasing $V$ typically leads to larger average queue sizes (as implied by the stability proof structure, often scaling as $O(V)$), representing the fundamental $[O(1/V), O(V)]$ trade-off between average performance optimality and queue backlog/delay in stochastic network optimization.

\bibliographystyle{IEEEtran}
\bibliography{mybibfile}
	
\end{document}