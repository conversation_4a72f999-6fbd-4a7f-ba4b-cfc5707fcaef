#!/usr/bin/env python3
"""
Performance comparison test for GDTS algorithm.
Runs a quick comparison between GDTS and other algorithms to verify performance.
"""

import sys
import time
import numpy as np

# Import simulation components
from simulation import run_single_simulation
from comparison_algorithms import (
    gdts_scheduling, gdts_no_ec_scheduling, 
    gmw_scheduling, rand_scheduling
)

def test_gdts_performance_comparison():
    """Test GDTS performance compared to other algorithms."""
    print("Testing GDTS performance comparison...")
    
    # Temporarily modify simulation parameters for faster testing
    import simulation
    original_total_slots = simulation.TOTAL_TIME_SLOTS
    original_num_nodes = simulation.NUM_NODES
    original_num_sinks = simulation.NUM_SINKS
    
    # Set parameters for meaningful comparison
    simulation.TOTAL_TIME_SLOTS = 500  # Moderate size for comparison
    simulation.NUM_NODES = 5  # Small network
    simulation.NUM_SINKS = 2  # Two sinks
    
    try:
        # Test algorithms
        algorithms_to_test = {
            "GDTS": gdts_scheduling,
            "GDTS-NoEC": gdts_no_ec_scheduling,
            "GMW": gmw_scheduling,
            "RAND": rand_scheduling
        }
        
        results = {}
        
        for algo_name, scheduling_func in algorithms_to_test.items():
            print(f"\n  Running {algo_name}...")
            
            try:
                start_time = time.time()
                result = run_single_simulation(scheduling_func)
                end_time = time.time()
                
                results[algo_name] = {
                    'runtime': end_time - start_time,
                    'throughput': result['final_avg_throughput_mbps'],
                    'delivered': result['final_total_delivered_mbits'],
                    'avg_battery': result['time_avg_battery'],
                    'avg_queue': result['time_avg_queue_mbit']
                }
                
                print(f"    ✓ {algo_name} completed in {results[algo_name]['runtime']:.2f}s")
                
            except Exception as e:
                print(f"    ❌ {algo_name} failed: {e}")
                return False
        
        # Print comparison results
        print("\n" + "="*60)
        print("PERFORMANCE COMPARISON RESULTS")
        print("="*60)
        print(f"{'Algorithm':<12} {'Runtime(s)':<10} {'Throughput(Mbps)':<16} {'Delivered(Mbits)':<16} {'Avg Battery(J)':<15} {'Avg Queue(Mbits)':<15}")
        print("-"*60)
        
        for algo_name, metrics in results.items():
            print(f"{algo_name:<12} {metrics['runtime']:<10.2f} {metrics['throughput']:<16.4f} "
                  f"{metrics['delivered']:<16.2f} {metrics['avg_battery']:<15.2f} {metrics['avg_queue']:<15.2f}")
        
        # Verify GDTS performs reasonably
        gdts_throughput = results['GDTS']['throughput']
        gdts_noec_throughput = results['GDTS-NoEC']['throughput']
        gmw_throughput = results['GMW']['throughput']
        
        print("\n" + "="*60)
        print("PERFORMANCE ANALYSIS")
        print("="*60)
        
        # Check that GDTS performs at least as well as random
        if gdts_throughput >= results['RAND']['throughput']:
            print("✓ GDTS throughput >= RAND throughput")
        else:
            print("⚠ GDTS throughput < RAND throughput (may be acceptable)")
        
        # Check that GDTS with EC performs at least as well as GDTS without EC
        if gdts_throughput >= gdts_noec_throughput:
            print("✓ GDTS throughput >= GDTS-NoEC throughput (energy cooperation helps)")
        else:
            print("⚠ GDTS throughput < GDTS-NoEC throughput (energy cooperation may not be beneficial in this scenario)")
        
        # Check runtime is reasonable
        max_runtime = max(metrics['runtime'] for metrics in results.values())
        if max_runtime < 30:  # Should complete within 30 seconds
            print("✓ All algorithms completed within reasonable time")
        else:
            print("⚠ Some algorithms took longer than expected")
        
        # Check that all algorithms delivered some data (if network is connected)
        total_delivered = sum(metrics['delivered'] for metrics in results.values())
        if total_delivered > 0:
            print("✓ At least some data was delivered across algorithms")
        else:
            print("⚠ No data delivered (may indicate network connectivity issues)")
        
        return True
        
    finally:
        # Restore original parameters
        simulation.TOTAL_TIME_SLOTS = original_total_slots
        simulation.NUM_NODES = original_num_nodes
        simulation.NUM_SINKS = original_num_sinks

def test_gdts_energy_conservation():
    """Test that GDTS respects energy conservation principles."""
    print("\nTesting GDTS energy conservation...")
    
    # Create a simple test scenario
    from test_gdts import create_test_network
    nodes, sinks, potential_links, current_channel_gains = create_test_network()
    
    # Store initial energy
    initial_total_energy = sum(node['battery'] for node in nodes)
    
    # Run GDTS for one slot
    actions = gdts_scheduling(0, nodes, sinks, potential_links, 
                             current_channel_gains, 1e-12, 1e6)
    
    # Calculate energy consumption
    total_data_tx_energy = sum(actions['p'].values())
    total_energy_tx_energy = sum(actions['e_hat'].values())
    total_energy_consumed = total_data_tx_energy + total_energy_tx_energy
    
    print(f"  Initial total energy: {initial_total_energy:.2f} J")
    print(f"  Data transmission energy: {total_data_tx_energy:.2f} J")
    print(f"  Energy cooperation energy: {total_energy_tx_energy:.2f} J")
    print(f"  Total energy consumed: {total_energy_consumed:.2f} J")
    
    # Verify energy consumption is reasonable
    if total_energy_consumed <= initial_total_energy:
        print("  ✓ Energy consumption does not exceed available energy")
    else:
        print("  ❌ Energy consumption exceeds available energy")
        return False
    
    # Verify GDTS parameters are respected
    for power in actions['p'].values():
        if not (0.5 <= power <= 1.5):  # GDTS power range
            print(f"  ❌ Data transmission power {power} outside GDTS range [0.5, 1.5]")
            return False
    
    for power in actions['e_hat'].values():
        if power > 0 and not (0.5 <= power <= 1.0):  # GDTS energy cooperation range
            print(f"  ❌ Energy cooperation power {power} outside GDTS range [0.5, 1.0]")
            return False
    
    print("  ✓ All power values within GDTS parameter ranges")
    return True

def run_performance_tests():
    """Run all performance tests."""
    print("=" * 60)
    print("GDTS Performance Test Suite")
    print("=" * 60)
    
    try:
        success = True
        success &= test_gdts_performance_comparison()
        success &= test_gdts_energy_conservation()
        
        if success:
            print("\n" + "=" * 60)
            print("✓ All GDTS performance tests passed successfully!")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ Some GDTS performance tests failed!")
            print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Performance test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_performance_tests()
    sys.exit(0 if success else 1)
