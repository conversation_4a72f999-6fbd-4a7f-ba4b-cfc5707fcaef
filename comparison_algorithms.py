# comparison_algorithms.py
# Implements GMW, EAG, and RAND scheduling algorithms for comparison.

import numpy as np
import random
import math

# --- Necessary Constants (Copied from simulation.py for standalone execution) ---
# Note: Ideally, constants would be managed in a shared config file or module.
# Copying them here for simplicity based on user feedback.

# Communication Parameters
P_MIN_dBm = 5.0
P_MIN_W = 10**(P_MIN_dBm / 10) / 1000 # p_min in Watts

# Energy Consumption
P_RCV_J_per_Mbit = 50e-6 # p_rcv (Joules per Mbit)
P_RCV_J_per_bit = P_RCV_J_per_Mbit / 1e6

# Queue Capacity
QUEUE_MAX_Mbit = 50.0 # q_max (Mbit)
QUEUE_MAX_bits = QUEUE_MAX_Mbit * 1e6 # q_max (bits)

# --- Helper Functions (Copied from simulation.py) ---

def get_device_by_id(device_id, nodes, sinks):
    """Helper to find a node or sink by its ID."""
    for node in nodes:
        if node['id'] == device_id:
            return node
    for sink in sinks:
        if sink['id'] == device_id:
            return sink
    return None

# Need necessary functions called by calculate_potential_flow
def calculate_snr(power_tx_W, instantaneous_channel_gain, noise_power_W):
    """Calculates SNR based on transmit power and instantaneous channel gain."""
    if instantaneous_channel_gain <= 0 or noise_power_W <= 0:
        return 0
    snr = (power_tx_W * instantaneous_channel_gain) / noise_power_W
    return max(0, snr) # Ensure SNR is non-negative

def calculate_data_rate_bits_per_slot(snr, bandwidth):
    """Calculates data rate in bits per slot using Shannon capacity."""
    if snr <= 0:
        return 0
    # Assuming unit time slot (1 second), Rate (bps) = Capacity
    # Use math.log2 for base 2 logarithm
    try:
        # Ensure argument to log2 is strictly positive
        rate = bandwidth * math.log2(1 + max(snr, 1e-12))
    except ValueError: # Handle potential domain error if 1+snr is negative (shouldn't happen with max)
        rate = 0
    return rate

def calculate_potential_flow(sender_node, receiver_dev, sink_k_id, power_tx_W, channel_gain_ij, e_hat_j,
                             noise_power_W, bandwidth): # Added noise_power_W, bandwidth
    """
    Calculates the potential data flow f_lk^t based on constraints.
    Args:
        sender_node: Dictionary of the sending node.
        receiver_dev: Dictionary of the receiving device (node or sink).
        sink_k_id: ID of the final destination sink.
        power_tx_W: Transmit power used.
        channel_gain_ij: Instantaneous channel gain between sender and receiver.
        e_hat_j: Energy cooperation transmission power of the receiver (0 if not transmitting).
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        Potential data flow in bits.
    """
    # 1. Calculate Link Capacity (Rate) r_l^t (Eq. 7)
    snr = calculate_snr(power_tx_W, channel_gain_ij, noise_power_W) # Pass noise power
    rate_bits = calculate_data_rate_bits_per_slot(snr, bandwidth) # Pass bandwidth

    # 2. Sender Queue Constraint f_lk^t <= q_ik^t (Eq. 8b) - Use queue_bits
    sender_queue_bits = sender_node['queue_bits'].get(sink_k_id, 0.0)

    # 3. Receiver Constraints (only apply if receiver is a node, not a sink)
    receiver_energy_constraint_bits = float('inf')
    receiver_buffer_constraint_bits = float('inf')

    if not receiver_dev['is_sink']:
        # Receiver Energy Constraint f_lk^t <= (b_j^t - e_hat_j) / p_rcv (Eq. 8c, adapted)
        available_energy_for_reception = max(0, receiver_dev['battery'] - e_hat_j)
        if P_RCV_J_per_bit > 1e-12:
             receiver_energy_constraint_bits = math.floor(available_energy_for_reception / P_RCV_J_per_bit)
        else:
              receiver_energy_constraint_bits = float('inf')

        # Receiver Buffer Constraint f_lk^t <= q_max - sum(q_jk') (Eq. 8d)
        current_total_queue_j = sum(receiver_dev['queue_bits'].values())
        receiver_buffer_constraint_bits = max(0, QUEUE_MAX_bits - current_total_queue_j)

    # Determine the actual flow based on the minimum of all constraints
    potential_flow_bits = min(rate_bits,
                              sender_queue_bits,
                              receiver_energy_constraint_bits,
                              receiver_buffer_constraint_bits)

    return max(0, potential_flow_bits)


# Need access to global packet_details
# This assumes simulation.py defines packet_details globally
# It's generally better practice to pass it, but reverting based on user feedback
# from simulation import packet_details # Moved inside functions to avoid circular import

# --- GMW Scheduling Algorithm ---
def gmw_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                   noise_power_W, bandwidth): # packet_details removed from args
    """
    Performs Greedy-MaxWeight (GMW) scheduling.
    Selects links greedily based on queue difference weight W_lk = q_ik - q_jk,
    subject to basic energy and half-duplex constraints. No energy cooperation.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        packet_details: Dict {packet_id: details}. Needed for packet selection.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): float_bits_value}   # Data flow (BITS, not packet IDs)
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for GMW
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    nodes_data_busy = set()
    candidate_links_tuples = [] # List of ((i, j, k), Weight_Value, f_potential_bits)

    # 1. Calculate Weight W_lk = q_ik - q_jk for all potential links
    num_nodes = len(nodes) # Get number of nodes dynamically
    for sender_id in range(num_nodes): # Iterate up to num_nodes
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # Basic energy check: Can sender afford p_min?
        if sender_node['battery'] < P_MIN_W:
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)
            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']: # Iterate using queue_bits keys
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now rely on f_potential_bits > 0 check below
                p_trial_W = P_MIN_W # Use p_min for evaluation

                # Calculate potential flow (needed for receiver energy check and packet selection)
                    # GMW doesn't use e_hat, so pass 0.0
                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_trial_W, channel_gain_ij, e_hat_j=0.0,
                                                                noise_power_W=noise_power_W, bandwidth=bandwidth) # Pass necessary params

                if f_potential_bits > 1e-9:
                    # Basic receiver energy check (if receiver is a node)
                    receiver_energy_ok = True
                    if not receiver_dev['is_sink']:
                        required_rcv_energy = P_RCV_J_per_bit * f_potential_bits
                        receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

                    if receiver_energy_ok:
                        # Calculate GMW Link Weight W_lk = q_ik - q_jk
                        q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0)
                        q_jk = 0.0
                        if not receiver_dev['is_sink']:
                            q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0)
                        # GMW weight doesn't include the V term
                        gmw_weight = q_ik - q_jk

                        # Only consider links with positive weight
                        if gmw_weight > 0:
                            candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), gmw_weight, f_potential_bits))

    # 2. Sort candidates by GMW Weight
    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    # 3. Iteratively select links based on max weight and constraints
    for candidate in candidate_links_tuples:
        (i, j, k), weight, flow_bits = candidate # weight is gmw_weight

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        # Check half-duplex constraint
        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            p_actual_W = P_MIN_W

            # Final energy checks (re-check just before activation)
            sender_energy_ok = sender_node['battery'] >= p_actual_W
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9:
                # --- Packet selection logic removed - Using flow_bits directly ---

                # --- Store flow_bits directly for GMW/EAG/RAND ---
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                    p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow bits directly
                f_decisions[link_key] = flow_bits

                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                # --- MODIFICATION END ---
                        nodes_data_busy.add(j)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions

# --- EAG Scheduling Algorithm ---
def eag_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                   noise_power_W, bandwidth, battery_max_J): # packet_details removed from args
    """
    Performs Energy-Aware Greedy (EAG) scheduling.
    Selects links greedily based on an energy-aware weight: W_lk = (q_ik - q_jk) * (b_i / b_max),
    subject to basic energy and half-duplex constraints. No energy cooperation.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        packet_details: Dict {packet_id: details}. Needed for packet selection.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
        battery_max_J: Maximum battery capacity (b_max).
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): float_bits_value}   # Data flow (BITS, not packet IDs)
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for EAG
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    nodes_data_busy = set()
    candidate_links_tuples = [] # List of ((i, j, k), Weight_Value, f_potential_bits)

    # 1. Calculate Energy-Aware Weight for all potential links
    num_nodes = len(nodes)
    for sender_id in range(num_nodes):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # Basic energy check: Can sender afford p_min?
        if sender_node['battery'] < P_MIN_W:
            continue

        # Calculate sender energy factor (b_i / b_max)
        # Add epsilon to avoid division by zero if b_max is somehow 0
        energy_factor = sender_node['battery'] / (battery_max_J + 1e-9)
        energy_factor = max(0, min(energy_factor, 1.0)) # Clamp between 0 and 1

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)
            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']:
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now rely on f_potential_bits > 0 check below
                p_trial_W = P_MIN_W

                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_trial_W, channel_gain_ij, e_hat_j=0.0,
                                                                 noise_power_W=noise_power_W, bandwidth=bandwidth)

                if f_potential_bits > 1e-9:
                    receiver_energy_ok = True
                    if not receiver_dev['is_sink']:
                        required_rcv_energy = P_RCV_J_per_bit * f_potential_bits
                        receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

                    if receiver_energy_ok:
                        # Calculate GMW part of the weight
                        q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0)
                        q_jk = 0.0
                        if not receiver_dev['is_sink']:
                            q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0)
                        gmw_weight_part = q_ik - q_jk

                        # Calculate EAG weight
                        eag_weight = gmw_weight_part * energy_factor

                        # Only consider links with positive GMW part (or adjust logic if needed)
                        # and positive final EAG weight
                        if gmw_weight_part > 0 and eag_weight > 0:
                            candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), eag_weight, f_potential_bits))

    # 2. Sort candidates by EAG Weight
    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    # 3. Iteratively select links (same logic as GMW, but uses EAG weights for sorting)
    for candidate in candidate_links_tuples:
        (i, j, k), weight, flow_bits = candidate # weight is eag_weight

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            p_actual_W = P_MIN_W

            sender_energy_ok = sender_node['battery'] >= p_actual_W
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9:
                # --- Packet selection logic removed - Using flow_bits directly ---

                # --- Store flow_bits directly for GMW/EAG/RAND ---
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                    p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow bits directly
                f_decisions[link_key] = flow_bits

                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                # --- MODIFICATION END ---
                        nodes_data_busy.add(j)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions


# --- RAND Scheduling Algorithm ---
def rand_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                    noise_power_W, bandwidth): # packet_details removed from args
    """
    Performs Randomized (RAND) scheduling.
    Randomly selects links that satisfy basic energy and half-duplex constraints.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        packet_details: Dict {packet_id: details}. Needed for packet selection.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): float_bits_value}   # Data flow (BITS, not packet IDs)
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for RAND
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    nodes_data_busy = set()
    feasible_links_tuples = [] # List of ((i, j, k), f_potential_bits)

    # 1. Identify all feasible links satisfying basic energy constraints
    num_nodes = len(nodes)
    for sender_id in range(num_nodes):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # Basic sender energy check
        if sender_node['battery'] < P_MIN_W:
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)
            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']:
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now rely on f_potential_bits > 0 check below
                p_trial_W = P_MIN_W

                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_trial_W, channel_gain_ij, e_hat_j=0.0,
                                                                 noise_power_W=noise_power_W, bandwidth=bandwidth)

                if f_potential_bits > 1e-9:
                    # Basic receiver energy check
                    receiver_energy_ok = True
                    if not receiver_dev['is_sink']:
                        required_rcv_energy = P_RCV_J_per_bit * f_potential_bits
                        receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

                    if receiver_energy_ok:
                        # Add feasible link to the list
                        feasible_links_tuples.append(((sender_id, receiver_id, sink_k_id), f_potential_bits))

    # 2. Shuffle the list of feasible links randomly
    random.shuffle(feasible_links_tuples)

    # 3. Iterate through the shuffled list and activate links respecting half-duplex
    for candidate in feasible_links_tuples:
        (i, j, k), flow_bits = candidate

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        # Check half-duplex constraint
        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            # Re-check energy constraints just in case state changed (though unlikely in RAND)
            p_actual_W = P_MIN_W
            sender_energy_ok = sender_node['battery'] >= p_actual_W
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9:
                # --- Packet selection logic removed - Using flow_bits directly ---

                # --- Store flow_bits directly for GMW/EAG/RAND ---
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                    p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow bits directly
                f_decisions[link_key] = flow_bits

                # Mark nodes as busy
                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                # --- MODIFICATION END ---
                        nodes_data_busy.add(j)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions


# --- GDTS Algorithm Implementation ---
# Based on Jiang's paper: "Multicast Energy Cooperation Assisted Data Collection Scheduling"

# GDTS-specific parameters (adapted for simulation framework compatibility)
# Original Jiang's paper values were: p_min=0.5W, p_max=1.5W, γ_min=10dB, b_min=3.0J
# Adjusted to match simulation framework parameter regime for fair comparison
GDTS_P_MIN_W = 0.01  # p_min in Watts (matches simulation P_MIN_W)
GDTS_P_MAX_W = 0.1   # p_max in Watts (matches simulation P_MAX_W)
GDTS_E_MIN_W = 0.01  # e_min in Watts (scaled proportionally)
GDTS_E_MAX_W = 0.05  # e_max in Watts (scaled proportionally)
GDTS_B_MIN_J = 1.0   # b_min in Joules (more realistic for 10J max capacity)
GDTS_GAMMA_MIN_dB = 5.0  # γ_min in dB (matches simulation SNR_THRESHOLD_dB)
GDTS_GAMMA_MIN = 10**(GDTS_GAMMA_MIN_dB / 10)  # Convert to linear

def calculate_hop_distance(sender_id, sink_id, potential_links, nodes, sinks):
    """
    Calculate the shortest hop distance from sender node to sink using BFS.
    Args:
        sender_id: ID of the sender node
        sink_id: ID of the destination sink
        potential_links: Dict {sender_id: [receiver_ids]}
        nodes: List of node dictionaries
        sinks: List of sink dictionaries
    Returns:
        Hop distance (int) or float('inf') if unreachable
    """
    if sender_id == sink_id:
        return 0

    # BFS to find shortest path
    queue = [(sender_id, 0)]  # (node_id, distance)
    visited = {sender_id}

    while queue:
        current_id, dist = queue.pop(0)

        # Check neighbors
        if current_id in potential_links:
            for neighbor_id in potential_links[current_id]:
                if neighbor_id == sink_id:
                    return dist + 1

                if neighbor_id not in visited:
                    visited.add(neighbor_id)
                    queue.append((neighbor_id, dist + 1))

    return float('inf')  # Unreachable

def gdts_calculate_flow(sender_node, receiver_dev, sink_k_id, power_tx_W, channel_gain_ij,
                       noise_power_W, bandwidth):
    """
    Calculate flow f_{l,k}^t for GDTS using the constraints from Jiang's paper.
    This is similar to calculate_potential_flow but follows GDTS-specific equations.
    """
    # 1. Calculate Link Capacity (Rate) r_l^t
    snr = calculate_snr(power_tx_W, channel_gain_ij, noise_power_W)
    rate_bits = calculate_data_rate_bits_per_slot(snr, bandwidth)

    # 2. Sender Queue Constraint f_lk^t <= q_ik^t
    sender_queue_bits = sender_node['queue_bits'].get(sink_k_id, 0.0)

    # 3. Receiver Buffer Constraint (if receiver is a node, not a sink)
    receiver_buffer_constraint_bits = float('inf')
    if not receiver_dev['is_sink']:
        current_total_queue_j = sum(receiver_dev['queue_bits'].values())
        receiver_buffer_constraint_bits = max(0, QUEUE_MAX_bits - current_total_queue_j)

    # 4. Energy constraints are handled separately in GDTS

    # Determine the actual flow based on constraints
    potential_flow_bits = min(rate_bits, sender_queue_bits, receiver_buffer_constraint_bits)

    return max(0, potential_flow_bits)

def gdts_violation_check(activated_links, new_link, nodes, sinks, potential_links,
                        current_channel_gains, noise_power_W, bandwidth):
    """
    Check if activating new_link would violate any constraints.
    Args:
        activated_links: Set of already activated links (i, j, k)
        new_link: Tuple (i, j, k) representing the new link to check
        nodes, sinks, potential_links, current_channel_gains: Network state
        noise_power_W, bandwidth: Physical parameters
    Returns:
        True if no violations, False if violations detected
    """
    i, j, k = new_link

    # 1. Half-duplex constraint: node cannot send and receive simultaneously
    nodes_busy = set()
    for link in activated_links:
        sender, receiver, _ = link
        nodes_busy.add(sender)
        # Only add receiver to busy set if it's a node (not sink)
        receiver_dev = get_device_by_id(receiver, nodes, sinks)
        if receiver_dev and not receiver_dev['is_sink']:
            nodes_busy.add(receiver)

    # Check if new link violates half-duplex
    if i in nodes_busy:
        return False  # Sender already busy

    receiver_dev = get_device_by_id(j, nodes, sinks)
    if receiver_dev and not receiver_dev['is_sink'] and j in nodes_busy:
        return False  # Receiver already busy

    # 2. Energy constraints
    sender_node = get_device_by_id(i, nodes, sinks)
    if not sender_node:
        return False

    # Check if sender has enough energy for transmission
    channel_gain_ij = current_channel_gains.get((i, j), 0.0)
    if channel_gain_ij <= 0:
        return False

    # Calculate required power for SNR threshold
    required_power = (GDTS_GAMMA_MIN * noise_power_W) / channel_gain_ij
    required_power = max(GDTS_P_MIN_W, min(required_power, GDTS_P_MAX_W))

    if sender_node['battery'] < required_power:
        return False

    # 3. Flow feasibility
    flow = gdts_calculate_flow(sender_node, receiver_dev, k, required_power,
                              channel_gain_ij, noise_power_W, bandwidth)
    if flow <= 1e-9:
        return False

    return True


# --- Main GDTS Scheduling Algorithm ---
def gdts_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                   noise_power_W, bandwidth):
    """
    Performs GDTS (Greedy Data Transmission Scheduling) with multicast energy cooperation.
    Based on Algorithm 1 from Jiang's paper.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        A dictionary containing the decisions for this timeslot:
        {
            'y': {node_id: 0 or 1},              # Energy Tx state (1=Tx, 0=Rx)
            'e_hat': {node_id: power_W},         # Energy Tx power
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): flow_bits}          # Data flow
        }
    """
    # Initialize decisions
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes}
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    # --- Phase 1: Link Activation Decision Phase ---
    # Step 1: Identify candidate links and calculate weights
    candidate_links = []  # List of (link_tuple, weight, flow, power)

    # Pre-calculate hop distances for efficiency
    hop_distances = {}
    for node in nodes:
        node_id = node['id']
        for sink in sinks:
            sink_id = sink['id']
            hop_distances[(node_id, sink_id)] = calculate_hop_distance(
                node_id, sink_id, potential_links, nodes, sinks)

    # For each node i
    for sender_node in nodes:
        i = sender_node['id']

        # Check basic conditions: b_i^t >= p_min && sum(q_ik^t) > 0
        if sender_node['battery'] < GDTS_P_MIN_W:
            continue

        total_queue_bits = sum(sender_node['queue_bits'].values())
        if total_queue_bits <= 1e-9:
            continue

        # For each outgoing link l in O_i
        if i not in potential_links:
            continue

        for j in potential_links[i]:
            receiver_dev = get_device_by_id(j, nodes, sinks)
            if not receiver_dev:
                continue

            # Check channel and power constraints
            channel_gain_ij = current_channel_gains.get((i, j), 0.0)
            if channel_gain_ij <= 0:
                continue

            # Check if min{b_i^t, p_max} >= (σ_l^t * γ_min) / g_l^t
            required_power = (noise_power_W * GDTS_GAMMA_MIN) / channel_gain_ij
            available_power = min(sender_node['battery'], GDTS_P_MAX_W)

            if available_power < required_power:
                continue

            # For each sink k
            for sink in sinks:
                k = sink['id']

                # Calculate flow f_{l,k}^t
                actual_power = max(GDTS_P_MIN_W, min(required_power, GDTS_P_MAX_W))
                flow = gdts_calculate_flow(sender_node, receiver_dev, k, actual_power,
                                         channel_gain_ij, noise_power_W, bandwidth)

                if flow > 1e-9:
                    # Calculate weight = f_{l,k}^t * hop_{i,k}
                    hop_dist = hop_distances.get((i, k), float('inf'))
                    if hop_dist != float('inf'):
                        weight = flow * hop_dist
                        link_tuple = (i, j, k)
                        candidate_links.append((link_tuple, weight, flow, actual_power))

    # Step 2: Sort candidates by weight in descending order
    candidate_links.sort(key=lambda x: x[1], reverse=True)

    # Step 3: Iteratively select links using ViolationCheck
    activated_links = set()

    for link_tuple, weight, flow, power in candidate_links:
        i, j, k = link_tuple

        # Check if activating this link violates constraints
        if gdts_violation_check(activated_links, link_tuple, nodes, sinks,
                               potential_links, current_channel_gains,
                               noise_power_W, bandwidth):

            # Activate the link
            activated_links.add(link_tuple)
            x_decisions[link_tuple] = 1
            p_decisions[(i, j)] = power
            f_decisions[link_tuple] = flow

    # --- Phase 2: Energy Cooperation Action Decision Phase ---
    # For each node i
    for sender_node in nodes:
        i = sender_node['id']

        # Check surplus energy condition: b_i^t >= b_min
        if sender_node['battery'] < GDTS_B_MIN_J:
            continue

        # Check if any neighbor has low energy and pending data
        has_needy_neighbor = False
        if i in potential_links:
            for neighbor_id in potential_links[i]:
                neighbor_dev = get_device_by_id(neighbor_id, nodes, sinks)
                if neighbor_dev and not neighbor_dev['is_sink']:
                    # Check if neighbor has low energy and pending data
                    neighbor_low_energy = neighbor_dev['battery'] < GDTS_B_MIN_J
                    neighbor_has_data = sum(neighbor_dev['queue_bits'].values()) > 1e-9

                    if neighbor_low_energy and neighbor_has_data:
                        has_needy_neighbor = True
                        break

        if has_needy_neighbor:
            # Set energy transmission state
            y_decisions[i] = 1

            # Determine energy transmission power
            sender_has_data = sum(sender_node['queue_bits'].values()) > 1e-9
            if sender_has_data:
                e_hat_decisions[i] = GDTS_E_MIN_W
            else:
                # Random value between e_min and e_max (using random module already imported)
                e_hat_decisions[i] = random.uniform(GDTS_E_MIN_W, GDTS_E_MAX_W)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions


# --- GDTS without Energy Cooperation ---
def gdts_no_ec_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                         noise_power_W, bandwidth):
    """
    Performs GDTS without energy cooperation (GDTS-No).
    Only performs the link activation decision phase.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): flow_bits}          # Data flow
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes}
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    # Only perform Link Activation Decision Phase (same as GDTS main algorithm)
    # Step 1: Identify candidate links and calculate weights
    candidate_links = []

    # Pre-calculate hop distances
    hop_distances = {}
    for node in nodes:
        node_id = node['id']
        for sink in sinks:
            sink_id = sink['id']
            hop_distances[(node_id, sink_id)] = calculate_hop_distance(
                node_id, sink_id, potential_links, nodes, sinks)

    # For each node i
    for sender_node in nodes:
        i = sender_node['id']

        # Check basic conditions
        if sender_node['battery'] < GDTS_P_MIN_W:
            continue

        total_queue_bits = sum(sender_node['queue_bits'].values())
        if total_queue_bits <= 1e-9:
            continue

        # For each outgoing link
        if i not in potential_links:
            continue

        for j in potential_links[i]:
            receiver_dev = get_device_by_id(j, nodes, sinks)
            if not receiver_dev:
                continue

            channel_gain_ij = current_channel_gains.get((i, j), 0.0)
            if channel_gain_ij <= 0:
                continue

            required_power = (noise_power_W * GDTS_GAMMA_MIN) / channel_gain_ij
            available_power = min(sender_node['battery'], GDTS_P_MAX_W)

            if available_power < required_power:
                continue

            # For each sink k
            for sink in sinks:
                k = sink['id']

                actual_power = max(GDTS_P_MIN_W, min(required_power, GDTS_P_MAX_W))
                flow = gdts_calculate_flow(sender_node, receiver_dev, k, actual_power,
                                         channel_gain_ij, noise_power_W, bandwidth)

                if flow > 1e-9:
                    hop_dist = hop_distances.get((i, k), float('inf'))
                    if hop_dist != float('inf'):
                        weight = flow * hop_dist
                        link_tuple = (i, j, k)
                        candidate_links.append((link_tuple, weight, flow, actual_power))

    # Step 2: Sort and select links
    candidate_links.sort(key=lambda x: x[1], reverse=True)
    activated_links = set()

    for link_tuple, weight, flow, power in candidate_links:
        if gdts_violation_check(activated_links, link_tuple, nodes, sinks,
                               potential_links, current_channel_gains,
                               noise_power_W, bandwidth):
            activated_links.add(link_tuple)
            x_decisions[link_tuple] = 1
            p_decisions[(link_tuple[0], link_tuple[1])] = power
            f_decisions[link_tuple] = flow

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions
