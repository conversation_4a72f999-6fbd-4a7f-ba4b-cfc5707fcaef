import json
import matplotlib.pyplot as plt
import os
import pandas as pd # For moving average

# Define the input JSON file and output image file
INPUT_JSON = 'mean_instantaneous_throughput.json'
OUTPUT_DIR = 'fig'
OUTPUT_IMAGE = os.path.join(OUTPUT_DIR, 'fig_convergence_dynamics.png')

# Define the number of time slots to plot for convergence dynamics
PLOT_SLOTS = 100 # Plot up to the 100th time slot
WINDOW_SIZE = 5 # Window size for moving average

# Define line styles and markers for algorithms
# Inspired by the provided example image
LINE_STYLES = {
    "Lyapunov-MEC": {'linestyle': '-', 'marker': 'o', 'markersize': 4},
    "Lyapunov-NoEC": {'linestyle': '--', 'marker': 's', 'markersize': 4},
    "Lyapunov-UEC": {'linestyle': '-.', 'marker': '^', 'markersize': 4},
    "GMW": {'linestyle': ':', 'marker': '.', 'markersize': 5},
    "EAG": {'linestyle': ':', 'marker': 'd', 'markersize': 4},
    "RAND": {'linestyle': ':', 'marker': 'x', 'markersize': 4}
}
DEFAULT_STYLE = {'linestyle': '-', 'marker': '', 'markersize': 3} # Default if algo not in dict

def plot_convergence_dynamics():
    """
    Reads mean instantaneous throughput data from a JSON file, applies a 
    moving average smoothing, and plots the convergence dynamics for 
    various algorithms up to PLOT_SLOTS with distinct line styles and markers. 
    Saves the plot to a file.
    """
    try:
        with open(INPUT_JSON, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: Input file '{INPUT_JSON}' not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{INPUT_JSON}'.")
        return

    plt.figure(figsize=(10, 6))

    algorithms_to_plot = [
        "Lyapunov-MEC",
        "Lyapunov-NoEC",
        "Lyapunov-UEC",
        "GMW",
        "EAG",
        "RAND"
    ]

    corrected_algo_names = {
        "Lyapunov-MECX": "Lyapunov-MEC",
        "Lyapunov-NoECX": "Lyapunov-NoEC"
    }

    for algo_name_in_json in data.keys():
        display_name = corrected_algo_names.get(algo_name_in_json, algo_name_in_json)
        
        if display_name in algorithms_to_plot:
            throughput_series_raw = data[algo_name_in_json]
            slots_to_plot_actual = min(PLOT_SLOTS, len(throughput_series_raw))
            series_to_smooth = pd.Series(throughput_series_raw[:slots_to_plot_actual])
            smoothed_series = series_to_smooth.rolling(window=WINDOW_SIZE, min_periods=1, center=True).mean()
            
            style = LINE_STYLES.get(display_name, DEFAULT_STYLE)
            
            plt.plot(smoothed_series.index, 
                     smoothed_series.values, 
                     label=display_name, 
                     linestyle=style['linestyle'],
                     marker=style['marker'],
                     markersize=style['markersize'])

    plt.xlabel('Time Slot')
    plt.ylabel('Average Instantaneous Throughput (Mbps)')
    plt.title(f'Smoothed Convergence Dynamics (First {PLOT_SLOTS} Time Slots, Window={WINDOW_SIZE})')
    plt.legend(loc='best')
    plt.grid(True)
    plt.tight_layout()

    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    try:
        plt.savefig(OUTPUT_IMAGE)
        print(f"Smoothed convergence dynamics plot with distinct styles saved to '{OUTPUT_IMAGE}'")
    except Exception as e:
        print(f"Error saving plot: {e}")

if __name__ == '__main__':
    plot_convergence_dynamics()
