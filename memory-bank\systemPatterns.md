# 系统模式

*此文件记录了系统架构、关键技术决策、使用的设计模式、组件关系和关键实现路径。*

## 系统架构

*   **网络拓扑:** 静态能量收集无线传感器网络 (EH-WSN)，包含 $N$ 个传感器节点 ($\mathcal{N}$) 和 $K$ 个汇聚节点 ($\mathcal{K}$)。节点部署在规则网格中，汇聚节点位于固定位置（如角落/边缘）。拓扑由图 $\mathcal{G}=(\mathcal{V}, \mathcal{E})$ 表示，其中 $\mathcal{V}=\mathcal{N} \cup \mathcal{K}$，$\mathcal{E}$ 是基于通信范围确定的潜在链路。特别地，每个汇聚节点仅与其最近的传感器节点建立入向链路。假设网络连通。
*   **数据流:** 节点感知数据，生成数据包，通过可能的多跳路由传输到汇聚节点（受限于汇聚节点的特定连接）。
*   **时隙操作:** 系统按时隙运行，每个时隙包含能量协作和数据传输两个阶段的决策。
*   **核心组件:**
    *   **节点:** 具有数据缓冲区（分 $K$ 个队列）、可充电电池、环境能量收集能力、RF 能量收集能力、数据收发能力、能量传输能力。
    *   **链路:** 数据链路和能量传输链路（可能共享物理路径但使用不同频率或机制）。
    *   **控制器 (隐式):** Lyapunov-MEC 算法在逻辑上作为中心化或分布式控制器（论文未明确分布式实现），在每个时隙做出调度决策。

## 关键技术决策

*   **优化框架:** 采用 **Lyapunov 优化** 将 LTA 吞吐量最大化问题（带稳定性约束）转化为逐时隙优化问题。这是解决随机性和 LTA 目标的核心方法。
*   **能量管理:**
    *   引入 **虚拟能量赤字队列** ($B_i(t)$) 来处理 LTA 能量约束。
    *   采用 **多播能量协作 (M-EC)** 机制，允许节点向多个邻居广播能量，以提高能量共享效率。
    *   使用 **非线性 RF 能量收集模型** (式~\eqref{eq_EH1})，更贴近实际硬件特性。
    *   考虑 **环境能量收集** (四状态马尔可夫模型)。
*   **数据通信:**
    *   采用 **半双工** 通信模式。
    *   使用 **Rician 衰落信道模型** 和调整后的 Friis 路径损耗模型。
    *   考虑 **SNR 阈值** ($\gamma_{\min}$) 以保证基本链路质量。
*   **算法设计:**
    *   设计 **在线启发式算法 (Lyapunov-MEC)** 来近似求解逐时隙优化问题，平衡性能与复杂度。其能量协作阶段采用多播机制。
    *   设计 **Lyapunov-UEC 算法** 作为 Lyapunov-MEC 的变种，其能量协作阶段采用单播传输决策，即选择单个发送者-接收者对进行能量传输。
    *   两种算法都将决策过程分解为 **能量协作** 和 **数据传输** 两个阶段。
    *   引入 **RF 能量收集效率因子** ($\eta_{\text{data}}$) 来区分从数据信号和能量信号中收集能量的效率。

## 使用的设计模式

*   **漂移加惩罚 (Drift-Plus-Penalty):** Lyapunov 优化中的标准技术，用于在最小化队列漂移的同时优化 LTA 目标。此模式应用于 MEC 和 UEC。
*   **两阶段决策 (Two-Phase Decision):** Lyapunov-MEC 和 Lyapunov-UEC 算法均将能量协作和数据传输决策分开处理，简化了问题。
*   **贪婪选择 (Greedy Selection):**
    *   在 Lyapunov-MEC 的能量协作阶段，贪婪选择提供最大多播增益的能量发送者。
    *   在 Lyapunov-UEC 的能量协作阶段，迭代贪婪选择提供最大单播增益的（发送者-接收者-功率）组合。
    *   在两种算法的数据传输阶段，均使用贪婪策略（基于净权重）选择数据链路。
*   **队列加权 (Queue Weighting):** 两种算法的目标函数 $W^*(t)$ (式~\eqref{eq_W_star_final}) 均使用数据队列差异 ($q_{i,k}-q_{j,k}$) 和能量赤字队列 ($B_i(t)$) 对数据传输效益和能量成本进行加权。

## 组件关系

*   **队列与决策:** 数据队列 ($q_{i,k}$) 和能量赤字队列 ($B_i$) 的状态直接影响 Lyapunov-MEC 算法的逐时隙决策（通过权重 $W_{l,k}$ 和 $\text{NetWeight}_{l,k}$）。
*   **能量与数据:** 节点的能量状态 ($b_i(t)$) 约束了其数据传输功率 ($p_l^t$)、能量传输功率 ($\hat{e}_i^t$) 和数据接收能力 ($f_{l,k}^t$ 受 $p_{\text{rcv}}$ 限制)。能量协作 ($\hat{e}_i^t, \check{e}_i^t$) 直接影响后续时隙的能量状态和数据传输能力。
*   **信道与速率/功率:** 信道增益 ($g_l^t$) 影响数据传输速率 ($r_l^t$) 和达到目标 SNR 所需的功率 ($p_l^t$)。
*   **算法与模型:** Lyapunov-MEC 算法基于系统模型中定义的约束（能量、功率、半双工、队列容量等）进行决策。

## 关键实现路径

*   **Lyapunov-MEC 算法执行流程 (每个时隙 t):**
    1.  **观察状态:** 获取当前队列状态 $\mathbf{q}(t), \mathbf{B}(t)$，电池电量 $\mathbf{b}(t)$，信道增益 $\mathbf{g}^t$。
    2.  **阶段 1 (能量协作):** (算法~\ref{alg_lyapunov_mec_phase1})
        *   识别潜在能量发送者和接收者（基于能量和赤字阈值）。
        *   迭代计算潜在发送者的 M-EC 增益 ($\Delta W_i^{\text{EC}}$)。
        *   贪婪选择提供最大正增益的发送者，设置 $y_i^t=1$ 和 $\hat{e}_i^t$。
    3.  **阶段 2 (数据传输):** (算法~\ref{alg_lyapunov_mec_phase2}，MEC 和 UEC 此阶段逻辑相同)
        *   计算所有潜在数据链路的基础权重 $W_{l,k}$。
        *   计算满足 SNR 阈值所需的最小功率 $p_{\text{required\_min\_W}}$。
        *   计算候选链路对的净权重 $\text{NetWeight}_{l,k}(p_{\text{required\_min\_W}})$ 和潜在流量 $f_{\text{potential}}$。
        *   按净权重降序排序候选对。
        *   迭代检查候选对，验证半双工和组合能量约束。
        *   若通过且净权重 > 0，则激活链路 ($x_{l^*,k^*}^t=1$)，记录功率 $p_{l^*}^t=p_{\text{required\_min\_W}}$ 和流量 $f_{l^*,k^*}^t$。标记节点数据繁忙。
    4.  **状态更新:** (算法~\ref{alg_lyapunov_mec_phase2} 末尾)
        *   计算实际 RF 收集能量 $\check{e}_i^t$（考虑 $\eta_{\text{data}}$）。
        *   计算总能耗 $\text{Cons}_i^t$。
        *   更新电池电量 $b_i(t+1)$。
        *   更新数据队列 $q_{i,k}(t+1)$。
        *   更新能量赤字队列 $B_i(t+1)$。
*   **Lyapunov-UEC 算法执行流程差异点 (与 MEC 相比):**
    *   **阶段 1 (能量协作):**
        *   UEC 算法迭代地识别能提供最大正 *单播* 增益的（发送者 $i$, 接收者 $j$, 功率 $e_{trial}$）组合。
        *   单播增益 $\Delta W_{i \to j}^{\text{UEC}}(e_{trial})$ 基于单个接收者 $j$ 的预期能量收集和发送者 $i$ 的能量成本计算。
        *   选定的发送者 $i$ 和接收者 $j$ 被标记为当前时隙能量协作繁忙，不能再参与本时隙的其他能量协作配对。
        *   循环持续直到没有更多有益的单播对。
    *   阶段 2 (数据传输) 和状态更新逻辑与 Lyapunov-MEC 相同。
*   **对比算法实现要点 (`comparison_algorithms.py`):**
    *   **GMW:**
        *   计算权重 $W_{lk} = q_{ik} - q_{jk}$。
        *   贪婪选择权重 > 0 且满足约束的链路。
        *   **使用固定最小功率 `P_MIN_W` 进行数据传输。**
        *   不进行能量协作。
    *   **EAG:**
        *   计算权重 $W_{lk} = (q_{ik} - q_{jk}) \times (b_i / b_{max})$。
        *   贪婪选择权重 > 0 且满足约束的链路。
        *   **使用固定最小功率 `P_MIN_W` 进行数据传输。**
        *   不进行能量协作。
    *   **RAND:**
        *   随机选择满足基本约束的可行链路。
        *   **使用固定最小功率 `P_MIN_W` (5 dBm / 3.16 mW) 进行数据传输。**
        *   不进行能量协作。
    *   **共同点:** 对比算法均直接操作数据流 (bits)，不进行能量协作，且使用固定的 `P_MIN_W` 进行数据传输。
