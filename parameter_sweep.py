# parameter_sweep.py

import simulation # Import the main simulation script as a module
import comparison_algorithms
import numpy as np
import matplotlib.pyplot as plt
import time
import copy # To potentially deepcopy node states if needed between runs

# --- Sweep Configuration ---
# Define multiple parameter sweeps
# Temporarily modified to run only BATTERY_MAX_J sweep
SWEEP_CONFIGS = [
    {
        'param_name': 'BATTERY_MAX_J',
        'param_values': [1.0, 2.0, 3.0, 4.0, 5.0, 10.0, 15.0, 20.0, 25.0], # Sweep battery capacity from 1J to 25J
        'xlabel': 'Maximum Battery Capacity (J)',
        'title_suffix': 'vs. Battery Capacity',
        'is_scale_factor': False
    },
    # { # Temporarily commented out V_CONTROL sweep
    #     'param_name': 'V_CONTROL',
    #     'param_values': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    #     'xlabel': 'Lyapunov Control Parameter (V)',
    #     'title_suffix': 'vs. Lyapunov V',
    #     'is_scale_factor': False
    # },
    # { # Temporarily commented out NUM_NODES sweep
    #     'param_name': 'NUM_NODES',
    #     'param_values': [20, 30, 40, 50],
    #     'xlabel': 'Number of Nodes (N)',
    #     'title_suffix': 'vs. Number of Nodes',
    #     'is_scale_factor': False
    # },
]

# Algorithms to compare in the sweep
ALGORITHMS_TO_COMPARE = {
    "Lyapunov-MEC": simulation.lyapunov_mec_scheduling,
    "Lyapunov-NoEC": simulation.lyapunov_noec_scheduling,
    # "Lyapunov-UEC": simulation.lyapunov_uec_scheduling, # Assuming UEC is available in simulation.py
    "GMW": comparison_algorithms.gmw_scheduling,
    "EAG": comparison_algorithms.eag_scheduling,
    "RAND": comparison_algorithms.rand_scheduling,
}

# Number of simulation runs to average for each parameter setting
NUM_SWEEP_RUNS = 30 # Restored to 30 for reliable results

# Store original values of ALL potentially modified parameters before starting any sweeps
original_simulation_parameters = {}
def store_original_parameters():
    """Stores the initial values of parameters that might be changed during sweeps."""
    global original_simulation_parameters
    params_to_store = ['P_MIN_dBm', 'P_MAX_dBm', 'P_MIN_W', 'P_MAX_W',
                       'ARRIVAL_PROB', 'PACKET_SIZE_MEAN_Mbit', 'PACKET_SIZE_MEAN_bits',
                       'ENV_EH_MEAN_W', 'NUM_NODES',
                       'BATTERY_MAX_J', 'V_CONTROL'] # Added BATTERY_MAX_J and V_CONTROL
    for param in params_to_store:
        if hasattr(simulation, param):
            value = getattr(simulation, param)
            # Handle numpy arrays correctly
            original_simulation_parameters[param] = value.copy() if isinstance(value, np.ndarray) else value
    print("Stored original simulation parameters.")

def restore_all_original_parameters():
    """Restores all parameters to their initially stored values."""
    global original_simulation_parameters
    print("Restoring ALL original simulation parameters...")
    for param_name, value in original_simulation_parameters.items():
         # Handle numpy arrays correctly
        current_value = getattr(simulation, param_name)
        if isinstance(value, np.ndarray):
             # Check if shapes match before assigning back
             if current_value.shape == value.shape:
                  setattr(simulation, param_name, value.copy())
             else:
                  print(f"Warning: Shape mismatch for {param_name}. Cannot restore.")
        else:
             setattr(simulation, param_name, value)
    print("Restored original simulation parameters.")


# --- Helper to update parameters and dependencies ---
def update_simulation_parameter(param_name, value):
    """Updates a parameter in the simulation module and its known dependencies."""
    # No need to store original here, done globally at the start
    print(f"  Setting {param_name} = {value}")
    setattr(simulation, param_name, value)

    # Update known dependencies
    if param_name == 'P_MIN_dBm':
        new_p_min_w = 10**(value / 10) / 1000
        print(f"    Updating P_MIN_W = {new_p_min_w:.4e}")
        setattr(simulation, 'P_MIN_W', new_p_min_w)
    elif param_name == 'P_MAX_dBm':
         new_p_max_w = 10**(value / 10) / 1000
         print(f"    Updating P_MAX_W = {new_p_max_w:.4e}")
         setattr(simulation, 'P_MAX_W', new_p_max_w)
    elif param_name == 'ARRIVAL_PROB':
        pass # No direct dependencies listed
    elif param_name == 'PACKET_SIZE_MEAN_Mbit':
         new_mean_bits = value * 1e6
         print(f"    Updating PACKET_SIZE_MEAN_bits = {new_mean_bits:.4e}")
         setattr(simulation, 'PACKET_SIZE_MEAN_bits', new_mean_bits)
    elif param_name == 'NUM_NODES':
         # IMPORTANT: Changing NUM_NODES requires re-initialization logic
         # in run_single_simulation to work correctly. Assuming initialize_network handles it.
         print(f"    NUM_NODES changed. Ensure initialize_network() uses the new value.")
         pass
    elif param_name == 'BATTERY_MAX_J':
         # Update DELTA_I dependency
         new_delta_i = simulation.LTA_ENERGY_THRESHOLD_FACTOR * value
         print(f"    Updating DELTA_I = {new_delta_i:.4e}")
         setattr(simulation, 'DELTA_I', new_delta_i)
         pass
    elif param_name == 'V_CONTROL':
         # No direct dependencies listed in simulation parameters
         pass
    # Add more dependencies here if needed

# --- Main Loop for Multiple Sweeps ---
overall_start_time = time.time()
store_original_parameters() # Store initial state once

all_sweep_results_data = {} # Store results for all sweeps: {sweep_param_name: {algo_name: [...]}}

for config in SWEEP_CONFIGS:
    param_name = config['param_name']
    param_values = config['param_values']
    xlabel = config['xlabel']
    title_suffix = config['title_suffix']
    is_scale_factor = config.get('is_scale_factor', False) # Default to False

    print(f"\n--- Starting Parameter Sweep for: {param_name} ---")
    print(f"Values: {param_values}")
    print(f"Algorithms: {list(ALGORITHMS_TO_COMPARE.keys())}")
    print(f"Runs per setting: {NUM_SWEEP_RUNS}")

    sweep_start_time = time.time()
    # Initialize to store means, standard deviations, and number of valid runs for CI calculation
    current_sweep_results = {name: {'means': [], 'stds': [], 'n_runs': []} for name in ALGORITHMS_TO_COMPARE}

    # Store original EH rates specifically for scaling sweep
    original_env_eh_mean_w_for_scale = None
    if is_scale_factor and param_name == 'ENV_EH_SCALE':
        # Use the globally stored original value as the base for scaling
        original_env_eh_mean_w_for_scale = original_simulation_parameters.get('ENV_EH_MEAN_W', None)
        if original_env_eh_mean_w_for_scale is None:
             raise ValueError("Original ENV_EH_MEAN_W not found in stored parameters.")
        original_env_eh_mean_w_for_scale = original_env_eh_mean_w_for_scale.copy() # Ensure we use a copy

    for value in param_values:
        print(f"\nTesting {param_name} = {value}")

        # --- Special handling for scaling factors ---
        if is_scale_factor and param_name == 'ENV_EH_SCALE':
            if original_env_eh_mean_w_for_scale is None:
                 raise ValueError("Original EH rates not stored for scaling.")
            # Apply scaling to the *original* rates stored at the beginning of this specific sweep
            new_rates = original_env_eh_mean_w_for_scale * value
            print(f"  Setting ENV_EH_MEAN_W = {new_rates}")
            simulation.ENV_EH_MEAN_W = new_rates
        else:
            # Update the primary parameter and its direct dependencies
            update_simulation_parameter(param_name, value)

        # Run simulations for each algorithm at this parameter value
        for algo_name, scheduling_func in ALGORITHMS_TO_COMPARE.items():
            print(f"  Running {algo_name}...")
            run_throughputs = []
            # run_delays = [] # Can collect other metrics too if needed

            for i in range(NUM_SWEEP_RUNS):
                print(f"    Run {i+1}/{NUM_SWEEP_RUNS}...")
                try:
                    # Ensure simulation uses the modified parameters
                    sim_result = simulation.run_single_simulation(scheduling_func)
                    run_throughputs.append(sim_result['final_avg_throughput_mbps'])
                    # run_delays.append(sim_result['final_avg_packet_delay'])
                except Exception as e:
                    print(f"    ERROR during simulation run {i+1} for {algo_name} with {param_name}={value}: {e}")
                    run_throughputs.append(np.nan)
                    # run_delays.append(np.nan)

            # Calculate average throughput, standard deviation, and actual number of runs for this algorithm and parameter value
            avg_throughput = np.nanmean(run_throughputs)
            std_throughput = np.nanstd(run_throughputs)
            actual_n_runs = np.sum(~np.isnan(run_throughputs)) # Count non-NaN runs

            current_sweep_results[algo_name]['means'].append(avg_throughput)
            current_sweep_results[algo_name]['stds'].append(std_throughput)
            current_sweep_results[algo_name]['n_runs'].append(actual_n_runs)
            print(f"    Avg Throughput for {algo_name}: {avg_throughput:.4f} Mbps, Std: {std_throughput:.4f}, N_Runs: {actual_n_runs}")

    # --- Store results for this sweep ---
    all_sweep_results_data[param_name] = {
        'results': current_sweep_results, # This now contains dicts with 'means', 'stds', 'n_runs'
        'param_values': param_values,
        'xlabel': xlabel,
        'title_suffix': title_suffix
    }

    # --- Restore parameters before starting next sweep ---
    # Important to restore ALL original parameters, not just the one swept
    restore_all_original_parameters()
    # Re-store again in case the restoration changed something needed for the next loop
    # store_original_parameters() # Re-storing might not be necessary if restore_all is comprehensive


    sweep_end_time = time.time()
    print(f"--- Sweep for {param_name} Finished. Time: {sweep_end_time - sweep_start_time:.2f}s ---")


# --- Plotting Results for Each Sweep ---
print("\n--- Plotting All Sweep Results ---")

# Define linestyles and markers for different algorithms
# Ensure these lists have enough styles/markers if more algorithms are added
LINESTYLES = ['-', '--', '-.', ':', (0, (3, 1, 1, 1)), (0, (5, 10))]
MARKERS = ['o', 's', '^', 'v', 'd', 'x'] # circle, square, triangle_up, triangle_down, diamond, x
ALGO_NAMES_ORDERED = list(ALGORITHMS_TO_COMPARE.keys()) # Get a fixed order of algorithm names

for param_name, sweep_data in all_sweep_results_data.items():
    print(f"Plotting results for sweep: {param_name}")
    results_dict = sweep_data['results']
    param_values = sweep_data['param_values']
    xlabel = sweep_data['xlabel']
    title_suffix = sweep_data['title_suffix']

    plt.figure(figsize=(10, 6))
    for i, algo_name in enumerate(ALGO_NAMES_ORDERED):
        if algo_name not in results_dict:
            continue
        
        algo_data = results_dict[algo_name]
        means = np.array(algo_data['means'])
        stds = np.array(algo_data['stds'])
        n_runs_array = np.array(algo_data['n_runs'])

        # Filter out potential NaN values if runs failed (based on mean being NaN)
        valid_indices = [j for j, m in enumerate(means) if not np.isnan(m)]
        
        valid_params = np.array([param_values[j] for j in valid_indices])
        valid_means = means[valid_indices]
        valid_stds = stds[valid_indices]
        valid_n_runs = n_runs_array[valid_indices]
        
        linestyle_idx = i % len(LINESTYLES) # Cycle through linestyles
        marker_idx = i % len(MARKERS) # Cycle through markers
        
        if valid_params.size > 0: # Check if there are any valid points to plot
            line, = plt.plot(valid_params, valid_means, marker=MARKERS[marker_idx], linestyle=LINESTYLES[linestyle_idx], label=algo_name)
            
            # Calculate 95% CI
            # se = stds / sqrt(n_runs)
            # For safety against n_runs being zero if all runs for a point failed (though mean would be NaN then)
            se = np.divide(valid_stds, np.sqrt(valid_n_runs), out=np.zeros_like(valid_stds, dtype=float), where=valid_n_runs > 0)
            ci_half_width = 1.96 * se
            
            plt.fill_between(valid_params, valid_means - ci_half_width, valid_means + ci_half_width, color=line.get_color(), alpha=0.2)

    plt.xlabel(xlabel)
    plt.ylabel("Average Throughput (Mbps)")
    plt.title(f"Throughput Comparison {title_suffix}")
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    # Instead of plt.show() for each, maybe save figures?
    # Ensure the 'fig' directory exists or handle potential errors
    try:
        import os
        if not os.path.exists('fig'):
            os.makedirs('fig')
        plot_filename = f"fig/sweep_throughput_vs_{param_name.replace(' ', '_')}.png"
        plt.savefig(plot_filename)
        print(f"Saved plot to {plot_filename}")
    except Exception as e:
        print(f"Error saving plot for {param_name}: {e}")
    plt.close() # Close the figure to avoid displaying multiple windows if run interactively

overall_end_time = time.time()
print(f"\n--- All Parameter Sweeps Finished. Total Time: {overall_end_time - overall_start_time:.2f}s ---")
