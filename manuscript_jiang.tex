\documentclass[lettersize,journal]{IEEEtran}
%\documentclass[journal,11pt,draftclsnofoot,onecolumn]{IEEEtran}
\usepackage{amsmath,amsfonts}
%\usepackage{algorithmic}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{bm}
\usepackage{graphicx}
%\usepackage{algorithm}
%\usepackage{algpseudocode}
\usepackage[colorlinks, linkcolor=blue, citecolor=blue]{hyperref}
\usepackage{amsmath}
\usepackage{epstopdf}
\usepackage{booktabs}  
\usepackage{multirow}   
\usepackage{array}     
\usepackage{csquotes}
\usepackage{color}
\usepackage{amssymb}
\usepackage{enumerate}
\usepackage[numbers,sort&compress]{natbib}

\graphicspath{{fig/}}

%\renewcommand{\algorithmicrequire}{\textbf{Input:}}  
%\renewcommand{\algorithmicensure}{\textbf{Output:}} 
\usepackage[ruled,linesnumbered]{algorithm2e}
\newtheorem{proposition}{Proposition}
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{corollary}{Corollary}
\newtheorem{remark}{Remark}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}
\newenvironment{proof}{{\quad \it Proof:}}{$\hfill\blacksquare$\par}
\hyphenation{}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
		T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\usepackage{balance}
\setlength{\parindent}{1em}


\IEEEpubid{\begin{minipage}{\textwidth}\ \\[30pt] \centering
        Copyright \copyright~2025 IEEE. Personal use of this material is permitted. \\However, permission to use this material for any other purposes must be obtained from the IEEE by sending a <NAME_EMAIL>.
\end{minipage}}

\begin{document}
	\title{Multicast Energy Cooperation Assisted Time-efficient Data Collection Scheduling in WSNs}
	\author{
		Yang Jiang, Zhenguo~Gao*~\IEEEmembership{Senior Member,~IEEE}, Hsiao-Chun Wu*~\IEEEmembership{Fellow,~IEEE}, Yunlong Zhao, Wenxian Jiang~\IEEEmembership{Senior Member,~IEEE}, Amar Kaswan
		
		\IEEEcompsocitemizethanks{
			\IEEEcompsocthanksitem Y.~Jiang, Z.~G.~Gao, and W.~X.~Jiang are with both the Department of Computer Science and Technology in Huaqiao University, and Key Laboratory of Computer Vision and Machine Learning(Huaqiao University), Fujian Province University, Xiamen, FJ, 361021, CHINA. (e-mail: {\tt <EMAIL>}); H.-C.~Wu is with the School of Electrical Engineering and Computer Science, Louisiana State University, Baton Rouge, LA 70803, USA and also with the Innovation Center for AI Applications, Yuan Ze University, Chungli 32003, Taiwan (e-mail: {\tt <EMAIL>}; Y.~L.~Zhao is with the School of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, JS, 211100, CHINA; A.~Kaswan is with the Department of Computer Science and Engineering, Indian Institute of Technology (ISM) Dhanbad, India).
		}
		
		\thanks{This work was jointly supported by Natural Science Foundation of China under Grants 62372190 and 62072236.}
	}
	
	\markboth{IEEE}%
	{Multicast Energy Cooperation Assisted Time-efficient Data Collection Scheduling in Wireless Sensor Networks}
	
	\maketitle
	
	\begin{abstract}
		In Wireless Sensor Networks (WSNs), enabling nodes to harvest energy from the environment and facilitating energy sharing among nodes through Wireless Power Transfer (WPT) technology, known as energy cooperation, can alleviate energy scarcity issues and effectively prolong the lifespan of WSNs. Although previous research has investigated various forms of energy cooperation, recent developments have underscored the potential of Multicast Energy Cooperation (M-EC) in supporting efficient multi-node energy sharing. This approach leverages the broadcast nature of wireless signals, potentially offering greater efficiency compared to traditional point-to-point style Unicast Energy Cooperation (U-EC). In this paper, We focus on the M-EC Assisted Data Collection paradigm for Energy Harvesting-WSNs (EH-WSNs) and investigate the underlying M-EC Assisted Data Collection Scheduling (MECADCS) problem, aiming to minimize the data collection completion time by jointly optimizing the schedule decisions for energy cooperation and data collection. We formulate the MECADCS problem as a Mixed Integer Non-Linear Programming (MINLP) problem and establish its NP-hardness. We also simplified the MECADCS problem into a Mixed Integer Linear Programming (MILP) formulation via piecewise linear approximation, yet solving it using existing mature MILP solvers is still computationally expensive. To promptly return good solutions, we propose an efficient Greedy-based Data Transmission Scheduling Algorithm (GDTS),heuristically determines energy cooperation and data transmission schedules and achieves a computational speedup of $10^4$ times compared to exact solvers. Simulation results demonstrate that GDTS significantly reduces the data collection completion time compared to both algorithms without energy cooperation and those utilizing U-EC.
	\end{abstract}



	\begin{IEEEkeywords}
		Energy harvesting, Data collection, Wireless power transfer, Multicast energy cooperation.
		
	\end{IEEEkeywords}
	
	\section{Introduction}
	\IEEEPARstart{W}{ireless} Sensor Networks (WSNs) play a vital role in enabling a wide range of potential applications, including smart homes~\cite{Shabber2021}, environmental sensing and monitoring~\cite{Sharma2022}, process control~\cite{Koo2023}, disaster alerts~\cite{Prasad2021}, and public safety~\cite{Liu2020}. Their significance continues to grow as they permeate critical domains such as vehicular networks, Industry 4.0 \cite{Qi2018},  and the emerging landscape of 6G networks~\cite{Tomkos2020}. In these applications, WSNs are typically used to perform specific tasks, such as data collection and processing.
	
    The limited energy resources in WSNs constrain the data collection and processing capabilities of nodes (also referred to as sensors) and limit their operational lifespan. To address this challenge, Energy Harvesting WSNs (EH-WSNs) have emerged as an innovative solution~\cite{Ma2020}. EH-WSNs harness environmental resources, such as solar and wind energy, to power nodes, extending their operational lifespan and reducing reliance on traditional energy sources. Wireless Power Transfer (WPT) technology, which enables convenient wireless energy charging of nodes over a distance, has garnered significant attention as an additional means of mitigating energy scarcity in WSNs~\cite{Cannon2009}. Specifically, by allowing nodes in a WSN to wirelessly share energy with other nodes, WPT enhances energy distribution across the network. Although charging other nodes through WPT inevitably incurs additional energy consumption at the source node, this process facilitates a more optimal energy distribution that aligns closely with application requirements, thereby improving network performance and extending its lifetime. This process, in which nodes use WPT technology to charge their neighbors in order to redistribute energy across the network to better align with application requirements, is referred to as energy cooperation \cite{Gurakan2013}.
    
    In the literature, energy cooperation is typically considered in the form of point-to-point Unicast Energy Cooperation (U-EC), where energy is transferred from one node to another. However, the broadcast nature of wireless signals allows a single radio signal to charge multiple nodes simultaneously and enables a single node to harvest energy from multiple concurrent signals \cite{Gao2019}. Assuming that nodes involved in information transmission cannot harvest energy, even when within the radio’s broadcast coverage area, energy harvesting is therefore limited to a subset of nodes within the radio's broadcast range. Based on this characteristic, we define the behavior of wireless energy sharing among nodes—leveraging the broadcast nature of wireless signals—as Multicast Energy Cooperation (M-EC), rather than broadcast energy cooperation.

    Compared to U-EC, M-EC enables multiple nodes to simultaneously harvest energy from a single radio signal, which not only reduces charging time but also significantly enhances energy sharing efficiency~\cite{Liu2019, Ieperen2024}. This is particularly beneficial in multi-node collaborative scenarios, such as environmental monitoring and agricultural IoT applications, where numerous sensors are deployed over extended periods to collect data. 

    Exploiting M-EC in EH-WSNs introduces a new paradigm for M-EC assisted data collection. In this paradigm, multicast energy cooperation and data communication operations must be jointly scheduled to enhance the data collection performance of EH-WSNs, such as minimizing the completion time of data collection tasks. Given its promising potential in promoting data collection performance, M-EC assisted multi-hop data collection in EH-WSNs has emerged as a prominent research topic, attracting considerable attention to further unlock its capabilities.
		
    To the best of our knowledge, no prior work has focused on the paradigm of M-EC-assisted multi-hop data collection in EH-WSNs. This paper aims to address this gap. Specifically, we study time-slotted data collection in EH-WSNs composed of nodes equipped with multicast energy cooperation capabilities. In these networks, we assume that both energy cooperation and data communication operate in half-duplex mode, with nodes having dedicated devices for each function. With the goal of ensuring timely completion of data collection tasks, we explore the problem of optimizing the joint scheduling of energy cooperation and data collection, referred to as the Multicast Energy Cooperation-Assisted Data Collection Scheduling (MECADCS) problem. Since the data collection schedule determines the time schedule for activating links, the terms data collection schedule and link schedule are used interchangeably for convenience throughout the text. This problem is challenging for the following reasons: (1) The strong coupling between data communication and energy cooperation, as energy cooperation acts as a double-edged sword for data communication. While it can be beneficial, excessive or unnecessary energy cooperation inevitably leads to energy loss and may cause data transmission delays for the energy transmitter, particularly during sudden bursts of data collection tasks; (2) The intricate interplay between battery energy levels and data queues of nodes across time slots, stemming from the causal relationships among battery energy, energy usage, and data transmission and reception.
	
	
	In this paper, we formulate the MECADCS problem as a Mixed Integer Nonlinear Programming (MINLP) problem and convert it to a Mixed Integer Linear Programming (MILP) problem via piecewise linear approximation, enabling the use of mature MILP solvers. However, due to the NP-hard nature of the problem, this approach is limited to smaller instances. To deal with larger instances, we propose a heuristic algorithm, the Greedy-based Data Transmission Scheduling Algorithm (GDTS), to efficiently obtain sub-optimal solutions for MECADCS. GDTS iteratively determines proper schedule decisions slot by slot. In each slot, it firstly prioritizes and selects proper links not causing violations for activation, then identifies nodes with surplus energy that can assist energy-deficient neighbors with pending packets for broadcasting energy signal. The amount of energy used for transmission is determined based on certain conditions. We analyze GDTS's properties and evaluate its performance through simulations, which demonstrate the superiority of M-EC over U-EC and No-EC.
	
	Main contributions of this paper are summarized as follows:	
	\begin{itemize}
		\item This study is the first to address the joint scheduling of multicast energy cooperation and data transmission in multi-hop EH-WSNs. We demonstrate that MECADCS is NP-hard and formulate it as a MINLP problem by incorporating the causal relationships among energy storage, utilization, data transmission, reception, and queuing. To make small problem instances solvable with existing MILP solvers, we further transform the MINLP formulation into an MILP problem using piecewise linear approximation.
		\item We propose a heuristic algorithm, GDTS, for the MECADCS problem and analyze its time complexity to establish its feasibility.
		
		\item We conduct extensive simulations to validate the superiority of M-EC over U-EC and No-EC in promoting data collection performance.
	\end{itemize}
	
	We list frequently used symbols in Table~\ref{t1}.	
    \begin{table}[!htbp]
    \caption{Symbols and their meanings}
    \label{t1}
    \centering
    \begin{tabular}{|p{0.05\textwidth}|p{0.38\textwidth}|}
        \hline
        \textbf{Symbol} & \textbf{Meaning} \\
        \hline
        $\mathcal{N}, N$         & $\mathcal{N}{=}\{1,2,\cdots,N\}$ is the set of $N$ nodes \\
        \hline
        $\mathcal{K}, K$         & $\mathcal{K}{=}\{1,2,\cdots,K\}$ is the set of $K$ sinks \\
        \hline
        $a_{i,k}^t$          & Number of data packets generated by node $i$ in time slot $t$ and destined for sink $k$ \\
        \hline
        $p_\text{sense}$           & Energy consumption for sensing a unit of data \\
        \hline
         $p_\text{rcv}$          & Energy consumption for receiving a unit of data \\
        \hline
        $s_{i}^t$          & Node $i$'s energy consumption for sensing in time slot $t$ \\
        \hline
        $p_\text{max}$ $(p_\text{min})$           & Maximum (minimum) signal transmission power of nodes for data transmission\\
        \hline
        $p_{l}^t$           & Transmission power on link $l$ in time slot $t$ \\
        \hline
        $\hat{p}_i^t$ $(\check{p}_i^t)$         & Node $i$'s energy consumption power for transmitting (receiving) data packets in time slot $t$\\
        \hline
        $\mathrm{SNR}_{l}^t$         & Signal-to-noise ratio on link $l$ in time slot $t$\\
        \hline
        $f_{l,k}^t$         & Number of data packets transmitted over link $l$ to sink $k$ \\
        \hline
        $q_{i,k}^t$         & Queue Length of packets at node $i$ destined for sink $k$ \\
        \hline
        $h_i^t$         & Energy harvested by node $i$ from the environment in time slot $t$\\
        \hline
        $\mathcal{N}_i$         & Set of node $i$'s neighbors \\
        \hline
        $e_\text{max}$ $(e_\text{min})$           & Maximum (minimum) signal transmission power of nodes for energy cooperation \\
        \hline
        $e_\text{mp}$           & Upper limit of energy power harvested by a node from RF signals in a time slot, or maximum energy harvesting power from RF signals\\
        \hline
        $\hat e_i^t$         & Energy consumed by node $i$ to transmit energy via WPT in time slot $t$ \\
        \hline
        $Rf_i^t$         & RF signal power received by node $i$  in time slot $t$ \\
        \hline
        $\check e_i^t$         & Energy received by node $i$ via WPT in time slot $t$\\
        \hline
        $b_i^t$         & Battery energy  of node $i$ in time slot $t$\\
        \hline
        $x_{l,k}^t$         & $x_{l,k}^t{=}1$ if link $l$ is activated for routing data destined for sink $k$, otherwise $x_{l,k}^t{=}0$\\
        \hline
        $y_{i}^t$         & $y_{i}^t{=}1$ if node $i$ transmits energy, otherwise $y_{i}^t{=}0$\\
        \hline
        $\mathcal{Q}$         & The set of data packets generated by the data collection task set\\
        \hline
    \end{tabular}
\end{table}

	The remaining sections are organized as follows. Section~\ref{sec_rel_work} reviews related work. In Section~\ref{sec_model}, preliminary models are provided. Section~\ref{sec_problem_def} formulates the MECADCS problem and proves that it is NP-hard. In Section~\ref{sec_heuristic}, we provide a detailed description of GDTS. Section~\ref{sec_sim} presents and analyzes the simulation results. Finally, Section~\ref{sec_conclusion} presents the conclusion. 

    
	\section{Related work}
	\label{sec_rel_work}
	Our research primarily focuses on the joint scheduling of energy cooperation and data transmission (link activation) in EH-WSNs. Given the limited research on the joint optimization of energy cooperation and link scheduling, we will review recent advancements in these two areas separately.


    \begin{table*}[!htbp]
    \caption{Comparison of Related Works}
    \label{t_Comparison}
    \centering
    \renewcommand{\arraystretch}{1.2}
    \begin{tabular}{|c|c|c|c|c|}
        \hline
        \textbf{Prior works} & 
        \textbf{Energy harvesting} & \textbf{Energy cooperation} & \textbf{Multicast energy cooperation} &
         \textbf{Link scheduling} \\ 
        \hline
        \cite{Gurakan2013} \cite{Varan2017} \cite{Yin2020} \cite{Ma2021} \cite{Liang2020}  \cite{Li2020} \cite{Gurakan2016} \cite{Jiao2020}  & $\checkmark$   & $\checkmark$   &    &   \\
        \hline
        \cite{He2018} \cite{Wang2019} \cite{Choi2020} \cite{Yu2020}  \cite{Zhao2023} \cite{Liu2022} \cite{Abbasalizadeh2024}  \cite{Wang2024}&     &    &    &  $\checkmark$ \\
        \hline
          \cite{Liu2019} \cite{Ieperen2024} \cite{Shanin2022}&  $\checkmark$   & $\checkmark$  & $\checkmark$   &   \\
        \hline
        \cite{Wang2021} \cite{Cui2023}   &  $\checkmark$   & $\checkmark$  &    &  $\checkmark$ \\
        \hline
        Our work   &  $\checkmark$   & $\checkmark$  & $\checkmark$   &  $\checkmark$ \\
        \hline
    \end{tabular}
\end{table*}

	\subsection{Energy Cooperation}
	Gurakan \textit{et al.} \cite{Gurakan2013} integrated energy cooperation into a two-hop relay system, focusing on transmission power control to maximize network throughput. Varan \textit{et al.} \cite{Varan2017} explored scenarios where transmitters share energy with receivers for decoding, addressing one-to-one and one-to-many matching problems to optimize the total data rate of all matched pairs of transmitters and receivers.
	
	Yin \textit{et al.} \cite{Yin2020} explored the interactions among caching capabilities, resource allocation, and energy cooperation in caching-capable heterogeneous networks. While they considered energy harvesting from the environment as auxiliary energy supplementation to traditional battery-based energy supply sources, they neglected the energy coupling among nodes across time slots. Ma \textit{et al.} \cite{Ma2021} investigated throughput optimization in industrial WSNs by utilizing energy harvesting from interference RF signals, addressing the trade-off between energy harvesting efficiency and transmission reliability.
	
	Works by Liang \textit{et al.} \cite{Liang2020} and Li \textit{et al.} \cite{Li2020} explored optimal energy cooperation strategies in WSNs to maximize system throughput and long-term average data transmission rates. The authors of \cite{Gurakan2016} delved into the joint optimization of data communication and energy transfer to minimize data transmission latency in the network. Expanding on this research, Jiao \textit{et al.} \cite{Jiao2020} delved deeper into power allocation issues in EH-WSNs within interference channels. 
	
	However, the aforementioned studies are limited to unicast energy cooperation and only consider simple linear RF energy conversion models. In reality, RF energy conversion is nonlinear and a function of the transmission power.
	
	Recent studies have considered multicast energy cooperation.
    Liu \textit{et al.} \cite{Liu2019} proposed a multi-frequency, multi-power WPT system based on a single transmitter that simultaneously powers multiple receivers with different energy requirements . This system enhances WPT efficiency by enabling multicast energy delivery, where each receiver can harvest energy at varying power levels according to its needs. Ieperen \textit{et al.} \cite{Ieperen2024} proposed a capacitive WPT system using frequency bifurcation to achieve coupling-independent energy transfer, supporting stable one-to-many WPT transmission to multiple receivers.

    Energy cooperation has also been further integrated into Simultaneous Wireless Information and Power Transfer (SWIPT) systems, combined with MIMO techniques for multicast transmission.  Shanin et al. \cite{Shanin2022} investigated MIMO WPT systems and proposed an optimal transmission strategy to maximize the average harvested energy at energy-harvesting nodes, using a nonlinear energy harvesting model. Their results indicate that employing multi-antenna transmission strategies significantly improves system performance. Peng et al. \cite{Peng2022} explored an IRS-assisted SWIPT system, aiming to maximize achievable data rates through the joint design of transmission precoding matrices, IRS matrices, and power splitting ratios. 
	
	However, these studies overlooked link scheduling, resulting in solutions that highlight the theoretical potential of energy cooperation, rather than practical applications.

	
	\subsection{Link Scheduling}
	He \textit{et al.} \cite{He2018} tackled the link scheduling problem in single-hop WSNs to minimize data collection latency. Wang \textit{et al.} \cite{Wang2019} extended this research to multi-hop WSNs with multiple data streams. They addressed the latency minimization problem by jointly optimizing data sampling at the source and link scheduling along each data stream path. Choi \textit{et al.} \cite{Choi2020} aimed to control user-requested delays and maintain energy efficiency in cache nodes. They addressed the long-term link scheduling problem using Lyapunov optimization. Work by Yu \textit{et al.} \cite{Yu2020} explored the impact of uncertain fading gain on link scheduling. Zhao \textit{et al.} \cite{Zhao2023} employed convolutional graph neural networks to address link scheduling problems. 
    
    Additionally, several learning-based link scheduling methods have gained significant attention. Liu \textit{et al.} \cite{Liu2022} employed software-defined networking and deep reinforcement learning to tackle hybrid flow scheduling, isolating traffic by partitioning link bandwidth and dynamically allocating resources to optimize scheduling. In dynamic wireless networks, Abbasalizadeh \textit{et al.} \cite{Abbasalizadeh2024} proposed a greedy link scheduling algorithm that combines deep learning and fuzzy logic to enhance link scheduling by predicting link quality probabilities. Wang \textit{et al.} \cite{Wang2024} applied multi-agent deep reinforcement learning to optimize dynamic network scheduling and routing, formulating the problem as a Markov Decision Process and addressing it using deep Q-networks to reduce overhead and improve convergence.
     
    The aforementioned works, including various models summarized in \cite{Gore2011}, focused on traditional link scheduling problems under various scenarios without energy cooperation.
	
    Some recent studies have begun to consider energy cooperation. Wang \textit{et al.} \cite{Wang2021} addressed the link scheduling problem in situations with unicast energy cooperation, considering power allocation but without data collection. Cui \textit{et al.} \cite{Cui2023} also investigated the link scheduling problem under unicast energy cooperation. Their work did not consider node caching capabilities or the nonlinear relationship between data communication rate and signal transmission power.
	
    To date, no existing works have investigated link scheduling in situations with multicast energy cooperation. Table \ref{t_Comparison} summarizes the differences between ours and prior works.



	\section{System Models}
	\label{sec_model}
	\subsection{Network Model}
	We consider an EH-WSN composed of $N$ static nodes in the set $\mathcal{N}{=}\{1,2,\ldots,N\}$ and $K$ sinks in the set $\mathcal{K} {=}\{1,2,\ldots, K\}$. 
    The network topology is represented by a graph $\mathcal{G} {=} (\mathcal{V}, \mathcal{E})$, where $\mathcal{V}{=}\mathcal{N}{\cup}\mathcal{K}$ and $\mathcal{E}{=}\{1,2,\ldots,E\}{\subseteq}\mathcal{V}{\times}\mathcal{V}$ denotes the set of communication links (also called as data links). Fig.~\ref{fig_example_wsn} illustrates an example EH-WSN consists of seven nodes and one sink. 
	
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.44]{jiang1.png}
		\captionsetup{justification=centering}
		\caption{An example EH-WSN.}
		\label{fig_example_wsn}
	\end{figure}
	
	The nodes in the EH-WSN sense the environment and generate data that needs to be routed, potentially through multi-hop relaying, to one of the sinks. We assume the network is connected, ensuring that each node has routes to all sinks. The sinks collect the incoming data, which is subsequently forwarded to remote back-end servers via long-range links for further processing.
    
    In this work, we consider a time-slotted periodic data collection application in EH-WSNs, where each operational cycle is divided into three stages: task assignment, data sensing, and data routing. In the task assignment stage, the system allocates a set of data collection tasks to selected nodes. Each task is represented as a four-tuple $(src,sink,time,size)$, where $src$ specifies the node assigned to the task, $sink$ indicates the destination node for the task's data, $time$ represents the time slot in which the task is assigned, and $size$ denotes the data size to be collected, measured in bits. During the data sensing stage, nodes sense and collect the required amount of data, grouping them into packets for transmission. When the data size is large, multiple packets may be generated. Let $\mathcal{Q}{=}\{1,2,\ldots,Q\}$ denote the set of all data packets generated by the task set. In the subsequent data routing stage, the prepared data packets in $\mathcal{Q}$ are routed to their respective destinations. A task is considered complete only when all data collected in the sensing stage successfully reach their assigned sinks. This paper addresses the problem of minimizing the number of time slots required to complete all tasks by jointly optimizing data transmission and energy cooperation schedules using M-EC. The time slots for the data routing stage are represented as $\mathcal{T}{=}\{1,2,\ldots,T\}$. For simplicity, we assume each time slot has a unit time length, allowing power and energy to be used interchangeably in the subsequent discussions.


	\subsection{Data Communication Model}
	Regarding data communication, we make the following preliminary assumptions: (1) Data communication operates in a half-duplex mode, meaning a node cannot transmit and receive data simultaneously; (2) All data communications are unicast; (3) A node cannot transmit or receive different data over multiple outgoing or incoming links at the same time; (4) Channel interference among data links activated in the same time slot is negligible, achieved through methods such as using orthogonal frequency channels and allowing only far-apart links to share the same channel~\cite{Mamat2023}.  

    Let $a_{i,k}^t$ denote the amount of data generated by node $i$ in time slot $t$, destined for sink $k$. Let $p_{\text{sense}}$ denote the energy consumed by a node to sense one unit of data, and let $s_i^t$ represent the amount of energy consumed by node $i$ for data sensing in time slot $t$. Consequently, the total energy consumed for data sensing is given by
	\begin{equation}
		\label{eq_s}
		\begin{aligned}
			s_i^t = \sum_{k \in \mathcal{K}}p_\text{sense}\cdot a_{i,k}^t,\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	

    Let $\mathbb{S}^t_D$ denote the set of active links in time slot $t$, and let $p_{l}^t$ represent the transmission power allocated to link $l$ in time slot $t$. Let $p_{\text{max}}$ and $p_{\text{min}}$ denote the maximum and minimum transmission powers of a node for data communication, respectively. Then, the transmission power of each active link must satisfy the following constraint.
	\begin{equation}
		\label{eq_p_control}
		\begin{aligned}
			p_\text{min}\leq p_{l}^t\leq p_\text{max}, {\quad}{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	

We assume all data links follow a Rician fading channel~\cite{Gao2015}. For link $l(i,j)$, the probability density function of its channel power gain $g_{i,j}^t$ (or $g_{l}^t$) is given by Eq.~\eqref{eq_g_ij_rician}, where $\overline{g}_{i,j}^t$ represents the expected value of $g_{i,j}^t$, $K_{i,j}$ is the Rician K-factor, defined as the ratio of the power in the dominant line-of-sight component to the total power in the scattered components. The variable $x$ is a random variable drawn from an exponential distribution with a mean of one, and $I_0(\cdot)$ denotes the zero-order modified Bessel function \cite{Gao2015}.
    \begin{equation} 
	\label{eq_g_ij_rician}
 \begin{aligned}
	   f_{g_{i,j}^t}(x) = &\frac{K_{i,j}+1}{\overline{g}_{i,j}^t} e^{-x(K_{i,j}+1)/\overline{g}_{i,j}^t - K_{i,j}}\\
    &{\cdot}I_0 \left( \sqrt{\frac{4K_{i,j}(K_{i,j}+1)x}{\overline{g}_{i,j}^t}} \right), \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{aligned}
    \end{equation} 


We assume that, for link $l(i,j)$, the expected channel power gain $\overline{g}_{i,j}^t$ follows an adjusted Friis free-space equation, as expressed in Eq.~\eqref{eq_g_ij}. Here, $d_{i,j}$ represents the distance between nodes $i$ and $j$, $G_i$ and $G_j$ represent the transmitter and receiver antenna gains, respectively, $L_p$ accounts for polarization losses, $\lambda$ is the signal wavelength, and $\beta$ is an adjustment factor introduced for short-range transmission scenarios \cite{He2013}.
    \begin{equation} 
	\label{eq_g_ij}
	   \overline g_{i,j}^t = \frac{G_i G_j}{L_p} \left( \frac{\lambda}{4 \pi (d_{i,j} + \beta)} \right)^2,\quad \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{equation} 


	Let $\sigma_{l}^t$ denote the corresponding noise power. The Signal-to-Noise Ratio (SNR) of the received signal on link $l$ is then given by Eq.~\eqref{eq_SNR}. To ensure that the received signal on link $l$ can be decoded correctly, the corresponding SNR must not fall below a specified threshold $\gamma_{\text{min}}$, as in Eq.~\eqref{eq_SNR_control}~\cite{Patrik2004}.
    \begin{align}		
		\mathrm{SNR}_{l}^t=&\frac{p_{l}^tg_{l}^t}{\sigma_{l}^t},&{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR}\\
        \mathrm{SNR}_{l}^t \geq& \gamma_{\min}, &{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR_control} 
	\end{align}	


	Let $r_{l}^t$ represent the data transmission rate on link $l$ during time slot 
$t$, which can be expressed as Eq.~\eqref{eq_r}, where $W$ represents the frequency channel bandwidth of the link. We assume all links have the same bandwidth $W$. Since each time slot lasts for one unit time length, $r_l^t$ effectively represents the capacity of link $l$ in time slot $t$.
	\begin{equation}
		\label{eq_r}	
		\begin{aligned}
			r_{l}^t=W\log_2\left(1+	\mathrm{SNR}_{l}^t\right),{\quad}{\forall}l{\in}\mathbb{S}^t_D,t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	 
	
To facilitate data routing, we assume that each node is equipped with a data buffer of capacity  $q_{\text{max}}$, where it maintains $K$ separate data queues, one for each sink. Let $q_{i,k}^t$ denote the queue length of data destined for sink $k$ at node $i$. Similarly, let $f_{l,k}^t$ denote the amount of data transmitted over link $l$ for sink $k$ in time slot $t$. Naturally, data transmission is constrained by both the link capacity and the buffer sizes at the corresponding nodes, so we have constraints as the following equations from Eq.~\eqref{eq_f_r} to Eq.~\eqref{eq_f_q_j}, where $b_j^t$ denotes the amount of energy in battery of node $j$ at time slot $t$, and $p_{\text{rcv}}$ denotes the energy consumed by a node to receive a unit of data.
    \begin{align}
f_{l,k}^t{\leq}&r_{l}^t,{\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_r}\\
f_{l,k}^t{\leq}&q_{i,k}^t, {\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_q_i}\\
f_{l,k}^t{\leq}&\frac{b_{j}^t}{p_\text{rev}},& \forall  l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_b}\\
f_{l,k}^t{\leq}&q_\text{max}{-}\sum_{k{\in}\mathcal{K}}q_{j,k}^t & \forall l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.\label{eq_f_q_j}
    \end{align}
    
	
    Under the above constraints, the parameter $\gamma_{\text{min}}$ in constraint Eq.~\eqref{eq_SNR_control} under limits the the SNR of the link, ensuring efficient link capacity, as indicated in Eq.~\eqref{eq_r}. This, in turn, enables higher data flow rates, as described in Eq.~\eqref{eq_f_r}.
	
    Combining Eq.~\eqref{eq_p_control}, Eq.~\eqref{eq_SNR_control}, and Eq.~\eqref{eq_f_r}, the power $p_l^t$ allowed for data transmission on link $l$ can be expressed as
	\begin{equation}
		\label{eq_p_f}
			p_l^t{=}{\max}\left\{p_{\min},\frac{\sigma_{l}{\cdot}\gamma_{\min}}{g_l^t}, \frac{(2^{\frac{\sum_{k{\in}\mathcal{K}}f_{l,k}^t}{W}}{-}1)\sigma_l^t}{g_l^t}\right\}, {\forall}l{\in}\mathbb{S}^t_D,t{\in}\mathcal{T}.
	\end{equation}

    Then, the energy amounts consumed by node $i$ in time slot $t$ for data transmission and data reception, denoted respectively as $\hat{p}_i^t$ and $\check{p}_i^t$, are given by Eq.~\eqref{eq_p_out} and Eq.~\eqref{eq_p_in}, respectively.

	\begin{align}
	\hat p_i^t =&\sum_{l\in\mathcal{O}_i}p_l^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_p_out}\\
    \check p_i^t = &\sum_{l\in\mathcal{I}_i}\sum_{k{\in}\mathcal{K}}p_\text{rcv}{\cdot}f_{l,k}^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_p_in}
	\end{align}

	The queue length update formula for node $i$, corresponding to data destined for sink $k$ in time slot $t$, is given by
	\begin{equation}
		\label{eq_q}
		\begin{aligned}
			q_{i,k}^{t{+}1}=q_{i,k}^{t}+a_{i,k}^{t}+\sum_{k\in \mathcal{K}}\sum_{l\in \mathcal{I}_i}f_{l,k}^t-\sum_{k\in \mathcal{K}}\sum_{l\in \mathcal{O}_i}f_{l,k}^t,\\{\forall}i{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}

    
	\subsection{Energy Harvesting Model}
   Each node is equipped with a rechargeable battery with a capacity of $b_{\max}$ for energy storage. We assume that the nodes are powered by energy harvested using specialized devices from the environmental sources, such as solar energy. In this work, the energy harvesting process of a node from the environment is modeled using the hidden Markov chain framework described in~\cite{Ku2015}. In this model, the energy harvesting conditions at a node are classified into four distinct states: \textit{Excellent}, \textit{Good}, \textit{Fair}, and \textit{Poor}, indexed as 1 through 4, respectively. For each state $c{\in}\{1,2,3,4\}$, the amount of energy harvested in a time slot $t$ by a node $i$, denoted as $h_i^t$, is modeled as a Gaussian distribution with mean $\mu_c$ and variance $\rho_c$. Additionally, the probability of transitioning from state $c$ to state $c'$ is denoted by $P_{cc'}$. These parameters are derived from actual solar irradiance measurements~\cite{Ku2015}.

     %%The energy cooperation method in this paper differs from SWIPT methods, which use time or power splitting for simultaneous data and energy transfer on the same channel. Instead, our approach employs a dedicated half-duplex antenna at each node for energy transmission/reception, managed by specialized radio equipment. Here, energy cooperation specifically refers to the transfer of surplus energy via this dedicated antenna.

    In addition to harvesting energy from environmental sources, nodes can also harvest RF energy from the radio signals of neighboring nodes to supplement their energy reserves. These radio signals include both intentional energy transmission signals for energy cooperation via WPT and unicast data transmission signals intended for specific destinations, which are unintentionally received by the current node due to the broadcast nature of radio signals. We assume that a node's energy cooperation neighbors are also its communication neighbors. To prevent interference from energy signal to data signal, we assume that energy cooperation are conducted on different frequency channels from data collection, whereas the energy antenna can harvest energy from both energy transmission and data transmission signals. 
    	Let $\mathbb{S}_E^t$ denote the set of nodes engaged in energy transmission in time slot $t$, and let $e_{\max}$ and $e_{\min}$ respectively denote the maximum and minimum energy transmission powers of nodes. Let $\hat{e}_i^t$ denote the energy amount utilized for energy transmission by node $i$ in time slot $t$, then we have Eq.~\eqref{eq_epower_bounds_first}.
	\begin{equation}
		\label{eq_epower_bounds_first}
		e_\text{min}\leq \hat{e}_i^t\leq e_\text{max}, \quad {\forall} i{\in} \mathbb{S}_E^t, t{\in}\mathcal{T}.
	\end{equation}
	
	Let $\hat{p}_j^t$ and $\hat{e}_j^t$ respectively denote the energy transmit power and data transmission of node $i$ in time slot $t$, then the accumulated RF signal power at node $i$ in time slot $t$, denoted as $Rf_i^t$, follows Eq.~\eqref{eq_Rf}.
	\begin{equation}
		\label{eq_Rf}
		Rf_i^t = 
		\left\{
		\begin{aligned}
			&\sum_{j{\in}\mathcal{N}_i} \hat{p}_j^t g_{j,i}^t + \sum_{j{\in}\mathcal{N}_i} \hat{e}_j^t g_{j,i}^t, && \text{if } i{\notin}\mathbb{S}^t_E, \\
			&0, && \text{if } i{\in}\mathbb{S}^t_E.
		\end{aligned}
		\right.
	\end{equation}

        Because of the energy loss incured in energy harvesting process, node $i$ can only harvest a small part of the accumulated RF signal power $Rf_i^t$. Let $\check{e}_i^t$ denote the energy amount harvested by node $i$ from $Rf_i^t$, then $\check{e}_i^t$ can be expressed as Eq.~\eqref{eq_EH}, where $\Omega_{i}$ and $\Psi_{i}^{t}$ are provided in Eq.~\eqref{eq_omega} and Eq.~\eqref{eq_psi}, $\exp(\cdot)$ denotes the exponentiation with the base of the natural logarithm $e$, constant $e_\text{mp}$ represents the upper limit of energy power harvested by a node from RF signals in a time slot (referred as maximum energy harvesting power from RF signals for short), and $\mu_{i}$ and $\nu_{i}$ are fixed parameters determined by the involved hardware components~\cite{Boshkovska2015}.
\begin{subequations}
\label{eq_EH1}
\begin{align}
        \check{e}_{i}^{t}=&\frac{\left[\Psi_{i}^{t}-e_\text{mp}\Omega_{i}\right]}{1-\Omega_{i}},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_EH}\\
	\Omega_{i}=& \frac{1}{1+\exp(\mu_{i}\nu_{i})},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_omega}\\
	\Psi_{i}^{t}=&\frac{e_\text{mp}}{1+\exp\left(-\mu_{i}(Rf_{i}^t-\nu_{i})\right)},& \forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_psi}
\end{align}
\end{subequations}

	
	
	Considering the battery capacity $b_\text{max}$ of a node, the actual energy obtained by node $i$ in time slot $t$, denoted as $\bar{e}_i^t$, should satisfies the constraints as follows. 
	\begin{align}
		\bar{e}_i^t{\leq}&\check{e}_i^t{+}h_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T},\label{eq_e_eh}\\
		\bar{e}_i^t{\leq}&b_{\text{max}}{-}b_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.\label{eq_e_b}
	\end{align}  
	
    Considering the causality of energy usage, we assume that the energy harvested in a time slot can only be used in subsequent time slots. Therefore, the energy consumption behavior of a node during a time slot must satisfy Eq.~\eqref{eq_energy_control}.
	\begin{equation}
		\label{eq_energy_control}
		s_{i}^t+\hat{p}_i^t+\check{p}_i^t+\hat{e}_{i}^t\leq b_i^t, \quad {\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
    In summary, the evolution formula for the battery energy of node $i$ is given by
	\begin{equation}
		\label{eq_b}
		\begin{aligned}
			b_i^{t+1}=b_i^t+\bar e_i^t-s_i^t-\hat p_i^t-\check p_i^t-\hat{e}_{i}^t\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	
	
\subsection{Schedule Model for Data Communication and Energy Cooperation}
    In the energy cooperation-assisted data collection paradigm, actions for energy cooperation and data transmission must be carefully scheduled across time slots. For each time slot, the energy cooperation schedule determines which nodes transmit or receive energy and how much power is used, while the data communication schedule determines which links to activate, which data to transmit, and how much power to use. The activation of a link implicitly determines the corresponding sender and receiver. In brief, the energy cooperation schedule assigns nodes to time slots for energy transmission/reception, while the data communication schedule assigns links to time slots for data transmission. These are referred to as node-based and link-based allocations, respectively, in \cite{Patrik2004}. Since data collection is essentially achieved through data transmissions over links directed towards sinks according to the data communication schedule, the terms \textit{data collection schedule} and \textit{data communication schedule} are used interchangeably. The combination of an energy cooperation schedule and a data communication schedule is called an Energy Cooperation-assisted Data Collection (ECaDC) joint schedule.

   Now, we define some decision variables to describe an ECaDC schedule. Let $x_{l,k}^t{\in}\{0,1\}$ denote whether link $l$ is activated in time slot $t$ for transmitting data packets targeted for sink $k$, where 1 indicates that the link is activated and 0 indicates that it is not. Let $y_i^t{\in}\{0,1\}$ represent the energy reception/transmission state of node $i$ in time slot $t$, where $y_i^t{=}1$ means that node $i$ should transmit energy, and $y_i^t{=}0$ means that it should be in the energy reception state. Let $\textbf{x}{:=}[x_{l,k}^t| l{\in} \mathcal{E}, k {\in}\mathcal{K}, t {\in}\mathcal{T}]$, $\textbf{y}{:=}[y_i^t| i {\in} \mathcal{N}, t{\in}\mathcal{T}]$, $\textbf{p} {:=} [p_l^t | l {\in} \mathcal{E}, t {\in} \mathcal{T}]$, and $\textbf{e}{:=} [\hat{e}_i^t|i {\in} \mathcal{N}, t {\in} \mathcal{T}]$. Then $\textbf{x}$, $\textbf{y}$, $\textbf{p}$, $\textbf{e}$  together form an ECaDC schedule, which is expressed as ECaDC($\textbf{x,y,p,e}$) to emphasize its components $\textbf{x,y,p,e}$.

    We assume that a node can only transmit one type of data packet per time slot. Considering the half-duplex mode of data communication, the activation status of the links must satisfy the following constraint.
	
	\begin{equation}
		\label{eq_x}
		\sum_{k\in \mathcal{K}}\sum_{l\in \mathcal{I}_i}x_{l,k}^t+\sum_{k\in \mathcal{K}}\sum_{l\in\mathcal{O}_i}x_{l,k}^t\leq1,  \quad{\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
	Using the decision variables in $\textbf{x}$, the constraint on $p_l^t$ in Eq.~\eqref{eq_p_control} can be re-expressed as Eq.~\eqref{eq_p_x}. 
	\begin{equation}
		\label{eq_p_x}
		\begin{aligned}
			\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{min}\leq p_{l}^t\leq\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{max},\quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	The SNR constraints in Eq.~\eqref{eq_SNR_control} and Eq.~\eqref{eq_SNR} can be combined as Eq.~\eqref{eq_SNR_x_control}, where a large enough constant $\phi_1 {\gg} \gamma_\text{min}$ is required to deactivate the constraint when the link is not active. 
	\begin{equation} 
		\label{eq_SNR_x_control}
		p_{l}^tg_{l}^t+\phi_1(1-\sum_{k\in \mathcal{K}}x_{lk}^t)\geq\gamma_\text{min}\sigma_{l}^t, \quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
	\end{equation}	
	 
	The constraint in Eq.~\eqref{eq_epower_bounds_first} for controlling \(\hat{e}_i^t\) can be re-expressed as
	\begin{equation}
		\label{eq_y}
		y_{i}^t{\cdot}e_\text{min}\leq \hat{e}_i^t\leq y_{i}^t{\cdot}e_\text{max}, \quad \forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.
	\end{equation}
	
	With the assumption that a node can receive energy signals only when it is in the energy reception state, Eq.~\eqref{eq_Rf} about \(Rf_i^t\) can be re-expressed as Eq.~\eqref{eq_Rf_in_y}, where  $\phi_2$ is a large positive number.
	\begin{equation}
		\label{eq_Rf_in_y}
		Rf_i^t\leq(1{-}y_i^t)\phi_2,  \quad \forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.
	\end{equation}

	
	\section{The MECADCS Problem}
	\label{sec_problem_def}
	
	In this paper, we focus on energy cooperation and data collection joint schedules to efficient and timely performing of the data collection tasks in a working cycle. The optimization objective is to minimize the number of time slots required to complete the data collection tasks. We refer to the underlying optimization problem as the MEC Assisted Data Collection Scheduling (MECADCS) problem.
	
	The MECADCS problem can be more formally stated as follows. Given an EH-WSN with node set $\mathcal{N}$, sink set $\mathcal{K}$, topology graph $\mathcal{G}(\mathcal{V},\mathcal{E})$, data collection tasks $\mathcal{Q}$, list of nodes' battery energy amounts $\mathcal{B}{:=}[b_i^0|i{\in}\mathcal{N}]$, and other related parameters, the goal is to determine an ECaDC schedule ECaDC(\textbf{x,y,p,e}) that uses the minimum number of time slots to complete all data collection tasks.
    
	Let $z^t{\in}\{0,1\}$ be a binary auxiliary variable representing the completion status of the current data collection task, where $z^t{=}1$ indicates that some data packets have not yet reached their corresponding sinks by time slot $t$, and $z^t{=}0$ otherwise. Let \(\textbf{z} := [z^t | t \in \mathcal{T}]\) and let \(Q := |\mathcal{Q}|\) denote the total number of data packets in \(\mathcal{Q}\). The constraints on \(z^t\) are presented in Eq.~\eqref{eq_Z}. 
	\begin{equation}
		\label{eq_Z}
		\begin{aligned}
			&Q = \sum_{t=1}^{T_1} \sum_{i\in \mathcal{N}} \sum_{k\in \mathcal{K}} a_{i,k}^t,\\
			&z^t \geq \frac{\sum_{i\in \mathcal{N}}\sum_{k\in \mathcal{K}} q_{i,k}^t}{Q},\quad \forall t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	
	Then, the MECADCS problem can be mathematically formulated as a Mixed-Integer Nonlinear Programming (MINLP) problem in Eq.~\eqref{eq_P_1}.
	
	\begin{equation}
		\label{eq_P_1}
		\begin{array}{rrl}
			\textbf{(\textbf{P1})} &\min\limits_{\textbf{x,y,p,e,z}} &\sum_{t{=}1}^{T}z^t\\
			& \text{s.t.}&\text{Eqs}.~\eqref{eq_s},\eqref{eq_g_ij_rician},\eqref{eq_g_ij},\eqref{eq_SNR},\eqref{eq_r},\eqref{eq_f_r},
			\eqref{eq_f_q_i},\\
			&&\eqref{eq_f_b},\eqref{eq_f_q_j},\eqref{eq_p_out},\eqref{eq_p_in},\eqref{eq_q},\eqref{eq_Rf},\eqref{eq_EH},\\
			&&\eqref{eq_e_eh},\eqref{eq_e_b},\eqref{eq_energy_control},\eqref{eq_b},\eqref{eq_x},\eqref{eq_p_x},\eqref{eq_SNR_x_control},\\
   &&\eqref{eq_y},\eqref{eq_Rf_in_y},\eqref{eq_Z},
		\end{array}
	\end{equation}
     where the objective function $\sum_{t=1}^{T}z^t$ represents the total number of time slots required to complete all data collection tasks. The constraints determine the relationships between the system variables to ensure that the schedule respects the operational restrictions, such that only valid solution options are allowed.
     
     Constraints Eqs.~\eqref{eq_x}-\eqref{eq_Rf_in_y} enforce the half-duplex communication mode by linking the binary variables \textbf{x} and \textbf{y} with the data transmission power \textbf{p} and energy transmission power \textbf{e}, respectively. The data transmission power \textbf{p} is constrained by Eqs.~\eqref{eq_energy_control} and \eqref{eq_b}, which relate it to the battery energy $b_i^t$. In turn, the battery energy $b_i^t$ is linked to the energy transfer variable \textbf{e} through Eqs.~ \eqref{eq_e_eh}-\eqref{eq_b}.

    Eqs.~\eqref{eq_SNR}, \eqref{eq_r}-\eqref{eq_f_q_j} impose constraints on the intermediate data flow $f_{l,k}^t$, which is linked to \textbf{p} through Eqs.~\eqref{eq_SNR}, \eqref{eq_r}, and \eqref{eq_f_q_j}. The $f_{l,k}^t$ is further connected to the objective function through Eqs.~\eqref{eq_f_q_i}, \eqref{eq_q}, and \eqref{eq_Z}. Specifically, Eq.~\eqref{eq_Z} ensures that when data is present in a node's buffer but not yet routed to the sink, the variable $z^t$ remains equal to 1, thereby contributing to the total number of time slots in the objective function.
   
   In summary, these constraints define the feasible solution space for the optimization problem. The interdependencies between the variables directly affect the total number of time slots required, steering the system toward an optimal scheduling solution with the minimum duration.
    	
   The inclusion of the logarithmic function in the Shannon formula Eqs.~\eqref{eq_r} and \eqref{eq_f_r}, and the logistic function in Eqs.~\eqref{eq_EH} and \eqref{eq_e_eh}, renders P1 an MINLP problem. As shown in the Lemma~\ref{proposition_1}, the MECADCS problem is NP-Hard. While existing solvers, such as Baron, Bonmin, and Mosek, can solve small instances of such problems, the NP-Hard nature significantly hampers computational efficiency. Consequently, we are driven to explore linearization techniques or efficient heuristic algorithms to derive quasi-optimal solutions.
	
	\begin{lemma}
		\label{proposition_1}
		The MECADCS problem is NP-Hard.
	\end{lemma}
	
	\begin{proof}
		We prove the NP-hardness of MECADCS by providing a reduction from the Flexible Job Shop Scheduling (FJSP) problem~\cite{SDPJDLSKT2024}, which is NPC, to a sub-problem of MECADCS, where slot time is small sufficient, all nodes initially have sufficient energy that makes M-EC unnecessary, have no data buffer, and they always use a fixed transmission power. The sub-problem is called the Data Collection Scheduling (DCS) problem. 
		
		\textbf{The DCS problem:} This problem can be stated as follows: Given a network with graph $G(V,E)$, a set of data packets $P$ where $i{\in}P$ is generated at $s_{i}$ and should be transmitted to its destination $d_{i}$ following a set of predefined routes $R_{i}$ with transmission time on node $k$ as $t_i^k$, the objective is to find a link scheduling solution with the shortest time required to route all the packets to their destinations.  
		
		\textbf{The FJSP problem:} According to~\cite{SDPJDLSKT2024}, given a set of jobs $\mathcal{J}$, a set of machines $\mathcal{M}$, and a set of operations $\mathcal{O}$. Each job $j{\in}\mathcal{J}$ consists of a series of $n_j$ consecutive operations in set $\mathcal{O}$, where each operation $i{\in}\mathcal{O}$ can be performed on any machine from a machine set $\mathcal{M}_i{\subseteq}\mathcal{M}$.  
		
		Let $\tau_i^k$ be the processing time of operation $i$ on machine $k{\in}\mathcal{M}_i$. Each operation $i$ has a direct predecessor $pr(i)$ and a direct successor $fr(i)$ according to the technological processing order of the job (referred to as routing or path). The sets $\mathcal{P}\mathcal{R}(i)$ and $\mathcal{F}\mathcal{R}(i)$ represent all predecessor and successor operations of operation $i$ in all its routes, respectively.
		
        It also assumes that: (1) Each machine processes only one job at a time; (2) Each job runs uninterrupted on a single machine; (3) All machines and jobs are available at time 0.
		
		Let $\mathcal{S}$ be the set of all valid solutions, and let $s{\in}\mathcal{S}$ be a solution (which consists of a sequence of operations and a set of operation-machine assignments), let $C_j(s)$ denote job $j$'s completion time in the solution $s$, let $C_{\max}(s){=}\max\{C_j(s)|j{\in}\mathcal{J}\}$, then the objective of FJSP is to find an optimal solution $s^*{=}\arg\min_{s{\in}\mathcal{S}}(C_{\text{max}}(s))$.
		
		\textbf{Reduction from an FJSP instance to a DCS Instance:}
		
		\begin{itemize}
			\item \textbf{$\mathcal{M}{\cup}\mathcal{J}{\rightarrow}V$:} 
			
			For each machine $k{\in}\mathcal{M}$ in FJSP, create a node $v_{Mk}$. For each job $j{\in}\mathcal{J}$, create two nodes $v^s_{Jj}$, $v^d_{Jj}$, which will be assigned as the source and destination of a packet corresponding to task $j$. All such nodes make up the node set $V{=}\{v_{Mk}|k{\in}\mathcal{M}\}{\cup}\{v^s_{Jj},v^d_{Jj}|j{\in}\mathcal{J}\}$ in the DCS problem instance.
			
			\item \textbf{$\mathcal{J}{\rightarrow}P$:} 
			
			
			For each job $j{\in}\mathcal{J}$, create a packet $p_{j}$ source at node $v_{Jj}$. All such packets make up the packet set $P{=}\{p_{j}|j{\in}\mathcal{J}\}$ in the DCS problem instance.
			
			\item \textbf{$\mathcal{O}$ and $\{\mathcal{M}_i|i{\in}\mathcal{J}\} {\rightarrow}\{R_i|i{\in}\mathcal{J}\}$:} 
			
			For each job $i$ in the FJSP instance, assume job $i$ has technological processing sequence $[O_1,O_2,\ldots,O_{n_i}]$, we create a set of routes $R_{pi}$ for routing packet $i$ as follows. Using $v^s_{Ji}$ and $v^d_{Ji}$ respectively as the only source and the destination of packet $p_i{\in}P$, using the nodes in set $\{v_{Mj}|j{\in}\mathcal{M}_k\}$ as the $k$-th hop nodes, $k{\in}\{1,2,\ldots,n_i\}$. All such routes make up the route set $R_i$ for packet $p_i{\in}P$. 
			\item \textbf{$\tau_i^k{\rightarrow}t_i^k$:} 
			
			The processing time $\tau_i^k$ of operation $i$ on machine $k{\in}\mathcal{M}_i$ is mapped to the packet transmission time for node $v_{Mk}$ forwarding the packet $p_i{\in}P$ along the routes.
		\end{itemize}
		
		Through above steps, we convert an FJSP instance into a DCS instance in polynomial time. Furthermore, it is easy to notice the fact that there is a one-to-one map between the solutions of the FJSP instance and those of the DCS instance. Since FJSP is NPC, our DCS problem is NP-hard. The NP-hardness of MECADCS is established.
	\end{proof}
	
	
	\subsection{Transforming MINLP into MILP through Piecewise Linear Approximation}
  In this section, we apply the piecewise linear approximation method proposed in \cite{Jiang2013}, to transform nonlinear constraints into linear ones, thereby converting the original MINLP problem P1 into an MILP problem. The core concept of piecewise linear approximation for a single-variable function $f(x)$ involves dividing the domain of $x$ into small segments and approximating the original nonlinear function with a series of linear function segments. This approach ensures that the approximation errors remain within a predefined threshold \cite{Jiang2013}, maintaining a balance between accuracy and computational efficiency. Here to address P1, we apply the piecewise linear approximation method to the logarithmic function $\ln(1{+}S)$ in Eq.~\eqref{eq_f_r} and the sigmoid function $\frac{1}{1{+} \exp(-w)}$ in Eq.~\eqref{eq_EH}. Here, for expression clarity, we use $S$ to represent $\mathrm{SNR}_l^t$ and use $w$ to represent $\mu_i(Rf_i^t{-}\nu_i)$.



  For function $\ln(1{+}S)$, let assume $[S_{\min}, S_{\max}]$ is the value range of $S$ and is required to split into $M$ segments respect to a given threshold, then, as illustrated in Fig.~\ref{fig:log_piecewise}, the piecewise linear approximations of $\ln(1{+}S)$ can be expressed as Eq.~\eqref{eq_f_r_2}, where $k_{S}^{(m)}$ denotes the slope of the $m$-th segment, and $S^{(m)}$ and $F_S^{(m)}$ are the x- and y- coordinates of the $m$-th segment right endpoint. 
	\begin{equation}
		\label{eq_f_r_2}
		\begin{aligned}
			f_{l,k}^{t}\leq \frac{W}{\ln 2}\left[k_{S}^{(m)}\left(\frac{p_l^tg_l^t}{\sigma_l^t}-S^{(m-1)}\right)+F_S^{(m-1)}\right],\\
			\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},m{=}1,\dots,M,	
		\end{aligned}	
	\end{equation}


\begin{figure}[h]
    \centering
    \subfloat[]{
        \includegraphics[width=0.47\columnwidth]{jiang2a.png}
        \label{fig:log_piecewise}
    }
    \hfill
    \subfloat[]{
        \includegraphics[width=0.47\columnwidth]{jiang2b.png}
        \label{fig:logistic_piecewise}
    }
    \caption{Illustration of piecewise linear approximation method}
    \label{fig_piecewise}
\end{figure}

For the sigmoid function $\frac{1}{1{+}\exp(-w)}$ in the expression of $\bar{e}_{i}^{t}$, when $w{\geq}0$, the function is concave, so the piecewise linear approximation method are applied similarly. However, when $w{<}0$, the function is convex. To safely approximate it, we use the line tangent with the original curve at the midpoint of the segment for approximation (see Fig.~\ref{fig:logistic_piecewise}), which ensures that the linearized function remains below the original function. In this way, $\bar{e}_{i}^{t}$, which are involved in Eqs.~\eqref{eq_EH} and \eqref{eq_e_eh}, with certain new symbols defined correspondingly, is approximated by Eq.~\eqref{eq_e_eh_2}.

\begin{equation}
\label{eq_e_eh_2}
\begin{aligned}
    \bar{e}_{i}^{t}&{\leq}\frac{e_\text{mp}\left\{k_{w}^{(m)}\left[\mu_{i}(Rf_{i}^t-\nu_{i})-w^{(m-1)}\right]+F_w^{(m-1)}\right\}}{1-\Omega_{i}}\\
    &-\frac{e_{mp}\Omega_{i}}{1-\Omega_{i}}+h_i^t,\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T},m{=}1,\dots,M.   
\end{aligned}
\end{equation}

    
	Thus, by applying the piecewise linear approximation method, the MECADCS problem can be reformulated as the MILP problem in Eq.~\eqref{eq_P_2}.
	\begin{equation}
		\label{eq_P_2}
		\begin{array}{rrl}
			\textbf{(\textbf{P2})} &\min\limits_{\textbf{x,y,p,e,z}} &\sum_{t{=}1}^{T}z^t\\
			& \text{s.t.}&\text{Eqs}.~\eqref{eq_s},\eqref{eq_g_ij_rician},\eqref{eq_g_ij},\eqref{eq_SNR},\eqref{eq_f_q_i},\eqref{eq_f_b},
			\eqref{eq_f_q_j},\\
			&&\eqref{eq_p_out},\eqref{eq_p_in},\eqref{eq_q},\eqref{eq_Rf},\eqref{eq_EH},\eqref{eq_e_b},\eqref{eq_energy_control},\\
			&&\eqref{eq_b},\eqref{eq_x},\eqref{eq_p_x},\eqref{eq_SNR_x_control},\eqref{eq_y},\eqref{eq_Rf_in_y},\eqref{eq_Z},\\
            &&\eqref{eq_f_r_2},\eqref{eq_e_eh_2}.
		\end{array}
	\end{equation}
	
	It is important to note that although linearization converts the MINLP problem into the MILP problem, the NP-hard nature of P2 remains unchanged. However, since MILP problems are relatively easier to solve, we can efficiently obtain solutions using solvers such as Gurobi and CPLEX.

	\section{The GDTS Algorithm for Solving MECADCS} 
	\label{sec_heuristic}
	\subsection{Outline of the GDTS Algorithm}
	Due to the NP-hard nature of MECADCS, we propose a heuristic algorithm GDTS to efficiently obtain sub-optimal solutions for MECADCS. GDTS iteratively makes decisions slot by slot. For each time slot, GDTS works in two phases: link activation decision phase and energy cooperation action decision phase.  
	
	\subsubsection{Link Activation Decision Phase}
	In this phase, GDTS  makes link activation decisions for all nodes. To determine which links to activate, GDTS first prioritizes the links by assigning weights based on link capacity and hop distance, defined as the number of hops from the sender of the link to the corresponding sink. A link's weight is computed as the product of its capacity and hop distance. GDTS then selects links for activation in descending order of their weights. When selecting a new link for activation, a prerequisite is that activating the new link will not lead to violations of the constraints in problem \textbf{P1}. The link selection process continues until no new links can be activated, marking the end of the link activation decision phase.
	
	In GDTS, only the links whose corresponding sender satisfies the conditions in Eq.~\eqref{eq_link_act_cond} will be assigned weights. Specifically, Eq.~\eqref{eq_link_act_cond_1} ensures the node has sufficient energy for the required action, while Eq.~\eqref{eq_link_act_cond_2} requires that the node has data to transmit. 
	
	\begin{subequations}
		\label{eq_link_act_cond}
		\begin{align}
			b_i^t \geq &p_{\min},\label{eq_link_act_cond_1}\\
			\sum_{k{\in}\mathcal{K}}q_{i,k}^t > &0.
			\label{eq_link_act_cond_2}
		\end{align}   
	\end{subequations}
	
	The advantage of this prioritization is illustrated in the example network shown in Fig.~\ref{fig_hop_exp}, where nodes \textit{A} and \textit{B} both have sufficient energy, and they have 2 and 3 data packets to route to sink $C$, respectively. Each node's data buffer size is 5. Fig.~\ref{fig_hop_exp} also shows schedules corresponding to two prioritization methods: with and without considering hop distance. In this figure, a schedule entry $(A,B,2)$ means transmitting 2 data on link $(A,B)$. When prioritizing based on the product of link capacity and hop distance, link $(A,B)$ has a priority value of 4, while link $(B,C)$ has 3. Thus, GDTS prioritizes link $(A,B)$, completing the data collection in the next time slot. In contrast, if hop count is ignored, link $(A,B)$ has a priority of 2, while link $(B,C)$ remains at 3. This causes GDTS to activate link $(B,C)$ first, delaying the data collection by one slot.

	
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.85]{jiang3.png}
		\captionsetup{justification=centering}
		\caption{An example network for illustrating the advantage of prioritizing considering hop distance.}
		\label{fig_hop_exp}
	\end{figure}
	
	\subsubsection{Energy Cooperation Action Decision Phase}
	In this phase, a node $i$ is allowed to transmit energy to others only when the two conditions in Eq.~\eqref{eq_surplus_egy} are satisfied. Constraint Eq.~\eqref{eq_surplus_egy_1} ensures that node $i$ has surplus energy, while constraint Eq.~\eqref{eq_surplus_egy_2} requires that a neighbor of node $i$ is low on energy and has pending data to forward.
	
	\begin{subequations}
		\label{eq_surplus_egy}
		\begin{equation}
			b_i^t{\geq}{b_{\min}}.
			\label{eq_surplus_egy_1}
		\end{equation}    
		\begin{equation}
			\left\{j|j{\in}\mathcal{N}_i,b_j^t{<}b_\text{min}, \exists k{\in}\mathcal{K}, q_{j,k}^t{>}0\right\}{\neq}\emptyset.
			\label{eq_surplus_egy_2}
		\end{equation}   
	\end{subequations}
	
	For a node $i$ allowed to transmit energy by satisfying the constraints in Eq.~\eqref{eq_surplus_egy}, its energy transmission power $\hat{e}_i^t$ is determined using Eq.~\eqref{eq_ecphase_esend} as follows: If it has no cached data packets to forward, $\hat{e}_i^t$ is set to a random value uniformly distributed within the range $[e_{\min},e_{\max}]$, otherwise $\hat{e}_i^t$ is set to $e_{\min}$.
	
	\begin{equation}
		\label{eq_ecphase_esend}	
		\hat{e}_i^t=\left\{
		\begin{matrix}
			e_{\min}, &\sum_{k{\in}\mathcal{K}}q_{i,k}^t{>}0,\\
			\text{rand}([e_{\min}, e_{\max}]), &\sum_{k{\in}\mathcal{K}}q_{i,k}^t{=}0.
		\end{matrix}
		\right.
	\end{equation}
	
	
	\subsection{The GDTS Algorithm}
	The pseudo-code of GDTS is shown in Alg.~\ref{GDTS}. The outermost structure of GDTS is a for-loop, which determines link activation and energy cooperation decisions for nodes slot by slot. Each iteration of the outermost for-loop contains three code segments.

    The first two code segments constitute the link activation decision phase. The first segment (lines \ref{alg_line_for1_begin}-\ref{alg_line_for1_end}) is a for-loop that checks all nodes and assigns weights to links with cached data packets and sufficient energy for communication at the tail node. These links are sorted by weight in descending order at line \ref{alg_line_sort}. The second segment (lines \ref{alg_line_for2_begin}-\ref{alg_line_for2_end}) is another for-loop that incrementally adds new links to the set $\mathbb{S}_D^t$ of links determined for activation. The function $\mathrm{ViolationCheck(\cdot)}$ ensures only links that won't cause violations are added to $\mathbb{S}_D^t$.

    The third segment (lines \ref{alg_line_for3_begin}-\ref{alg_line_for3_end}) is also a for-loop that sets all nodes with surplus energy and satisfying Eq.~\eqref{eq_surplus_egy} to energy transmission status. All other nodes are initialized to energy reception status by line \ref{alg1_line_ini}.

    Packet queues and battery energy levels are updated in line \ref{alg_line_update_nextslot}, preparing to proceed to the next time slot. 

    Finally, GDTS returns all scheduling variables at line~\ref{alg_line_return}.
	
	
\begin{algorithm}
\caption{Pseudocode for GDTS}  
\label{GDTS}  
\KwIn{$\mathcal{G}{=}(\mathcal{V},\mathcal{E})$, $T$, $h_i^t$, $a_i^t$, $\mathcal{N}_i$;}
\KwOut{$\mathbf{x},\mathbf{y},\mathbf{p},\mathbf{e}$;}
Initialize all variables $\mathbf{x},\mathbf{y},\mathbf{p},\mathbf{e}$ to 0;\label{alg1_line_ini}\\
\For{$(t{\leftarrow} 1\hspace{1mm}to\hspace{1mm}T)$}
{
    $\mathbb{S}\leftarrow \emptyset, \mathbb{S}_D^t\leftarrow \emptyset$;\\
    \For{$(i{\leftarrow} 1\hspace{1mm}to\hspace{1mm}N)$\label{alg_line_for1_begin}}
    {
        \If{$(b_i^t{\geq}p_{\text{min}}\hspace{2mm}\&\&\hspace{2mm}\sum_{k{\in}\mathcal{K}}q_{i,k}^t{>}0)$}
        {
            \For{each $l\in \mathcal{O}_i$}
            {
                \If{$\left(min\{b_i^t,p_\text{max}\}{\geq}\frac{\sigma_l^t\cdot\gamma_\text{min}}{g_l^t}\right)$}
                {
                    \For{$(k{\in}\mathcal{K})$}
                    {
                        Compute $f_{l,k}^t$ via Eqs.~\eqref{eq_f_r}$\sim$\eqref{eq_f_q_j};\\
                        \If{$(f_{l,k}^t{>}0)$}
                        {
                            $\mathbb{S}{=}\mathbb{S}\cup l$;\\
                            $l.\text{weight}{=}f_{l,k}^t{\times}\text{hop}_{i,k}$;\\
                        }
                    }
                }
            }
        }
    }\label{alg_line_for1_end}  
    Sort $\mathbb{S}$ by weight in descending order;\label{alg_line_sort}\\
    \For{ $(l{\leftarrow} 1\hspace{1mm}to\hspace{1mm}|\mathbb{S}|)$\label{alg_line_for2_begin}}  
    {
        \If{$ViolationCheck(\mathbb{S}_D^t,l)$}
        {
            $\mathbb{S}_D^t{=}\mathbb{S}_D^t{\cup}l$; $x_{l,k}^t{=}1$;\\
            Compute $p_l^t$ via Eq.~\eqref{eq_p_f};\\
        }
    }\label{alg_line_for2_end}

    \For{$(i{\leftarrow} 1\hspace{1mm}to\hspace{1mm}N)$\label{alg_line_for3_begin}}
    {
        \If{$(Eq.~\eqref{eq_surplus_egy} \hspace{1mm} is\hspace{1mm} \text{satisfied})$}
        {
            $y_{i}^t{=}1$;\\
        }
        \eIf{$(\sum_{k{\in}\mathcal{K}}q_{ik}^t{>}0)$}
        {
            $\hat{e}_i^t{=}e_\text{min}$;\\
        } 
        {
            $\hat{e}_i^t{=}\text{random}([e_\text{min},e_\text{max}])$;\\
        }
    }
}\label{alg_line_for3_end}  
Update $q_{l,k}^{t+1}, b_i^{t+1}$ via Eq.~\eqref{eq_q}, Eq.~\eqref{eq_b};\label{alg_line_update_nextslot}\\
return $\textbf{x},\textbf{y},\textbf{p},\textbf{e}$;\label{alg_line_return}\\
\end{algorithm}	
	


	\begin{lemma}
		The running time complexity of GDTS is $\mathcal{O}\left(T{\cdot}N(N{+}K)\log(N(N{+}K))\right)$.
	\end{lemma}
	\begin{proof}
		The initialization step has a time complexity of $\mathcal{O}(1)$, which does not significantly affect the overall complexity. The time complexity of the outer loop is $\mathcal{O}(T)$, where $T$ is the number of time slots.
		
		Within each iteration of the outer loop, the operations in lines \ref{alg_line_for1_begin}-\ref{alg_line_for1_end} have an overall time complexity of $\mathcal{O}(|\mathcal{E}|{\cdot}|\mathcal{K}|){=}\mathcal{O}(E{\cdot}K)$, where $E$ is the number of edges, and $K$ is the number of sinks. In the worst case, as each link could potentially transmit any data packet, the time complexity of the sorting operation at line \ref{alg_line_sort} is $\mathcal{O}\left(|\mathcal{E}|{\cdot}|\mathcal{K}|{\cdot}\log(|\mathcal{E}|{\cdot}|\mathcal{K}|)\right){=}\mathcal{O}\left(E{\cdot}K{\cdot}\log(E{\cdot}K)\right)$. 
	
	The time complexity in lines \ref{alg_line_for2_begin}-\ref{alg_line_for2_end} is $\mathcal{O}(|\mathcal{N}|{\cdot}|\mathcal{N}_i|){=}\mathcal{O}(N{\cdot}|\mathcal{N}_i|)$, where $N$ is the number of nodes. This is because that, in the worst case, each node needs to examine its battery energy and its neighbors' data queues. The update operation at line \ref{alg_line_update_nextslot} has a time complexity of $\mathcal{O}(N)$.
	
	Combining the above results, the overall time complexity is $\mathcal{O}(T{\cdot} (N{\cdot}|\mathcal{N}_i|{+}E{\cdot}K{+}E{\cdot}K{\cdot}\log(E{\cdot}K))){=}\mathcal{O}(T{\cdot}(N{\cdot}|\mathcal{N}_i|$ $E{\cdot}K{\cdot}\log(E{\cdot}K))){=}\mathcal{O}\left(T{\cdot}N(N{+}K)\log(N{\cdot}(N{+}K))\right)$, where the last step follows from $E{=}N(N{+}K){>}N{\cdot}|\mathcal{N}_i|$.
	\end{proof} 
	
	In summary, by employing GDTS to schedule data transmissions and energy cooperation, we can efficiently accomplish data collection tasks. Multicast energy cooperation scheduling effectively harnesses surplus energy from high-energy nodes, while link scheduling promotes data transmission efficiency in each time slot. The streamlined core design of GDTS consistently achieves solutions for problem P1 within relatively short time slots. This advantage becomes more pronounced when dealing with larger scale WSNs, as demonstrated in comparative simulation experiments in the next section. 

	\section{Performance Evaluation}
	\label{sec_sim}
	\subsection{Simulation Setup}
	To evaluate the performance of GDTS, we conduct numerical simulation experiments using Matlab 2022a on a computer equipped with an A10-8750B CPU and 8GB of RAM, and running the Windows 10 operating system. 
	
	\subsubsection{Performance Metrics}
	We employ five performance metrics: schedule length, amount of collected data, total remaining energy, energy cooperation efficiency, and running time. Schedule length is defined as the number of time slots required to complete a set of specific data collection tasks, where a task is considered finished only after the sensed data packet is successfully routed and received by one of the sinks. The amount of collected data represents the total number of data packets received by the sinks, while the total remaining energy refers to the sum of all nodes' remaining energy at the end of each time slot. Energy cooperation efficiency is defined as the ratio of the total energy harvested by all nodes from RF radios to the total energy consumed for energy transmission. Finally, running time measures the time taken by the algorithm to solve a single problem instance, reflecting its computational efficiency. Schedule length serves as the primary performance metric, while other metrics are used as auxiliary indicators to provide additional insights into the algorithms when needed.
	
	\subsubsection{Algorithms for Comparison}
	For performance comparison with our GDTS, we selected the heuristic algorithm exploiting unicast energy cooperation proposed in \cite{Cui2023} as a benchmark, and denote it here as MinTime-U. In MinTime-U, nodes select neighbors with the lowest energy for point-to-point energy transmission and cannot harvest energy from the RF signals used for data transmission. Additionally, we constructed a variant of GDTS without energy cooperation for comparison, and denote it as GDTS-No. To better distinguish GDTS-No from our M-EC mode GDTS, we label the latter as GDTS-M in this section. Thus, GDTS-M, MinTime-U, and GDTS-No are representative heuristic algorithms for the three energy cooperation modes of M-EC, U-EC, and No-EC, respectively. These three algorithms are all aimed at solving the problem P1 in Eq.~\eqref{eq_P_1}.

   Furthermore, to explore the extreme potential of different energy cooperation modes, we also compose three global-search-based algorithms, each of which exploits one of the three energy cooperation modes: M-EC, U-EC, and No-EC. We labeled these three algorithms as GlobS-M, GlobS-U, and GlobS-No, respectively. GlobS-U can be regarded as an adaption of the algorithm proposed in~\cite{Wang2021}. In all these algorithms, the Gurobi solver is used to search for the optimal solutions. In contrast to above three algorithms (GDTS-M, MinTime-U, and GDTS-No), the new three algorithms (GlobS-M, GlobS-U, and GlobS-No) are targeted for solving the problem P2 in Eq.~\eqref{eq_P_2}. 
   
\subsubsection{Simulation Parameters}
To enhance the credibility of the simulations, the main simulation parameters are set based on related works in the literature. As in~\cite{Lu2015}, we set the maximum communication distance $d_{\max}$ between nodes to 5 meters. Drawing inspiration from \cite{Nishimura2021}, where it was found that sensor transmission power typically exceeds 1 W in conventional applications requiring high data transmission rates, we set the maximum and minimum transmission powers as $p_{\max}{=}1.5$ W and $p_{\min}{=}0.5$ W, respectively. Similarly, the maximum and minimum power for energy transmission are set as $e_{\max}{=}1.5$ W and $e_{\min}{=}0.5$ W, respectively.


In the Rician fading channel model in Eq.~\eqref{eq_g_ij_rician}, the Rician K-factor $K_{i,j}$ is fixed at 10~\cite{Gao2015}. The wavelength $\lambda$ is set to 0.33 m~\cite{He2013}. For Eq.~\eqref{eq_g_ij}, based on the parameter fitting results for a device with 1 W transmission power reported in \cite{He2013}, The transmission gain $G_i$ and the reception gain are respectively set $G_i{=}8$ dBi and $G_j{=}2$ dBi. In addition, the path loss exponent $\beta$ is set to 0.2, and the polarization loss $L_p$ is set to 1 dB. The channel bandwidth $W$ is set to 500 kHz, with a channel noise level $\sigma_l^t{=}{-}30$ dBm~\cite{Boshkovska2015}, and the minimum SNR threshold $\gamma_{\min}$ is set to 10 dB.

The battery capacity $b_{\max}$ is set to 50 mAh~\cite{Zhang2022}. Additionally, the maximum data buffer size $q_{\max}$ is set to 10 Mb. It is noteworthy that larger data buffer sizes and battery capacities can help bypass constraints Eq.~\eqref{eq_f_q_j} and Eq.~\eqref{eq_e_b}, respectively. Similarly, under favorable channel conditions, appropriately setting the minimum transmission power $p_{\min}$ allows for the disregard of constraints in Eq.~\eqref{eq_SNR_control} and Eq.~\eqref{eq_SNR_x_control}.

Regarding the parameters of the energy harvesting model, the energy conversion efficiency of the solar panel is set to 20\%~\cite{Sudevalayam2011}. The mean $\mu_c$ and variance $\rho_c$ of the Gaussian distribution for the solar states are set according to Table III in~\cite{Ku2015}, while the state transition probabilities provided in Table IV in \cite{Ku2015} are utilized. The area of a solar panel is set to 5 cm$^2$. In Eq.~\eqref{eq_EH}, the mean $\mu_i$ is set to 1500, and $\nu_i$ is set to 0.0015~\cite{Cui2023}, and the energy harvesting power upper bound $b_\text{mp}$ is set to 0.15 W.

The main simulation parameters and their default values are summarized in Table~\ref{t22}. A specific combination of these parameters is referred to as a simulation configuration. We examine the effect of each parameter on the algorithms through simulations with different configurations, where only the parameter being inspected varies while all others remain fixed at their default values. 

	
	
		\begin{table}[htbp]
		\centering
		\caption{Simulation Parameter Settings}
		\label{t22}
  \setlength{\tabcolsep}{1.5mm}{
		\begin{tabular}{|c|c||c|c||c|c|}
			\hline
			\textbf{Parameter}       &  \textbf{Value} &  \textbf{Parameter}      &  \textbf{Value}  &  \textbf{Parameter}     &  \textbf{Value} \\
			\hline
			$N$       &     13  &  $K$ &   2   & $E$ &  56\\
			\hline
	     	$q_\text{max}$ &     10 Mb  &  $b_\text{min}$ &   3 J   &  $b_\text{max}$ &    50 mAh\\
			\hline
			$d_\text{max}$     &    5 m   &  $\gamma_{\text{min}}$ & 10 dB  & $\sigma_{l}^t$     & -30 dBm \\
			\hline
			$p_{\text{min}}$ &    0.5 W  & $p_\text{max}$  &   1.5 W     &  $\lambda$      & 0.33 m \\
			\hline
			$p_{\text{rcv}}$ &   0.1 J & $p_{\text{sense}}$ & 0.05 J  &  $\beta$       & 0.2 \\
			\hline
			$e_{\text{min}}$ &    0.5 W  & $e_\text{max}$  &   1 W    &  $W$           &0.5 MHz\\
			\hline
			$e_\text{mp}$ &    0.15 W  & $\mu_i$  &   1500     &  $\nu_i$           &0.0015\\
			\hline
			$\phi_1,\phi_2$     &  100  & $K_{i,j}$ &   10   & $Q$  & 36 Mb \\
			\hline
		\end{tabular}}
	\end{table}
	
   For each simulation configuration, we conduct multiple simulations. In each simulation, 15 nodes are uniformly and randomly deployed in a 20 m ${\times}$ 10 m region, and appropriate adjustment are applied to ensure that each node has at least one path connecting it to any of the sinks. We run 30 simulations for each configuration, and the values of the performance metrics are averaged to derive the final value. Additionally, the corresponding 95\% confidence intervals for these metrics are obtained and presented in the figures showing the simulation results.


	\subsection{Data Collection Evolution Process Example}
We conduct a simple experiment to inspect the data collection evolution process following the schedules generated by the algorithms in terms of performance metrics. This process helps to evaluate how each algorithm manages energy resources and data transmissions over time, better illustrating the potential of different energy cooperation modes. This experiment is performed using the example network shown in Fig.~\ref{fig_Network_top}. This network consists of 13 nodes and 2 sinks, where nodes 4 and 6 are responsible for sensing and transmitting 9 data units destined to sink 15, and nodes 10 and 12 are assigned to sense and transmit 9 data units to sink 1. 

    \begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.42]{jiang4.png}
		\captionsetup{justification=centering}
		\caption{An example network for illustrating data collection evolution process.}
		\label{fig_Network_top}
   \end{figure} 
    
The evolution of the amount of data collected over time, represented as a time series as time slots progress, is shown in Fig.~\ref{fig_dc_h}. Similarly, the evolution of the total remaining energy is depicted in Fig.\ref{fig_remain_energy_h}. Notably, the energy cooperation efficiency in M-EC mode is approximately 8\% for both GlobS-M and GDTS-M, while in U-EC mode, it is around 2\%. Global-search algorithms, such as GlobS-M, take into account inter-slot energy coupling and the causality of energy transmission among nodes, enabling more efficient resource allocation and thereby improving network performance and yielding better solutions. In contrast, heuristic algorithms like GDTS-M quickly identify feasible solutions, though they may not always be optimal. As a result, the heuristic GDTS-M is more appropriate for applications that prioritize computational efficiency.   
	
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.58]{jiang5.png}
		\captionsetup{justification=centering}
		\caption{Evolution process of the amount of collected data.}
		\label{fig_dc_h}
	\end{figure} 
	
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.58]{jiang6.png}
		\captionsetup{justification=centering}
		\caption{Evolution process of the total remaining energy.}
		\label{fig_remain_energy_h}
	\end{figure}


	\subsection{Performance Comparison}
	\subsubsection{Impact of Data Collection Size}
	In this experiment, we investigate the impact of data collection size, defined as the number of data packets to be collected, on the performance of the algorithms. In the simulation configurations in the experiment, the data collection size increases from 20 to 60 with a step size of 8. The data sensing tasks are assigned to nodes that are located at least four hops away from the targeted sink.
 
    The results of the schedule lengths for the algorithms are shown in Fig.~\ref{fig_cmp_DTSA_datasensed}. These results consistently demonstrate that algorithms utilizing energy cooperation outperform those without it, validating the role of energy cooperation in enhancing data collection performance. Notably, as the data collection size increases, the discrepancy in schedule lengths between the algorithms for different energy cooperation modes gradually widens. Compared to MinTime-U, GDTS-M reduces the schedule length by approximately 8\%, and compared to GDTS-No, it achieves a reduction of about 14\%. Additionally, GDTS-M reduces the schedule length by 10\% and 16\% compared to GDTS-U and GDTS-No, respectively.
	
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.58]{jiang7.png}
		\captionsetup{justification=centering}
		\caption{Impact of data collection size on schedule lengths of the algorithms.}
		\label{fig_cmp_DTSA_datasensed}
	\end{figure}
	
	\subsubsection{Impact of Solar Energy Harvesting Rate}  
    To investigate the impact of solar energy harvesting rate, we conduct an experiment with some simulation configurations where the area of the solar panel is increased from 3 cm$^2$ to 11 cm$^2$ with a step size of 2 cm$^2$.

    The results are shown in Fig.~\ref{fig_cmp_DTSA_lambda}. As the area of the solar panel increases, the gap in schedule lengths among the algorithms for different energy cooperation modes gradually diminishes. This suggests that when the solar energy harvesting rate of nodes is low, the influence of energy sharing on schedule length is more pronounced, as it reduces the downtime of nodes caused by energy shortages. However, when the solar energy harvesting rate is high, the differences in schedule lengths among the various algorithms tend to decrease, as nodes generally have sufficient energy to quickly complete data collection tasks.
	
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.58]{jiang8.png}
		\captionsetup{justification=centering}
		\caption{Impact of the solar panel area on schedule lengths of the algorithms.}
		\label{fig_cmp_DTSA_lambda}
	\end{figure}
	
	%\subsubsection{Impact of Maximum transmit power}  
	
\subsubsection{Impact of Maximum Energy Harvesting Power from RF Radio} 
	To further validate the advantages of M-EC, we examined the impact of maximum energy harvesting power from RF radio $e_\text{mp}$ on schedule lengths. In this experiment, we varied the parameter $e_\text{mp}$ from 0.1 J to 0.2 J, with a step size of 0.025 J. The results in Fig.~\ref{fig_e_mp} clearly demonstrate M-EC's superiority over U-EC, with the advantage of M-EC becoming more pronounced as $e_\text{mp}$ increases.

	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.58]{jiang9.png}
		\captionsetup{justification=centering}
		\caption{Impact of maximum energy harvesting power from RF radio on schedule length of the algorithms.}
		\label{fig_e_mp}
	\end{figure}
	
	
	\subsubsection{Impact of the Number of Sinks}  
In all the simulation configurations in this experiment, 12 nodes are uniformly and randomly deployed within a 20 m ${\times}$ 20 m area, whereas the number of sinks $K$ varies from 1 to 4 with a step size of 1. The sinks are positioned along the edges of the network, while several nodes located in the central area are assigned for data sensing. For each data-sensing node, the nearest sink is designated as its corresponding target. The results for the schedule length metric are shown in Fig.~\ref{fig_cmp_DTSA_sink}. These results clearly demonstrate that, with M-EC, both GlobS-M and GDTS-M significantly outperform the algorithms using U-EC and No-EC.
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.58]{jiang10.png}
		\captionsetup{justification=centering}
		\caption{Impact of the number of sinks on schedule length of the algorithms.}
		\label{fig_cmp_DTSA_sink}
	\end{figure}
	
	
	\subsubsection{ANOVA Test for Confirming M-EC's Superiority over U-EC and No-EC}
	To further establish M-EC's superiority over U-EC and No-EC, we conducted an Analysis of Variance (ANOVA) with a significance level of 0.05 on the schedule length metrics of the algorithms, using a data collection size of 18. The ANOVA results, presented in Table~\ref{tbl_anova_analysis_H}, reveal a p-value well below the significance level of 0.05 and an F-statistic significantly exceeding the critical value. These findings indicate that the algorithms utilizing M-EC outperform their counterparts with U-EC and No-EC, reinforcing the advantages of employing M-EC. 


	\begin{table}[htbp]	
		\centering
		\caption{ANOVA test and LSD post-hoc analysis for the algorithms}
		\label{tbl_anova_analysis_H}
		\resizebox{0.48\textwidth}{!}{%
			\begin{tabular}{|c|c|c|c|c|c|c|}
				\hline
				\multicolumn{3}{|c|}{\textbf{ANOVA test }} & \multicolumn{4}{c|}{\textbf{LSD post-hoc analysis}} \\
				\hline
				F-statistic  & $p$-value & F-critical & Method & Lb & MD & Ub \\
				\hline
				\multirow{5}{*}{36} & \multirow{5}{*}{$3.25{\times}10^{-25}$} & \multirow{5}{*}{3.25} & GlobS-M & 7.25 & 12.1 & 17  \\ % 合并第2到5行
				\cline{4-7} % 插入水平分割线，不覆盖合并单元格
				& & & GlobS-U& 4.6 & 9.5 & 14.4  \\ % 空行
				\cline{4-7}
    				& & & GlobS-No& 2.4 & 7.3 & 12.1  \\ % 空行
				\cline{4-7}
    				& & & GDTS-U& -10 & -5.1 & -0.25  \\ % 空行
				\cline{4-7}
    				& & & GDTS-No& -12 & -7.5 & -2.6  \\ % 空行
				\hline
			\end{tabular}
		}
	\end{table}
	
    To further assess the ANOVA results, we conducted post hoc pairwise comparison analysis using the Least Significant Difference (LSD) method. As part of this analysis, we calculated the Lower Bound (Lb) and Upper Bound (Ub) of the 95\% Confidence Interval (CI) for the Mean Deviation (MD). The results, presented in Table~\ref{tbl_anova_analysis_H}, show that the confidence intervals for the pairwise comparisons do not include 0, thereby confirming the significant advantages of the algorithms utilizing M-EC over those employing U-EC and No-EC. 

   \subsubsection{Comparison of Approaches to Solve the MECADCS Problem}

The algorithms GlobS-M and GDTS-M both employ M-EC but utilize different approaches to address the MECADCS problem. The first approach, used by GDTS-M, solves the original MINLP problem P1 in Eq.~\eqref{eq_P_1} using heuristic tricks. In contrast, the second approach, adopted by GlobS-M, essentially targeted to a series of problem instances of the MILP problem P2 in Eq.~\eqref{eq_P_2}, which is a piecewise linear approximation of the original MINLP problem P1 in Eq.~\eqref{eq_P_1}. 

In this section, we further explore a third approach, which involves directly solving the original MINLP problem P1 using the advanced optimization software Mosek. To differentiate among the three approaches, we label them as GDTS (heuristic), MILP (approximation), and MINLP (direct optimization). 
  
In this experiment, we additionally consider a third approach of directly solving the original MINLP problem P1 using the mature optimization software Mosek. To highlight the differences among the three approaches, we label the three approaches as GDTS, MILP, and MINLP. Due to the NP-hard nature of the MINLP problem, our comparison is restricted to small-scale instances where $K{=}1$, $Q{=}10$, and the node-link number pairs ($N$, $E$) take the values $(4,6)$, $(6,10)$, and $(8,14)$, respectively. The results for performance metrics—including Running Time (RT), Schedule Length (SL), Amount of Collected Data (ACD), and Total Remaining Energy (TRE)—are presented in detail in Table~\ref{tbl_AE_RC_H}.

\begin{table}[htbp]
    \centering
    \caption{Comparison of Different Approaches to Solve the MECADCS problem}
    \label{tbl_AE_RC_H}
    \resizebox{0.48\textwidth}{!}{%
        \begin{tabular}{|c|c|c|c|c|c|c|}
            \hline
            \multicolumn{2}{|c|}{\textbf{Network scale}}& \multicolumn{5}{c|}{\textbf{Performance}} \\
            \hline
            $N$ & $E$ & Approach & RT (s) & SL & ACD & TRE \\
            \hline
            \multirow{3}{*}{4} & \multirow{3}{*}{6} & GDTS & 0.004 & 15 & 20 & 5.7 \\ 
            \cline{3-7}
            & & MILP & 0.083 & 13 & 20 & 4.1 \\ 
            \cline{3-7}
            & & MINLP & 9.2 & 13 & 20 & 4.27 \\ 
            \hline
            \multirow{3}{*}{6} & \multirow{3}{*}{10} & GDTS & 0.006 & 16 & 20 & 10.4 \\ 
            \cline{3-7}
            & & MILP & 0.173 & 14 & 20 & 7.1 \\ 
            \cline{3-7}
            & & MINLP & 94 & 14 & 20 & 6.87\\ 
            \hline
            \multirow{3}{*}{8} & \multirow{3}{*}{14} & GDTS & 0.014 & 17 & 20 & 18.3 \\ 
            \cline{3-7}
            & & MILP & 3.6 & 15 & 20 & 12.47 \\ 
            \cline{3-7}
            & & MINLP & 463 & 15 & 20 & 12.86 \\ 
            \hline
        \end{tabular}
    }
\end{table}

Compared to the MINLP approach, the MILP approach achieves comparable solution performance while reducing the running time by approximately 100-fold. This significant improvement is attributed to the piecewise linear approximation method, underscoring its effectiveness and efficiency.
   

Compared to the MINLP and MILP approaches, GDTS produces slightly longer schedules but drastically reduces the running time, achieving improvements by several orders of magnitude—up to tens of thousands of times. Due to the significant differences in scale among the approaches, the substantial disparities in running times are more effectively visualized in Fig.~\ref{fig_RT}, where the vertical axis is presented on a logarithmic scale. The results reveal that as the number of nodes increases, the running times of MILP and MINLP grow exponentially, whereas GDTS consistently maintains a running time within the millisecond range. In summary, the results highlight that the piecewise linear approximation significantly reduces computational time without compromising accuracy. Although GDTS results in a marginally longer schedule length, its exceptional efficiency in running time makes it a highly suitable choice for practical applications.
	\begin{figure}[htbp]
		\centering
		\includegraphics[scale=0.39]{jiang11.png}
		\captionsetup{justification=centering}
		\caption{Comparison of running time of GDTS, MILP, and MINLP.}
		\label{fig_RT}
	\end{figure}

    
	\section{Conclusion}
	\label{sec_conclusion}
	For the multicast energy cooperation-assisted data collection paradigm in EH-WSNs, this paper addresses the Multicast Energy Cooperation Assisted Data Collection Scheduling (MECADCS) problem. The objective is to minimize the data collection completion time by jointly optimizing the scheduling decisions for energy cooperation and data communication by exploiting multicast energy cooperation. We formulate the MECADCS problem as an MINLP problem, establish its NP-hardness, and transform it into an MILP problem using piecewise linear approximation. Due to the NP-hard nature of the MECADCS problem, we propose a heuristic algorithm, GDTS, to efficiently derive sub-optimal solutions. Extensive simulations demonstrate that GDTS quickly and significantly reduces the data collection completion time in EH-WSNs by exploiting multicast energy cooperation.

	
	
\bibliographystyle{IEEEtran}
\bibliography{mybibfile}
	
\end{document}


