# 技术背景

*此文件记录了使用的技术、开发设置、技术限制、依赖项和工具使用模式。*

## 使用的技术

*   **核心理论/算法:**
    *   Lyapunov 优化理论
    *   多播能量协作 (M-EC)
    *   启发式在线调度算法 (Lyapunov-MEC)
    *   Lyapunov-UEC (单播能量协作): Lyapunov-MEC 的变种，能量协作阶段采用单播传输决策。
*   **网络与通信模型:**
    *   能量收集无线传感器网络 (EH-WSN)
    *   半双工通信
    *   Rician 衰落信道模型
    *   调整后的 Friis 路径损耗模型
    *   多跳路由
*   **能量模型:**
    *   四状态马尔可夫模型 (环境能量收集)
    *   非线性 RF 能量收集模型 (Boshkovska2015)
*   **仿真与文档:**
    *   Python (用于仿真，版本待确认)
    *   LaTeX (IEEEtran 类，用于论文撰写)
    *   BibTeX (用于参考文献管理)

## 开发设置

*   **操作系统:** Windows 10
*   **硬件:** Intel Core i7 CPU, 16GB RAM (用于仿真)
*   **主要软件:** Python (版本待确认), LaTeX 发行版 (如 TeX Live, MiKTeX), VS Code (用于编辑)

## 技术限制

*   **能量限制:** 节点电池容量有限 ($b_{\max}$)，能量收集具有随机性。
*   **通信限制:** 半双工约束，信道条件随机变化 (衰落)，最小 SNR 要求 ($\gamma_{\min}$)，传输功率限制 ($p_{\min}, p_{\max}, e_{\min}, e_{\max}$)。
*   **存储限制:** 有限的数据缓冲区大小 ($q_{\max}$)。
*   **计算限制:** 理想的联合调度问题是 NP-Hard 的，需要低复杂度的在线算法。
*   **模型简化与实现细节:**
    *   Lyapunov-MEC 算法采用了启发式简化（例如，简化的 EC 增益估计、贪婪链路选择）。
    *   Lyapunov-UEC 算法的能量协作阶段采用迭代贪婪方式选择最佳的（发送者-接收者-功率）单播组合，并将参与节点标记为当前时隙能量协作繁忙。
    *   数据传输功率并非固定 $p_{\min}$，而是动态计算以满足最低 SNR 阈值 ($p_{\text{required\_min\_W}}$)。
    *   RF 能量收集中区分了数据信号和能量信号的收集效率 ($\eta_{\text{data}}$)。
    *   Lyapunov 漂移上界推导中忽略了电池容量上限的饱和效应。
    *   未考虑复杂的干扰模型。
    *   仿真实现采用了网格布局和受限的汇聚节点连接，而非完全随机拓扑。

## 依赖项

*   **软件依赖:**
    *   Python 环境 (版本待确认，需要 numpy, matplotlib 等库，基于文件名推断)。
    *   LaTeX 发行版及相关宏包 (如 `ctex`, `amsmath`, `graphicx`, `algorithm2e`, `natbib`, `hyperref` 等)。
    *   BibTeX 处理器。
*   **数据依赖:**
    *   `mybibfile.bib` 包含所有参考文献条目。
    *   `fig/` 目录包含所有论文中引用的图片文件 (需要确保完整)。

## 工具使用模式

*   **论文撰写:** 使用 LaTeX 编辑器 (如 VS Code + LaTeX Workshop 插件) 编写 `.tex` 文件，使用 BibTeX 管理 `mybibfile.bib`，编译生成 PDF。
*   **仿真:**
    *   使用 Python (`simulation.py`, `comparison_algorithms.py`) 实现系统模型、Lyapunov-MEC、Lyapunov-UEC 算法及对比算法。`simulation.py` 包含 `lyapunov_mec_scheduling`, `lyapunov_noec_scheduling`, 和 `lyapunov_uec_scheduling` 函数。
    *   运行 `simulation.py` 进行单次或多次仿真（其主执行模块已更新以包含 UEC），并可能包含结果聚合与绘图逻辑。该脚本已被修改，使其在主执行时能够将各算法的平均瞬时吞吐量时间序列数据保存到 `mean_instantaneous_throughput.json` 文件。
    *   `parameter_sweep.py` (顺序扫描脚本) 和 `parallel_parameter_sweep.py` (并行扫描脚本) 用于自动化运行不同参数配置下的仿真。
    *   `parallel_parameter_sweep.py` 是当前主要的参数扫描工具，已更新以包含 Lyapunov-UEC 算法，能够针对不同参数进行扫描，支持多次运行取平均，并生成包含置信区间的对比图。
    *   新增 `analyze_convergence_data.py` 脚本，用于读取 `mean_instantaneous_throughput.json` 文件，并计算各算法的收敛时间等性能指标。
    *   新增 `plot_convergence_dynamics.py` 脚本，用于读取 `mean_instantaneous_throughput.json` 文件，对数据进行平滑处理，并绘制各算法在初始阶段的瞬时吞吐量收敛动态对比图 (如图 `fig_convergence_dynamics.png`)。
    *   使用 `visualize_simulation.py` 提供仿真实时动画可视化（主要用于调试和演示，而非生成论文图表）。
*   **版本控制:** (未明确提及，但推荐使用 Git) 用于代码和文档的版本管理。
