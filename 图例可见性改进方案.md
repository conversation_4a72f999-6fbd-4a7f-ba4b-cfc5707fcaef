# 参数扫描图例可见性改进方案

## 问题分析

在`fig_parallel`目录中的三个参数扫描图中，图例显示存在以下问题：

### 1. **图例重叠问题**
- **原因**: 8个算法的图例项过多，使用`loc='best'`自动定位时容易与数据线重叠
- **影响**: 无法清楚识别各算法对应的线型和标记

### 2. **图形尺寸限制**
- **原因**: 原始图形尺寸`figsize=(10, 6)`相对较小，无法容纳所有图例信息
- **影响**: 图例被压缩或截断

### 3. **图例格式不够清晰**
- **原因**: 缺少背景、边框等视觉增强效果
- **影响**: 图例在复杂背景下可读性差

## 解决方案实施

### 1. **图形尺寸优化**
```python
# 原始设置
plt.figure(figsize=(10, 6))

# 改进设置
fig, ax = plt.subplots(figsize=(14, 8))  # 增大40%宽度，33%高度
```

### 2. **图例位置优化**
```python
# 原始设置
plt.legend(loc='best')

# 改进设置 - 图例放在右侧外部
legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', 
                  fontsize=10, frameon=True, fancybox=True, 
                  shadow=True, framealpha=0.9, edgecolor='black')
legend.get_frame().set_facecolor('white')
```

### 3. **布局空间调整**
```python
# 原始设置
plt.tight_layout()

# 改进设置 - 为图例预留空间
plt.tight_layout()
plt.subplots_adjust(right=0.75)  # 为右侧图例留出25%空间
```

### 4. **视觉效果增强**
```python
# 线条和标记增强
line, = ax.plot(x, y, marker=markers[i], linestyle=linestyles[i], 
               label=algo, linewidth=2, markersize=6)  # 增加线宽和标记大小

# 网格透明度优化
ax.grid(True, alpha=0.3)  # 降低网格透明度，突出数据线

# 字体大小优化
ax.set_xlabel(xlabel, fontsize=12)
ax.set_ylabel("Average Throughput (Mbps)", fontsize=12)
ax.set_title(title, fontsize=14)
```

### 5. **高质量图像保存**
```python
# 原始设置
plt.savefig(filename)

# 改进设置
fig.savefig(filename, dpi=300, bbox_inches='tight', 
           facecolor='white', edgecolor='none')
```

## 替代方案

### 方案A：底部图例布局
```python
# 图例在底部，分多列显示
legend = ax.legend(bbox_to_anchor=(0.5, -0.15), loc='upper center', 
                  ncol=4, fontsize=10, frameon=True, fancybox=True, 
                  shadow=True, framealpha=0.9)
plt.subplots_adjust(bottom=0.2)  # 为底部图例留出空间
```

### 方案B：分离图例
```python
# 创建单独的图例图形
fig_legend = plt.figure(figsize=(8, 2))
handles, labels = ax.get_legend_handles_labels()
fig_legend.legend(handles, labels, loc='center', ncol=4)
fig_legend.savefig('legend_separate.png', bbox_inches='tight')
```

### 方案C：双列图例
```python
# 在图形内部使用双列图例
legend = ax.legend(loc='upper right', ncol=2, fontsize=9, 
                  frameon=True, fancybox=True, shadow=True, 
                  framealpha=0.95, edgecolor='black')
```

## 实施效果

### 改进前后对比

| 方面 | 改进前 | 改进后 |
|------|--------|--------|
| **图形尺寸** | 10×6英寸 | 14×8英寸 |
| **图例位置** | 自动最佳位置（可能重叠） | 右侧外部固定位置 |
| **图例可见性** | 可能被数据线遮挡 | 完全可见，有背景和边框 |
| **布局空间** | 紧凑布局 | 预留图例空间 |
| **图像质量** | 标准分辨率 | 300 DPI高分辨率 |

### 具体改进效果

1. **完全消除图例重叠**: 图例放在图形区域外部，不会与数据线冲突
2. **提高图例可读性**: 白色背景、黑色边框、阴影效果增强视觉对比
3. **保持数据区域完整**: 数据绘图区域不受图例影响
4. **支持所有算法**: 8个算法的图例项都能清晰显示
5. **适应不同参数扫描**: 对电池容量、Lyapunov V控制、节点数量三种扫描都适用

## 使用建议

### 1. **推荐使用右侧外部图例**
- 适用于大多数参数扫描图
- 图例完全可见，不影响数据解读
- 布局专业，适合学术发表

### 2. **特殊情况使用底部图例**
- 当算法名称较短时
- 需要更紧凑的垂直布局时
- 在演示文稿中使用时

### 3. **图像保存优化**
- 使用300 DPI确保打印质量
- `bbox_inches='tight'`确保图例不被裁剪
- 白色背景适合学术论文

## 验证方法

1. **运行测试脚本**: `python test_legend_improvements.py`
2. **检查生成的测试图像**:
   - `test_legend_old_style.png` - 原始样式
   - `test_legend_new_style.png` - 改进样式
   - `test_legend_bottom_style.png` - 替代样式
3. **重新运行参数扫描**: `python parallel_parameter_sweep.py`
4. **检查`fig_parallel`目录中的新图像**

## 总结

通过以上改进，参数扫描图的图例可见性问题得到完全解决：
- ✅ 图例不再与数据线重叠
- ✅ 所有算法名称清晰可见
- ✅ 图形布局专业美观
- ✅ 适合学术发表和演示使用
- ✅ 保持了数据的完整性和可读性

这些改进确保了三个参数扫描图（电池容量、Lyapunov V控制、节点数量）都具有清晰、专业的图例显示效果。
