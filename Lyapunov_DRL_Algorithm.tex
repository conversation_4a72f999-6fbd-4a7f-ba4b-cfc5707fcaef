\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amsfonts,amssymb}
\usepackage{graphicx}
\usepackage[ruled,linesnumbered]{algorithm2e}
\usepackage{url}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{cite} % 用于引用文献
\usepackage{ctex} % 支持中文
\usepackage{caption} % 支持 \ContinuedFloat
\usepackage{caption} % 支持 \ContinuedFloat
\usepackage{float} % 支持 [H] 定位符

% 定义一些可能用到的数学符号 (根据用户模型补充)
\newcommand{\Ncal}{\mathcal{N}}
\newcommand{\Kcal}{\mathcal{K}}
\newcommand{\Ecal}{\mathcal{E}}
\newcommand{\Tcal}{\mathcal{T}}
\newcommand{\Ical}{\mathcal{I}}
\newcommand{\Ocal}{\mathcal{O}}
\newcommand{\Scal}{\mathcal{S}} % 状态空间
\newcommand{\Acal}{\mathcal{A}} % 动作空间
\newcommand{\Rcal}{\mathcal{R}} % 奖励函数

\newtheorem{theorem}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{proposition}{Proposition}
\newtheorem{corollary}{Corollary}
\newtheorem{definition}{Definition}
\newtheorem{remark}{Remark}

\title{基于 Lyapunov 引导的多智能体强化学习联合调度算法}
% \author{Your Name} % 替换为您的名字

\begin{document}
\maketitle

\begin{abstract}
% 摘要待填充
本文针对能量收集无线传感器网络中能量协作与数据收集的联合调度问题，提出了一种基于 Lyapunov 引导的多智能体深度强化学习 (MADRL) 方法。该方法利用 Lyapunov 优化理论将长期吞吐量最大化问题转化为逐时隙优化问题，并采用多智能体深度确定性策略梯度 (MADDPG) 算法学习最优的调度策略，以平衡网络吞吐量、队列稳定性和能量约束。
\end{abstract}

\begin{IEEEkeywords}
能量收集无线传感器网络 (EH-WSN), 能量协作, 数据收集, Lyapunov 优化, 多智能体深度强化学习 (MADRL), MADDPG.
\end{IEEEkeywords}

\section{引言}
% 引言待填充
能量收集无线传感器网络 (EH-WSN) 为长期监测应用提供了可持续的解决方案。然而，能量的随机性和时变性给网络资源管理带来了挑战。节点间的能量协作可以有效缓解能量不平衡问题，但需要与数据传输进行联合调度。传统优化方法难以处理该问题的随机性和复杂性。受近期 Lyapunov 引导的强化学习方法 \cite{Liang2024LySAC, Kumar2023LMADDPG} 的启发，本文提出了一种基于 MADRL 的联合调度算法，旨在学习一种能够适应动态环境并优化网络性能的在线策略。 % 示例引用，需要替换为真实引用

\section{系统模型与问题回顾}
% 简要回顾系统模型和优化目标 P3
我们考虑一个包含 $N=|\Ncal|$ 个能量收集传感器节点和 $K=|\Kcal|$ 个汇聚节点的网络。节点 $i \in \Ncal$ 具有数据队列 $q_{i,k}(t)$ (目标为汇聚 $k$) 和能量赤字队列 $B_i(t)$。网络状态为 $\Theta(t) = (\mathbf{q}(t), \mathbf{B}(t))$。根据 Lyapunov 优化推导 (详见 [用户论文相应章节])，原长期吞吐量最大化问题可转化为在每个时隙 $t$ 最大化以下目标函数 $W^*(t)$ 的问题 (P3)：
\begin{align}
    W^*(t) = & \sum_{l=(i,j) \in \Ecal} \sum_{k \in \Kcal} (q_{i,k}(t) - q_{j,k}(t)) f_{l,k}^t + V \sum_{k, l=(i,k)} f_{l,k}^t \nonumber \\
    & + \sum_{i \in \Ncal} B_i(t) (\check{e}_i^t - (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t)) \label{eq:W_star}
\end{align}
其中 $f_{l,k}^t$ 是链路 $l$ 上流向汇聚 $k$ 的数据量，$\check{e}_i^t$ 是节点 $i$ 收集的 RF 能量，$\hat{p}_i^t, \check{p}_i^t, \hat{e}_i^t$ 分别是数据发送、数据接收和能量传输的能耗。$V \ge 0$ 是权衡参数。最大化 $W^*(t)$ 需要在每个时隙 $t$ 确定能量协作决策 $(\mathbf{y}^t, \mathbf{e}^t)$ 和数据传输决策 $(\mathbf{x}^t, \mathbf{p}^t)$，并受瞬时约束 (如能量因果性、功率限制、半双工等) 的限制。求解 P3 是一个复杂的混合整数非线性规划问题，难以直接获得最优解，尤其是在动态变化的环境中。

\section{基于 Lyapunov 引导的 MADRL 方法}
为了利用 DRL 解决逐时隙优化问题 P3，我们首先需要将该问题建模为一个多智能体马尔可夫决策过程 (Multi-Agent Markov Decision Process, MARL)。在此框架下，每个传感器节点 $i \in \Ncal$ 被视为一个智能体 (agent)，它们与网络环境进行交互，并在每个时隙 $t$ 根据观察到的状态选择动作，以最大化一个共同的长期累积奖励。

\subsection{MDP 建模}
一个 MARL 问题通常由以下要素定义：状态空间 $\Scal$、联合动作空间 $\Acal = \times_{i \in \Ncal} \Acal_i$、状态转移概率 $P(s'|s, \mathbf{a})$、联合奖励函数 $\Rcal(s, \mathbf{a})$ 以及折扣因子 $\gamma$。其中 $s \in \Scal$ 是全局状态，$\mathbf{a} = (a_1, ..., a_N) \in \Acal$ 是所有智能体的联合动作，$a_i \in \Acal_i$ 是智能体 $i$ 的动作。

根据 Lyapunov 引导的 DRL 框架，我们将逐时隙优化目标 $W^*(t)$ (Eq.~\eqref{eq:W_star}) 嵌入到奖励函数的设计中。以下是针对我们问题的具体 MDP 要素定义：

\subsubsection{状态空间 (State Space)}
我们采用集中式训练、分布式执行 (CTDE) 的范式。因此，需要区分每个智能体的局部观察 (用于执行) 和训练时 Critic 网络可以访问的全局状态。

\begin{itemize}
    \item \textbf{局部观察 $o_i(t)$:} 这是智能体 $i$ 在时隙 $t$ 开始时能获取的信息，用于其 Actor 网络做出决策。它至少应包含：
    \begin{itemize}
        \item 自身数据队列状态: $\mathbf{q}_i(t) = \{q_{i,k}(t)\}_{k \in \Kcal}$
        \item 自身能量赤字队列状态: $B_i(t)$
        \item 自身电池能量水平: $b_i(t)$
        \item 自身相关的信道增益: $\mathbf{g}_i(t) = \{g_{i,j}^t\}_{j:(i,j)\in\Ecal} \cup \{g_{j,i}^t\}_{j:(j,i)\in\Ecal}$
        \item (可选) 自身环境能量收集信息: $h_i^t$ 或其统计/预测值
    \end{itemize}
    令 $o_i(t) = (\mathbf{q}_i(t), B_i(t), b_i(t), \mathbf{g}_i(t), ...)$。

    \item \textbf{全局状态 $s(t)$:} 这是训练时 Critic 网络可以访问的信息，用于评估联合动作的价值。它可以是所有智能体局部观察的集合：
    $$ s(t) = (o_1(t), o_2(t), ..., o_N(t)) $$
    或者包含更全面的信息，例如完整的信道矩阵 $\mathbf{g}^t$。
\end{itemize}
状态空间 $\Scal$ 是所有可能的全局状态 $s(t)$ 的集合。

\subsubsection{动作空间 (Action Space)}
每个智能体 $i$ 在时隙 $t$ 需要决定其能量协作和数据传输相关的动作。其动作 $a_i(t)$ 包含以下部分：

\begin{itemize}
    \item \textbf{能量协作决策:}
    \begin{itemize}
        \item 能量传输/接收状态: $y_i^t \in \{0, 1\}$ (离散)
        \item 能量传输功率: $\hat{e}_i^t \in [e_{\min}, e_{\max}]$ (连续, 当 $y_i^t=1$ 时有效)
    \end{itemize}
    \item \textbf{数据传输决策 (针对所有出向链路 $l=(i,j) \in \Ocal_i$):}
    \begin{itemize}
        \item 链路激活状态 (按目标汇聚分): $\{x_{l,k}^t \in \{0, 1\}\}_{k \in \Kcal}$ (离散)
        \item 链路传输功率: $p_l^t \in [p_{\min}, p_{\max}]$ (连续, 当 $\sum_k x_{l,k}^t > 0$ 时有效)
    \end{itemize}
\end{itemize}
智能体 $i$ 的动作空间 $\Acal_i$ 是所有可能的 $a_i(t)$ 的集合。联合动作空间为 $\Acal = \times_{i \in \Ncal} \Acal_i$。这是一个混合离散-连续动作空间。

\textit{处理混合动作空间：} 像 MADDPG 这样的算法原生支持连续动作。对于离散动作 ($y_i^t, x_{l,k}^t$)，需要采用特殊技术，例如 Gumbel-Softmax 采样或将 Actor 输出的连续值映射到离散空间。对于连续动作 ($\hat{e}_i^t, p_l^t$)，Actor 可以直接输出，并通过激活函数 (如 tanh) 缩放到指定范围。动作选择后，需在环境模拟中强制执行瞬时约束 (如半双工约束 Eq. (219)，功率范围约束 Eq. (226), (239) 等)。

\subsubsection{奖励函数 (Reward Function)}
根据 Lyapunov 引导的 DRL 框架，奖励函数应直接关联于逐时隙优化目标 $W^*(t)$。我们采用全局奖励机制，即所有智能体在时隙 $t$ 获得相同的奖励 $R(t)$：
\begin{equation}
    R(t) = W^*(t) \label{eq:reward}
\end{equation}
其中 $W^*(t)$ 由 Eq.~\eqref{eq:W_star} 定义。在环境模拟中，执行联合动作 $\mathbf{a}(t) = (a_1(t), ..., a_N(t))$ 后，需要计算出该动作导致的实际数据流量 $f_{l,k}^t$、收集能量 $\check{e}_i^t$ 和各项能耗 $\hat{p}_i^t, \check{p}_i^t, \hat{e}_i^t$，然后代入 Eq.~\eqref{eq:W_star} 计算 $W^*(t)$ 作为当前时隙的奖励 $R(t)$。这个奖励信号直接引导智能体学习最大化 $W^*(t)$，从而间接优化原始的长期吞吐量目标并满足稳定性约束。

\subsubsection{状态转移概率 $P(s'|s, \mathbf{a})$}
状态转移由网络环境的动态决定，包括：
\begin{itemize}
    \item 队列更新: 根据 Eq. (149) 和 Eq. (339) 更新 $q_{i,k}(t+1)$ 和 $B_i(t+1)$。
    \item 电池更新: 根据 Eq. (206) 更新 $b_i(t+1)$。
    \item 信道演化: 根据信道模型更新 $g^t$ 到 $g^{t+1}$。
    \item 能量收集: 根据能量收集模型更新 $h_i^t$ 到 $h_i^{t+1}$。
\end{itemize}
DRL 算法通常是无模型 (model-free) 的，不需要显式知道状态转移概率，而是通过与环境交互采样学习。

\subsubsection{折扣因子 $\gamma$}
在 Lyapunov 引导的框架中，由于目标是优化长期平均性能（通过最小化漂移加惩罚），折扣因子 $\gamma$ 通常设置为接近 1 的值（例如 0.99），以更重视未来的奖励。

\subsection{多智能体深度确定性策略梯度 (MADDPG)}
为了解决上述 MARL 问题，我们采用多智能体深度确定性策略梯度 (MADDPG) 算法 \cite{Lowe2017MADDPG}。MADDPG 是 DDPG 算法在多智能体环境下的扩展，特别适用于智能体之间存在交互且动作空间包含连续变量的场景。它采用了**集中式训练、分布式执行 (Centralized Training with Decentralized Execution, CTDE)** 的范式，有效缓解了多智能体学习中的非平稳性问题，提高学习效率和稳定性。

\subsubsection{网络结构}
在 MADDPG 框架下，每个智能体 $i \in \Ncal$ 维护两组神经网络：
\begin{itemize}
    \item \textbf{Actor 网络 $\mu_i(o_i | \theta_i^\mu)$:} 输入为智能体 $i$ 的局部观察 $o_i(t)$，输出为确定性的动作 $a_i(t)$。参数为 $\theta_i^\mu$。
    \item \textbf{Critic 网络 $Q_i(s, a_1, ..., a_N | \theta_i^Q)$:} 输入为全局状态 $s(t)$ 和所有智能体的联合动作 $\mathbf{a}(t) = (a_1(t), ..., a_N(t))$，输出为该状态-联合动作对的 Q 值估计，即 $Q_i(s(t), \mathbf{a}(t))$。参数为 $\theta_i^Q$。Critic 网络仅在训练阶段使用，用于指导 Actor 网络的更新。
\end{itemize}
此外，为了稳定训练过程，每个 Actor 和 Critic 网络都配有一个对应的**目标网络 (Target Network)**，分别表示为 $\mu'_i(o_i | \theta_i^{\mu'})$ 和 $Q'_i(s, a_1, ..., a_N | \theta_i^{Q'})$。目标网络的参数 $\theta_i^{\mu'}$ 和 $\theta_i^{Q'}$ 通过对原始网络参数进行软更新 (soft update) 得到。

\subsubsection{训练过程 (CTDE)}
MADDPG 的训练过程遵循 CTDE 原则：
\begin{enumerate}
    \item \textbf{经验收集 (分布式执行):} 在每个时隙 $t$，每个智能体 $i$ 根据其局部观察 $o_i(t)$ 和当前的 Actor 策略 $\mu_i(o_i(t) | \theta_i^\mu)$ (通常加入探索噪声) 选择并执行动作 $a_i(t)$。环境根据联合动作 $\mathbf{a}(t)$ 转移到下一状态 $s(t+1)$ 并计算全局奖励 $R(t)$ (根据 Eq.~\eqref{eq:reward})。经验元组 $(s(t), \mathbf{a}(t), R(t), s(t+1))$ 被存储到共享的经验回放缓冲区 $D$ 中。
    \item \textbf{网络更新 (集中式训练):}
    \begin{itemize}
        \item 从缓冲区 $D$ 中随机采样一个 mini-batch 的经验 $(s, \mathbf{a}, R, s')$。
        \item \textbf{更新 Critic 网络:} 对于每个智能体 $i$，计算目标 Q 值：
        $$ y_i = R + \gamma Q'_i(s', \mu'_1(o'_1), ..., \mu'_N(o'_N))|_{\theta'^{Q}_i} $$
        其中 $o'_j$ 是下一状态 $s'$ 中智能体 $j$ 的局部观察，动作由目标 Actor 网络 $\mu'_j$ 产生。然后，通过最小化损失函数来更新 Critic 网络参数 $\theta_i^Q$：
        $$ L(\theta_i^Q) = \mathbb{E}_{(s,\mathbf{a},R,s') \sim D} \left[ (Q_i(s, a_1, ..., a_N | \theta_i^Q) - y_i)^2 \right] $$
        \item \textbf{更新 Actor 网络:} 对于每个智能体 $i$，使用来自相应 Critic 网络 $Q_i$ 的策略梯度来更新其 Actor 网络参数 $\theta_i^\mu$。策略梯度近似为：
        $$ \nabla_{\theta_i^\mu} J(\mu_i) \approx \mathbb{E}_{s \sim D, a_j \sim \mu_j} \left[ \nabla_{\theta_i^\mu} \mu_i(o_i) \nabla_{a_i} Q_i(s, a_1, ..., a_N)|_{a_i=\mu_i(o_i)} \right] $$
        \item \textbf{软更新目标网络:} 缓慢更新目标网络的参数：
        \begin{align*}
            \theta_i^{Q'} &\leftarrow \tau \theta_i^Q + (1-\tau) \theta_i^{Q'} \\
            \theta_i^{\mu'} &\leftarrow \tau \theta_i^\mu + (1-\tau) \theta_i^{\mu'}
        \end{align*}
        其中 $\tau \ll 1$ 是软更新系数。
    \end{itemize}
\end{enumerate}
通过这个过程，每个智能体的 Actor 网络学会在仅基于局部观察的情况下做出有利于最大化全局奖励 (即 $W^*(t)$) 的决策，而 Critic 网络则利用全局信息来提供准确的价值评估，从而指导 Actor 的学习。

\subsection{算法伪代码}
所提出的基于 Lyapunov 引导的 MADDPG 联合调度算法的训练过程总结在算法 \ref{alg:L-MADDPG-Training} 中。

\begin{algorithm}[htbp] % 保持 htbp 或其他允许的浮动选项
\caption{基于 Lyapunov 引导的 MADDPG 训练算法 (L-MADDPG Training)}
\label{alg:L-MADDPG-Training}
\KwIn{智能体数量 $N$, 最大训练 episodes $E_{max}$, 每个 episode 的最大时隙数 $T_{max}$, 经验回放缓冲区 $D$, mini-batch 大小 $M_b$, 折扣因子 $\gamma$, 目标网络软更新系数 $\tau$, Actor 学习率 $\alpha_\mu$, Critic 学习率 $\alpha_Q$, Lyapunov 权重 $V$}
\KwOut{训练好的各智能体 Actor 网络策略 $\mu_1, ..., \mu_N$}

\For{每个智能体 $i=1$ to $N$}{
    随机初始化 Actor 网络 $\mu_i$ (参数 $\theta_i^\mu$) 和 Critic 网络 $Q_i$ (参数 $\theta_i^Q$)\;
    初始化目标网络: $\theta_i^{\mu'} \leftarrow \theta_i^\mu$, $\theta_i^{Q'} \leftarrow \theta_i^Q$\;
}
初始化经验回放缓冲区 $D$\;

\end{algorithm} % 结束第一部分

\begin{algorithm}[htbp] % 开始第二部分
\ContinuedFloat % 标记为续接
\caption[]{基于 Lyapunov 引导的 MADDPG 训练算法 (续)} % 修改标题

\For{episode $e=1$ to $E_{max}$}{
    获取初始全局状态 $s(0)$ (包含所有 $o_i(0)$)\;
    \For{时隙 $t=0$ to $T_{max}-1$}{
        % 1. 分布式执行动作
        \For{每个智能体 $i=1$ to $N$}{
            获取局部观察 $o_i(t)$\;
            根据当前 Actor 策略选择动作 $a_i(t) = \mu_i(o_i(t) | \theta_i^\mu) + \text{Noise}$\; % 加入探索噪声
        }
        执行联合动作 $\mathbf{a}(t) = (a_1(t), ..., a_N(t))$\;
        环境计算实际结果 ($f_{l,k}^t, \check{e}_i^t, \hat{p}_i^t, \check{p}_i^t, \hat{e}_i^t$) 并计算全局奖励 $R(t) = W^*(t)$ (根据 Eq.~\eqref{eq:reward})\;
        环境转移到下一全局状态 $s(t+1)$ (包含所有 $o_i(t+1)$)\;
        将经验元组 $(s(t), \mathbf{a}(t), R(t), s(t+1))$ 存入 $D$\;
        $s(t) \leftarrow s(t+1)$\;

% 2. 集中式训练网络 (如果缓冲区足够大)
            \If{$|D| > M_b$}{
            从 $D$ 中随机采样 $M_b$ 个经验 $(s^j, \mathbf{a}^j, R^j, s'^j)$\;
            \For{每个智能体 $i=1$ to $N$}{
                % 计算目标 Actor 动作
                $\mathbf{a}' = (\mu'_1(o'^j_1), ..., \mu'_N(o'^j_N))$\;
                % 计算目标 Q 值
                $y_i^j = R^j + \gamma Q'_i(s'^j, \mathbf{a}' | \theta_i^{Q'})$\;
                % 更新 Critic 网络
                通过最小化损失 $L(\theta_i^Q) = \frac{1}{M_b}\sum_j (Q_i(s^j, \mathbf{a}^j | \theta_i^Q) - y_i^j)^2$ 来更新 $\theta_i^Q$\;
                % 更新 Actor 网络
                计算策略梯度 $\nabla_{\theta_i^\mu} J(\mu_i) \approx \frac{1}{M_b}\sum_j \nabla_{\theta_i^\mu} \mu_i(o_i^j) \nabla_{a_i} Q_i(s^j, a_1^j, ..., a_N^j)|_{a_i=\mu_i(o_i^j)}$\;
                使用该梯度更新 $\theta_i^\mu$\;
            }
            % 软更新所有目标网络
            \For{每个智能体 $i=1$ to $N$}{
                $\theta_i^{Q'} \leftarrow \tau \theta_i^Q + (1-\tau) \theta_i^{Q'}$\;
                $\theta_i^{\mu'} \leftarrow \tau \theta_i^\mu + (1-\tau) \theta_i^{\mu'}$\;
            }
        } % 结束 If
    } % 结束时隙循环
} % 结束 episode 循环
\end{algorithm} % 结束第二部分

\subsection{复杂度分析}
我们分析所提出的 L-MADDPG 算法在每个时隙的计算复杂度。复杂度主要分为两个阶段：训练阶段和执行阶段。

\subsubsection{执行阶段复杂度 (分布式)}
在训练完成后，算法进入执行阶段。在每个时隙 $t$，每个智能体 $i$ 仅需要运行其本地的 Actor 网络 $\mu_i$ 来根据局部观察 $o_i(t)$ 决定动作 $a_i(t)$。假设 Actor 网络是一个具有 $L_\mu$ 层、每层最多 $N_\mu$ 个神经元的多层感知机 (MLP)，并且局部观察 $o_i(t)$ 的维度为 $D_o$，动作 $a_i(t)$ 的维度为 $D_a$。那么，一次前向传播的计算复杂度大约为 $O(D_o N_\mu + \sum_{l=1}^{L_\mu-2} N_\mu^2 + N_\mu D_a)$。由于有 $N$ 个智能体独立执行此操作，因此在执行阶段，每个时隙的总计算复杂度约为 $O(N(D_o N_\mu + N_\mu^2 L_\mu + N_\mu D_a))$。这通常是一个较低的复杂度，适合在线分布式执行。

\subsubsection{训练阶段复杂度 (集中式)}
训练阶段通常在线下进行，计算复杂度较高。在每个训练步中（对应于从经验回放缓冲区采样一个 mini-batch）：
\begin{itemize}
    \item \textbf{Critic 更新:} 对于每个智能体 $i$，需要计算目标 Q 值和当前 Q 值，并进行反向传播。计算目标 Q 值需要所有 $N$ 个目标 Actor 网络进行一次前向传播，以及目标 Critic 网络 $Q'_i$ 的一次前向传播。计算当前 Q 值需要 Critic 网络 $Q_i$ 的一次前向传播。反向传播更新 Critic 参数 $\theta_i^Q$ 的复杂度与网络规模相关。假设 Critic 网络 $Q_i$ 输入维度为 $D_s + N D_a$ (全局状态 + 联合动作)，有 $L_Q$ 层，每层最多 $N_Q$ 个神经元，则更新一个 Critic 的复杂度粗略估计为 $O(M_b ( (D_s + N D_a) N_Q + N_Q^2 L_Q + N_Q ))$。总共 $N$ 个 Critic 网络，因此 Critic 更新的总复杂度约为 $O(N M_b ( (D_s + N D_a) N_Q + N_Q^2 L_Q ))$。
    \item \textbf{Actor 更新:} 对于每个智能体 $i$，需要计算策略梯度。这涉及到 Actor 网络 $\mu_i$ 的一次前向传播和反向传播，以及 Critic 网络 $Q_i$ 的一次前向传播（用于计算梯度 $\nabla_{a_i} Q_i$）。更新一个 Actor 的复杂度粗略估计为 $O(M_b ( (D_o N_\mu + N_\mu^2 L_\mu + N_\mu D_a) + ((D_s + N D_a) N_Q + N_Q^2 L_Q) ))$。总共 $N$ 个 Actor 网络，因此 Actor 更新的总复杂度约为 $O(N M_b ( (D_o N_\mu + N_\mu^2 L_\mu) + ((D_s + N D_a) N_Q + N_Q^2 L_Q) ))$。
    \item \textbf{目标网络更新:} 复杂度较低，主要是参数复制和加权求和。
\end{itemize}
因此，训练阶段每个更新步的总体复杂度主要由 Actor 和 Critic 网络的更新决定，大致为 $O(N M_b (\text{ActorNetSize} + \text{CriticNetSize}))$，其中网络规模取决于层数和神经元数量。虽然训练复杂度较高，但由于是离线进行，通常是可以接受的。

与 Lyapunov-MEC 算法（其复杂度为 $O(N^2 d_{max} + EK \log(EK))$）相比，L-MADDPG 在执行阶段的复杂度可能更低（取决于网络规模和 $N$），但在训练阶段复杂度要高得多。然而，L-MADDPG 有望通过学习获得比启发式方法更优的性能。
\subsection{讨论与总结}
本文提出的基于 Lyapunov 引导的 MADDPG 算法 (L-MADDPG) 为能量收集无线传感器网络中的联合能量协作与数据收集调度问题提供了一种新的解决思路。通过将 Lyapunov 优化理论与多智能体深度强化学习相结合，该方法旨在克服传统启发式算法在处理复杂动态环境和寻找全局最优策略方面的局限性。

L-MADDPG 的核心优势在于其 CTDE 训练范式和基于 Lyapunov 的奖励设计。CTDE 允许在训练时利用全局信息指导学习，而在执行时仅依赖局部观察，提高了算法的可扩展性和实用性。基于 Lyapunov 漂移加惩罚设计的奖励函数，则确保了智能体在学习最大化网络吞吐量（通过 $W^*(t)$ 体现）的同时，能够隐式地维持队列稳定性和满足能量约束。

尽管 L-MADDPG 的训练过程计算复杂度较高，且需要仔细调整超参数，但其潜力在于能够学习到更复杂、更自适应的调度策略，有望在性能上超越传统的启发式方法。通过离线训练，训练好的 Actor 网络可以在线部署，以较低的复杂度进行实时决策。接下来的章节将通过仿真实验来评估所提出 L-MADDPG 算法的实际性能，并与基准算法进行比较。

% 参考文献示例 (需要创建 mybibfile.bib 文件)
\bibliographystyle{IEEEtran}
\bibliography{mybibfile}

\end{document}