import json
import numpy as np
from scipy import stats
from statsmodels.stats.multicomp import pairwise_tukeyhsd
import argparse

def run_anova_and_tukey(data_file_path, param_name, param_value, algorithm_names, alpha=0.05):
    """
    Loads data from a JSON file, performs ANOVA and <PERSON><PERSON>'s HSD test.

    Args:
        data_file_path (str): Path to the JSON data file.
        param_name (str): The top-level parameter key (e.g., "V_CONTROL").
        param_value (str): The specific value for the parameter (e.g., "10").
        algorithm_names (list): A list of algorithm names to compare.
        alpha (float): Significance level.

    Returns:
        None. Prints the results.
    """
    try:
        with open(data_file_path, 'r') as f:
            all_data = json.load(f)
    except FileNotFoundError:
        print(f"Error: Data file not found at {data_file_path}")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {data_file_path}")
        return

    if param_name not in all_data:
        print(f"Error: Parameter '{param_name}' not found in the data.")
        return
    if param_value not in all_data[param_name]:
        print(f"Error: Value '{param_value}' for parameter '{param_name}' not found.")
        return

    param_specific_data = all_data[param_name][param_value]
    
    datasets = [] # List of np.arrays, one for each algorithm's data
    # For Tukey HSD, we need a flat list of all values and corresponding labels
    # These will be populated by the robust data preparation later if ANOVA is significant.
    
    # Temporary list to hold names of algorithms actually found and used for ANOVA
    # This helps correctly report which algorithms are being compared if some are skipped.
    anova_algo_names = []

    for algo_name in algorithm_names:
        if algo_name not in param_specific_data:
            print(f"Warning: Algorithm '{algo_name}' not found for {param_name}={param_value}. Skipping.")
            continue
        
        data = param_specific_data[algo_name]
        if isinstance(data, list) and len(data) > 1:
            datasets.append(np.array(data))
            anova_algo_names.append(algo_name) # Keep track of algos used in ANOVA
        else:
            print(f"Warning: Data for algorithm '{algo_name}' is not a list or has insufficient samples. Skipping.")

    if len(datasets) < 2:
        print("Error: Need at least two groups of data to perform ANOVA.")
        return

    print(f"\n--- ANOVA and Tukey's HSD Results for {param_name} = {param_value} ---")
    print(f"Comparing algorithms: {', '.join(anova_algo_names)}") # Use names of algos in ANOVA
    print(f"Significance level (alpha): {alpha}")

    # Perform ANOVA
    f_statistic, p_value_anova = stats.f_oneway(*datasets)
    
    print("\nANOVA Results:")
    print(f"  F-statistic: {f_statistic:.4f}")
    print(f"  P-value: {p_value_anova:.4g}")

    # Calculate F-critical value
    df_between = len(datasets) - 1
    df_within = sum(len(d) for d in datasets) - len(datasets)
    if df_within <= 0:
        print("Error: Cannot calculate F-critical value due to insufficient degrees of freedom within groups.")
        f_critical = float('nan')
    else:
        f_critical = stats.f.ppf(1 - alpha, df_between, df_within)
        print(f"  F-critical value (at alpha={alpha}): {f_critical:.4f}")

    if p_value_anova < alpha:
        print("\nANOVA result is significant (p < alpha). Performing Tukey's HSD post-hoc test...")
        
        # Prepare data robustly for Tukey's HSD
        # This ensures that only data from algorithms actually present in the input `algorithm_names`
        # and also valid in the JSON data are included, maintaining a defined order.
        tukey_data_collections = [] # To store {'name': algo_name, 'data': np.array(data_values)}
        all_values_for_tukey = []   # Flat list of all data points for Tukey
        group_labels_for_tukey = [] # Corresponding group labels for all_values_for_tukey

        for an in algorithm_names: 
            if an in param_specific_data and isinstance(param_specific_data[an], list) and len(param_specific_data[an]) > 1:
                algo_data_values = param_specific_data[an]
                tukey_data_collections.append({'name': an, 'data': np.array(algo_data_values)})
                all_values_for_tukey.extend(algo_data_values)
                group_labels_for_tukey.extend([an] * len(algo_data_values))
        
        if not tukey_data_collections or len(tukey_data_collections) < 2:
            print("  Skipping Tukey's HSD: Not enough valid groups after filtering for Tukey.")
            print("--- End of Analysis ---")
            return

        # MODIFICATION START: Print calculated means before Tukey's HSD
        print("\nCalculated Means for groups in Tukey HSD (order as in --algos):")
        for item in tukey_data_collections:
            print(f"  {item['name']}: {np.mean(item['data']):.4f}")
        # MODIFICATION END

        final_all_values_for_tukey = np.array(all_values_for_tukey)
        final_group_labels_for_tukey = np.array(group_labels_for_tukey)

        if len(np.unique(final_group_labels_for_tukey)) >= 2: # Ensure at least two unique groups for comparison
            try:
                tukey_result = pairwise_tukeyhsd(final_all_values_for_tukey, final_group_labels_for_tukey, alpha=alpha)
                print("\nTukey's HSD Results:")
                print(tukey_result)
            except Exception as e:
                print(f"Error during Tukey's HSD: {e}")
                print("  Ensure all groups have sufficient data and variance for Tukey's HSD.")
        else:
            print("  Skipping Tukey's HSD: Not enough unique groups for pairwise comparison after final preparation.")
            
    else:
        print("\nANOVA result is not significant (p >= alpha). No post-hoc test needed.")
    print("--- End of Analysis ---")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Perform ANOVA and Tukey's HSD test on simulation data.")
    parser.add_argument("data_file", help="Path to the JSON data file (e.g., anova_raw_throughput_data.json)")
    parser.add_argument("param_name", help="Parameter name to analyze (e.g., V_CONTROL, BATTERY_MAX_J)")
    parser.add_argument("param_value", help="Specific value of the parameter (e.g., 10, 20.0)")
    parser.add_argument("--algos", nargs='+', default=["Lyapunov-MEC", "Lyapunov-UEC", "Lyapunov-NoEC"],
                        help="List of algorithm names to compare (default: Lyapunov-MEC Lyapunov-UEC Lyapunov-NoEC)")
    parser.add_argument("--alpha", type=float, default=0.05, help="Significance level (default: 0.05)")

    args = parser.parse_args()

    run_anova_and_tukey(args.data_file, args.param_name, args.param_value, args.algos, args.alpha)

    # Example usage if not using command line:
    # print("\nExample run for V_CONTROL = 10:")
    # run_anova_and_tukey(
    #     data_file_path="anova_raw_throughput_data.json",
    #     param_name="V_CONTROL",
    #     param_value="10", # Ensure this is a string if keys in JSON are strings
    #     algorithm_names=["Lyapunov-MEC", "Lyapunov-UEC", "Lyapunov-NoEC"],
    #     alpha=0.05
    # )
    # print("\nExample run for BATTERY_MAX_J = 10.0:")
    # run_anova_and_tukey(
    #     data_file_path="anova_raw_throughput_data.json",
    #     param_name="BATTERY_MAX_J",
    #     param_value="10.0", # Ensure this is a string if keys in JSON are strings
    #     algorithm_names=["Lyapunov-MEC", "Lyapunov-UEC", "Lyapunov-NoEC"],
    #     alpha=0.05
    # )
    # print("\nExample run for NUM_NODES = 20:")
    # run_anova_and_tukey(
    #     data_file_path="anova_raw_throughput_data.json",
    #     param_name="NUM_NODES",
    #     param_value="20", # Ensure this is a string if keys in JSON are strings
    #     algorithm_names=["Lyapunov-MEC", "Lyapunov-UEC", "Lyapunov-NoEC"],
    #     alpha=0.05
    # )
