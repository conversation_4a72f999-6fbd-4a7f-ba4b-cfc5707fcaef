\documentclass[journal]{IEEEtran}
\usepackage{amsmath,amsfonts}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{bm}
\usepackage{graphicx}
\usepackage[colorlinks, linkcolor=blue, citecolor=blue]{hyperref}
\usepackage{amsmath}
\usepackage{epstopdf}
\usepackage{booktabs}  
\usepackage{multirow}   
\usepackage{array}     
\usepackage{csquotes}
\usepackage{color}
\usepackage{amssymb}
\usepackage{enumerate}
\usepackage[numbers,sort&compress]{natbib}

\graphicspath{{fig/}}

\usepackage[ruled,linesnumbered]{algorithm2e}
\newtheorem{proposition}{Proposition}
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{corollary}{Corollary}
\newtheorem{remark}{Remark}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}
\newenvironment{proof}{{\quad \it Proof:}}{$\hfill\blacksquare$\par}
\hyphenation{}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
		T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\usepackage{balance}
\setlength{\parindent}{1em}


\begin{document}
	\section{System Model}
	\label{sec_model}
	\subsection{Network Model}
	We consider an energy harvesting wireless sensor network (EH-WSN) deployed in a specific area (e.g., 100m $\times$ 100m), which consists of $N$ static sensor nodes in the set $\mathcal{N}{=}\{1,2,\ldots,N\}$ and $K$ sink nodes in the set $\mathcal{K} {=}\{1,2,\ldots, K\}$.
	   The network topology is represented by a graph $\mathcal{G} {=} (\mathcal{V}, \mathcal{E})$, where $\mathcal{V}{=}\mathcal{N}{\cup}\mathcal{K}$. The locations of sensor nodes and sink nodes are randomly generated within the area, ensuring that the resulting network topology is connected. $\mathcal{E}{=}\{1,2,\ldots,E\}{\subseteq}\mathcal{V}{\times}\mathcal{V}$ denotes the set of potential communication links (also referred to as data links) between nodes, where the existence of a link depends on whether the distance between nodes is less than the maximum communication range $R_{\max}$.
	
	Nodes in the EH-WSN sense the environment and generate data, which are routed to one of the sink nodes, possibly via multi-hop relays. To simulate potential energy bottlenecks near sink nodes, it is assumed that each sink node establishes direct incoming links only with its nearest sensor node. Data transmission from other nodes to a sink node must be accomplished through multi-hop routing. We assume that the network topology thus formed ensures that all sensor nodes have at least one multi-hop path to a sink node. Sink nodes collect the incoming data and subsequently forward them to a remote backend server for further processing via long-range links.
	   
   In this work, we consider a time-slotted periodic data collection application in EH-WSNs, where each operational cycle is divided into three phases: task assignment, data sensing, and data routing. In the task assignment phase, the system assigns a set of data collection tasks to selected nodes. Each task is defined by a quadruple $(src,sink,time,size)$, where $src$ is the source node assigned the task, $sink$ is the destination sink node for the task's data, $time$ is the time slot for task assignment, and $size$ is the amount of data to be collected (in bits). In the data sensing phase, nodes sense and collect the specified amount of data, grouping it into packets for transmission. Multiple packets may be generated if the data amount is large. Let $\mathcal{Q}{=}\{1,2,\ldots,Q\}$ denote the set of all data packets generated from the tasks. In the subsequent data routing phase, the prepared data packets in $\mathcal{Q}$ are routed to their respective destinations. A task is considered complete only when all data collected during the sensing phase successfully reach their designated sink node. This paper aims to achieve joint optimized scheduling of data transmission and energy cooperation through M-EC to maximize the long-term average (LTA) throughput of the network. The time slots for the data routing phase are denoted as $\mathcal{T}{=}\{1,2,\ldots,T\}$. For simplicity, we assume each time slot has a unit time duration, allowing power and energy to be used interchangeably in subsequent discussions.


	\subsection{Data Communication Model}
	Data communication adheres to the following fundamental assumptions: (1) nodes operate in half-duplex mode, i.e., they cannot transmit and receive data simultaneously; (2) all data communications are unicast; (3) a node cannot process different data on multiple outgoing or incoming links concurrently; (4) co-channel interference is negligible due to mechanisms such as orthogonal frequency-division multiplexing and allowing distant links to share channels~\cite{Mamat2023}.

    Let $a_{i,k}^t$ denote the amount of data generated by node $i$ in time slot $t$ destined for sink $k$. Let $p_{\text{sense}}$ be the energy consumed by a node to sense one unit of data, and let $s_i^t$ be the energy consumed by node $i$ for data sensing in time slot $t$. Thus, the total energy consumption for data sensing is given by:
	\begin{equation}
		\label{eq_s}
		\begin{aligned}
			s_i^t = \sum_{k \in \mathcal{K}}p_\text{sense}\cdot a_{i,k}^t,\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	

    Let $\mathbb{S}^t_D$ be the set of active links in time slot $t$, and $p_{l}^t$ be the transmission power allocated to link $l$ in time slot $t$. The maximum and minimum transmission powers for data communication by a node are denoted by $p_{\text{max}}$ and $p_{\text{min}}$, respectively. The transmission power of each active link must satisfy the following constraint:
	\begin{equation}
		\label{eq_p_control}
		\begin{aligned}
			p_\text{min}\leq p_{l}^t\leq p_\text{max}, {\quad}{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	

We assume that all data links follow a Rician fading channel model~\cite{Gao2015}. For a link $l(i,j)$, the probability density function of its channel power gain $g_{i,j}^t$ (or $g_{l}^t$) is given by Eq.~\eqref{eq_g_ij_rician}, where $\overline{g}_{i,j}^t$ represents the expected value of $g_{i,j}^t$, and $K_{i,j}$ is the Rician K-factor, defined as the ratio of the power in the dominant line-of-sight component to the total power in the scattered components. The variable $x$ is a random variable drawn from an exponential distribution with a mean of one, and $I_0(\cdot)$ denotes the zero-order modified Bessel function~\cite{Gao2015}.
    \begin{equation} 
	\label{eq_g_ij_rician}
 \begin{aligned}
	   f_{g_{i,j}^t}(x) = &\frac{K_{i,j}+1}{\overline{g}_{i,j}^t} e^{-x(K_{i,j}+1)/\overline{g}_{i,j}^t - K_{i,j}}\\
    &{\cdot}I_0 \left( \sqrt{\frac{4K_{i,j}(K_{i,j}+1)x}{\overline{g}_{i,j}^t}} \right), \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{aligned}
    \end{equation} 


We assume that for a link $l(i,j)$, the expected channel power gain $\overline{g}_{i,j}^t$ follows an adjusted Friis free-space equation (see Eq.~\eqref{eq_g_ij}). Here, $d_{i,j}$ is the distance between nodes $i$ and $j$, $G_i$ and $G_j$ are the transmitter and receiver antenna gains, respectively, $L_p$ accounts for polarization losses, $\lambda$ is the signal wavelength, and $\beta$ is an adjustment factor introduced for short-range transmission scenarios~\cite{He2013}.
    \begin{equation} 
	\label{eq_g_ij}
	   \overline g_{i,j}^t = \frac{G_i G_j}{L_p} \left( \frac{\lambda}{4 \pi (d_{i,j} + \beta)} \right)^2,\quad \forall i,j{\in}\mathcal{N},t{\in}\mathcal{T}.
    \end{equation} 


	Let $\sigma_{l}^t$ denote the corresponding noise power. The Signal-to-Noise Ratio (SNR) of the received signal on link $l$ is then given by Eq.~\eqref{eq_SNR}. To ensure that the received signal on link $l$ can be correctly decoded, the corresponding SNR must not fall below a specified threshold $\gamma_{\text{min}}$, as shown in Eq.~\eqref{eq_SNR_control}~\cite{Patrik2004}.
    \begin{align}		
		\mathrm{SNR}_{l}^t=&\frac{p_{l}^tg_{l}^t}{\sigma_{l}^t},&{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR}\\
        \mathrm{SNR}_{l}^t \geq& \gamma_{\min}, &{\forall}l{\in}\mathbb{S}^t_D, t{\in}\mathcal{T}.\label{eq_SNR_control} 
	\end{align}	


	Let $r_{l}^t$ represent the data transmission rate on link $l$ during time slot $t$, which can be expressed as Eq.~\eqref{eq_r}, where $W$ denotes the channel bandwidth of the link. We assume all links have the same bandwidth $W$. Since each time slot has a unit time duration, $r_l^t$ effectively represents the capacity of link $l$ in time slot $t$.
	\begin{equation}
		\label{eq_r}	
		\begin{aligned}
			r_{l}^t=W\log_2\left(1+	\mathrm{SNR}_{l}^t\right),{\quad}{\forall}l{\in}\mathbb{S}^t_D,t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	 
	
To support data routing, it is assumed that each node is equipped with a data buffer of capacity $q_{\text{max}}$, which maintains a separate data queue for each sink node. Let $q_{i,k}^t$ denote the length of the data queue at node $i$ for data destined to sink $k$. Similarly, $f_{l,k}^t$ denotes the amount of data transmitted over link $l$ destined for sink $k$ in time slot $t$. Data transmission is constrained by the link capacity and node buffer sizes, as given by the constraint equations from Eq.~\eqref{eq_f_r} to Eq.~\eqref{eq_f_q_j}, where $b_j^t$ is the battery energy of node $j$ in time slot $t$, and $p_{\text{rcv}}$ is the energy consumed by a node to receive one unit of data.
    \begin{align}
f_{l,k}^t{\leq}&r_{l}^t,{\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_r}\\
f_{l,k}^t{\leq}&q_{i,k}^t, {\quad} &\forall l{\in}\mathcal{E},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_q_i}\\
f_{l,k}^t{\leq}&\frac{b_{j}^t}{p_\text{rcv}},& \forall  l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T},\label{eq_f_b}\\
f_{l,k}^t{\leq}&q_\text{max}{-}\sum_{k{\in}\mathcal{K}}q_{j,k}^t & \forall l{\in}\mathcal{O}_i{\cap}\mathcal{I}_j,j{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.\label{eq_f_q_j}
    \end{align}
    
	
    Under the above constraints, the parameter $\gamma_{\text{min}}$ in Eq.~\eqref{eq_SNR_control} ensures a minimum link quality for data transmission. The transmission power $p_l^t$ of link $l$ in time slot $t$ is set to the minimum power required to meet this SNR threshold, while also adhering to the node's minimum and maximum power limits. This power $p_l^t$ is calculated as:
	\begin{equation}
		\label{eq_p_f}
			p_l^t = \max\left\{p_{\min},\frac{\sigma_{l}^t \cdot \gamma_{\min}}{g_l^t}\right\}, \quad \forall l \in \mathbb{S}^t_D, t \in \mathcal{T}.
	\end{equation}
    Furthermore, the calculated $p_l^t$ must satisfy the constraint $p_l^t \le p_{\max}$ (see Eq.~\eqref{eq_p_control}); if this condition is not met (e.g., due to extremely poor channel conditions requiring excessive power), link $l$ is not usable for data transmission in the current time slot $t$. This power determines the data transmission rate $r_l^t$ of the link (Eq.~\eqref{eq_r}), which in turn affects the actual data flow $f_{l,k}^t$ (Eq.~\eqref{eq_f_r}).

    The energy consumed by node $i$ in time slot $t$ for data transmission and data reception, denoted by $\hat{p}_i^t$ and $\check{p}_i^t$ respectively, are given by Eq.~\eqref{eq_p_out} and Eq.~\eqref{eq_p_in}, respectively.
	\begin{align}
	\hat p_i^t =&\sum_{l\in\mathcal{O}_i}p_l^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_p_out}\\
    \check p_i^t = &\sum_{l\in\mathcal{I}_i}\sum_{k{\in}\mathcal{K}}p_\text{rcv}{\cdot}f_{l,k}^t, &\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_p_in}
	\end{align}

	The update formula for the data queue at node $i$ for data destined to sink $k$ in time slot $t$ is as follows:
	\begin{equation}
		\label{eq_q}
		\begin{aligned}
			q_{i,k}^{t{+}1}=q_{i,k}^{t}+a_{i,k}^{t}+\sum_{l\in \mathcal{I}_i}f_{l,k}^t-\sum_{l\in \mathcal{O}_i}f_{l,k}^t,\\{\forall}i{\in}\mathcal{N},k{\in}\mathcal{K},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}

    
	\subsection{Energy Harvesting Model}
	  Each node is equipped with a rechargeable battery of capacity $b_{\max}$ for energy storage. It is assumed that nodes are powered by energy harvested from environmental sources (e.g., solar energy) via dedicated devices. This paper adopts the hidden Markov chain framework described in~\cite{Ku2015} to model the process of nodes harvesting energy from the environment. This model categorizes the energy harvesting conditions of a node into four states: \textit{Excellent}, \textit{Good}, \textit{Fair}, and \textit{Poor}, indexed from 1 to 4, respectively. For each state $c{\in}\{1,2,3,4\}$, the energy harvested by node $i$ in time slot $t$ (denoted as $h_i^t$) follows a Gaussian distribution with mean $\mu_c$ and variance $\rho_c$. Furthermore, the probability of transitioning from state $c$ to state $c'$ is denoted by $P_{cc'}$. These parameters are derived from actual solar irradiance measurements~\cite{Ku2015}.

    In addition to harvesting energy from environmental sources, nodes can also harvest radio frequency (RF) energy from the radio signals of neighboring nodes to supplement their energy reserves. These radio signals include: (1) dedicated energy signals for wireless power transfer (WPT) to achieve energy cooperation; (2) unicast data signals intended for specific destinations, from which neighboring nodes can also harvest energy due to the broadcast nature of wireless signals. We assume that a node's energy cooperation neighbors are also its communication neighbors. To prevent energy signals from interfering with data signals, it is assumed that energy cooperation is conducted on a different frequency channel from data transmission, while the energy antenna can harvest energy from both energy transmission and data transmission signals.
	Let $\mathbb{S}_E^t$ be the set of nodes participating in energy transmission in time slot $t$, and let $e_{\max}$ and $e_{\min}$ be the maximum and minimum energy transmission powers of a node, respectively. The amount of energy used by node $i$ for energy transmission in time slot $t$ is denoted by $\hat{e}_i^t$, and its constraint is shown in Eq.~\eqref{eq_epower_bounds_first}.
	\begin{equation}
		\label{eq_epower_bounds_first}
		e_\text{min}\leq \hat{e}_i^t\leq e_\text{max}, \quad {\forall} i{\in} \mathbb{S}_E^t, t{\in}\mathcal{T}.
	\end{equation}
	
	Let $\hat{p}_j^t$ and $\hat{e}_j^t$ denote the data transmission power and energy transmission power of node $j$ in time slot $t$, respectively. The total RF power received by node $i$ in time slot $t$ consists of the power from data transmission signals and energy transmission signals from neighboring nodes. Let $Rf_{i,\text{data}}^t = \sum_{j{\in}\mathcal{N}_i} \hat{p}_j^t g_{j,i}^t$ denote the total power from data signals, and let $Rf_{i,\text{energy}}^t = \sum_{j{\in}\mathcal{N}_i} \hat{e}_j^t g_{j,i}^t$ denote the total power from energy signals. If node $i$ itself is transmitting energy ($i \in \mathbb{S}_E^t$), it cannot receive RF energy. Therefore, the RF power components received by node $i$ for energy harvesting are:
	\begin{equation}
		\label{eq_Rf_components}
		(Rf_{i,\text{data}}^t, Rf_{i,\text{energy}}^t) = 
		\left\{
		\begin{aligned}
			&(\sum_{j{\in}\mathcal{N}_i} \hat{p}_j^t g_{j,i}^t, \sum_{j{\in}\mathcal{N}_i} \hat{e}_j^t g_{j,i}^t), && \text{if } i{\notin}\mathbb{S}^t_E, \\
			&(0, 0), && \text{if } i{\in}\mathbb{S}^t_E.
		\end{aligned}
		\right.
	\end{equation}
	We assume that the efficiency of harvesting energy from data signals may be lower than that from dedicated energy signals. An efficiency factor $\eta_{\text{data}} \in [0, 1]$ is introduced to represent the efficiency of converting data signal power into effective RF power available for harvesting (set to $\eta_{\text{data}}=0.1$ in simulations). Thus, the effective total input power $Rf_i^{\text{eff},t}$ for RF energy harvesting at node $i$ can be expressed as:
	\begin{equation}
	    \label{eq_Rf_eff}
	    Rf_i^{\text{eff},t} = \eta_{\text{data}} Rf_{i,\text{data}}^t + Rf_{i,\text{energy}}^t
	\end{equation}

        Due to losses in the energy harvesting process, the energy actually harvested by node $i$ is less than the effective RF signal power $Rf_i^{\text{eff},t}$ it receives. Let $\check{e}_i^t$ denote the energy harvested by node $i$ from $Rf_i^{\text{eff},t}$, calculated as shown in Eq.~\eqref{eq_EH}. In the equation, $\Omega_{i}$ and $\Psi_{i}^{t}$ are defined by Eq.~\eqref{eq_omega} and Eq.~\eqref{eq_psi}, respectively; $\exp(\cdot)$ is the natural exponential function; the constant $e_\text{mp}$ represents the maximum energy a node can harvest from RF signals in a single time slot (i.e., the maximum RF energy harvesting power); and $\mu_{i}$ and $\nu_{i}$ are fixed parameters determined by the relevant hardware components~\cite{Boshkovska2015}.
\begin{subequations}
\label{eq_EH1}
\begin{align}
    \check{e}_{i}^{t}=&\frac{\left[\Psi_{i}^{t}-e_\text{mp}\Omega_{i}\right]}{1-\Omega_{i}},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_EH}\\
	\Omega_{i}=& \frac{1}{1+\exp(\mu_{i}\nu_{i})},&\forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T},\label{eq_omega}\\
	\Psi_{i}^{t}=&\frac{e_\text{mp}}{1+\exp\left(-\mu_{i}(Rf_{i}^{\text{eff},t}-\nu_{i})\right)},& \forall i{\in}\mathcal{N},\forall t{\in}\mathcal{T}.\label{eq_psi} 
\end{align}
\end{subequations}
	Considering the battery capacity $b_\text{max}$ of a node, the actual energy obtained by node $i$ in time slot $t$ (denoted as $\bar{e}_i^t$) must satisfy the following constraints.
	\begin{align}
		\bar{e}_i^t{\leq}&\check{e}_i^t{+}h_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T},\label{eq_e_eh}\\
		\bar{e}_i^t{\leq}&b_{\text{max}}{-}b_i^t, &\forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.\label{eq_e_b}
	\end{align}  
	
    Considering the causality of energy usage, we assume that energy harvested in one time slot can only be used in subsequent time slots. Therefore, the energy consumption behavior of a node within a time slot must satisfy Eq.~\eqref{eq_energy_control}.
	\begin{equation}
		\label{eq_energy_control}
		s_{i}^t+\hat{p}_i^t+\check{p}_i^t+\hat{e}_{i}^t\leq b_i^t, \quad {\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
    In summary, the battery energy evolution formula for node $i$ is given by:
	\begin{equation}
		\label{eq_b}
		\begin{aligned}
			b_i^{t+1}=b_i^t+\bar e_i^t-s_i^t-\hat p_i^t-\check p_i^t-\hat{e}_{i}^t\quad \forall i{\in}\mathcal{N},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}	
	
\subsection{Data Communication and Energy Cooperation Scheduling Model}
    In the energy cooperation-assisted data collection paradigm, energy cooperation and data transmission actions must be carefully scheduled within each time slot. The energy cooperation schedule for each slot determines which nodes participate in energy exchange and their corresponding power allocations, while the data communication schedule determines which links are activated, what data is transmitted, and at what power. The activation of a link implicitly determines its sender and receiver. The energy cooperation schedule is responsible for assigning nodes to time slots for energy transmission/reception, whereas the data communication schedule assigns links to time slots for data transmission. In~\cite{Patrik2004}, these are referred to as node-based and link-based allocations, respectively. Given that data collection primarily relies on the data communication schedule to transmit data to sinks via links, the terms \textit{data collection schedule} and \textit{data communication schedule} are used interchangeably. The combination of an energy cooperation schedule and a data communication schedule is termed an Energy Cooperation-assisted Data Collection (ECaDC) joint schedule.

   To describe the ECaDC schedule, the following decision variables are defined: Let $x_{l,k}^t{\in}\{0,1\}$ be a binary variable, which is 1 if link $l$ is activated in time slot $t$ to transmit data packets destined for sink $k$, and 0 otherwise. Let $y_i^t{\in}\{0,1\}$ be a binary variable representing the energy interaction state of node $i$ in time slot $t$: $y_i^t{=}1$ indicates that node $i$ transmits energy, and $y_i^t{=}0$ indicates that it receives energy. Let $\textbf{x}{:=}[x_{l,k}^t| l{\in} \mathcal{E}, k {\in}\mathcal{K}, t {\in}\mathcal{T}]$, $\textbf{y}{:=}[y_i^t| i {\in} \mathcal{N}, t{\in}\mathcal{T}]$, $\textbf{p} {:=} [p_l^t | l {\in} \mathcal{E}, t {\in} \mathcal{T}]$, and $\textbf{e}{:=} [\hat{e}_i^t|i {\in} \mathcal{N}, t {\in} \mathcal{T}]$. The vectors $\textbf{x}$, $\textbf{y}$, $\textbf{p}$, and $\textbf{e}$ collectively constitute the ECaDC schedule, denoted as ECaDC($\textbf{x,y,p,e}$).

    It is assumed that a node can only transmit one type of data packet within a single time slot. Further considering the half-duplex mode for data communication, the activation status of links must satisfy the following constraint:
	\begin{equation}
		\label{eq_x}
		\sum_{k\in \mathcal{K}}\sum_{l\in \mathcal{I}_i}x_{l,k}^t+\sum_{k\in \mathcal{K}}\sum_{l\in\mathcal{O}_i}x_{l,k}^t\leq1,  \quad{\forall} i{\in} \mathcal{N},t{\in}\mathcal{T}.
	\end{equation}
	
	Using the decision variables in $\textbf{x}$, the constraint on $p_l^t$ in Eq.~\eqref{eq_p_control} can be rewritten as Eq.~\eqref{eq_p_x}.
	\begin{equation}
		\label{eq_p_x}
		\begin{aligned}
			\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{min}\leq p_{l}^t\leq\sum_{k\in \mathcal{K}}x_{l,k}^t{\cdot}p_\text{max},\quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
		\end{aligned}
	\end{equation}
	
	The SNR constraints in Eq.~\eqref{eq_SNR_control} and Eq.~\eqref{eq_SNR} can be combined into Eq.~\eqref{eq_SNR_x_control}, where $\phi_1$ is a sufficiently large constant ($\phi_1 {\gg} \gamma_\text{min}$) to ensure that this constraint is automatically satisfied when the link is not active.
	\begin{equation} 
		\label{eq_SNR_x_control}
		p_{l}^tg_{l}^t+\phi_1(1-\sum_{k\in \mathcal{K}}x_{lk}^t)\geq\gamma_\text{min}\sigma_{l}^t, \quad \forall l{\in}\mathcal{E},t{\in}\mathcal{T}.
	\end{equation}	
	 
	The constraint in Eq.~\eqref{eq_epower_bounds_first} for controlling \(\hat{e}_i^t\) can be rewritten as:
	\begin{equation}
		\label{eq_y}
		y_{i}^t{\cdot}e_\text{min}\leq \hat{e}_i^t\leq y_{i}^t{\cdot}e_\text{max}, \quad \forall i{\in} \mathcal{N}, t{\in}\mathcal{T}.
	\end{equation}
	
\section{Long-Term Average Throughput Maximization Problem Formulation}
\label{sec_problem_def}

In this paper, we employ the Lyapunov optimization framework to address the joint scheduling problem of multicast energy cooperation and data collection. The primary objective is to maximize the long-term average (LTA) throughput of the system, while ensuring the stability of data queues and maintaining the LTA battery energy levels of all nodes above predefined thresholds.

The LTA system throughput, denoted as $\bar{\mathcal{D}}$, is defined as the time-averaged rate at which data packets are successfully delivered to their respective sink nodes:
\begin{equation}
	\label{eq_lta_throughput}
	\bar{\mathcal{D}} = \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E\left[ \sum_{k \in \mathcal{K}} \sum_{l=(i,k) \in \mathcal{I}_k} f_{l,k}^t \right]
\end{equation}
    where $\mathcal{I}_k$ is the set of incoming links to sink node $k$, and $f_{l,k}^t$ is the amount of data destined for sink $k$ transmitted over link $l$ in time slot $t$. The expectation $E[\cdot]$ is taken over the random channel states and energy harvesting processes.

    Our goal is to maximize $\bar{\mathcal{D}}$ subject to the following constraints:
    \begin{itemize}
        \item \textbf{Data Queue Stability:} All data queues $q_{i,k}(t)$ must be stable, meaning their long-term average sizes are bounded, which implicitly ensures that all generated data are eventually delivered successfully.
        \item \textbf{LTA Battery Energy Constraint:} The LTA battery energy level of each node $i$ must be maintained above a predefined threshold $\delta_i$:
        \begin{equation}
            \label{eq_lta_energy}
            \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge \delta_i, \quad \forall i \in \mathcal{N}
        \end{equation}
        \item \textbf{Instantaneous Constraints:} All system model constraints defined in Section~\ref{sec_model} must be satisfied in each time slot $t$. These include energy causality (Eq.~\eqref{eq_energy_control}), battery capacity (Eq.~\eqref{eq_e_b}), power limits (Eqs.~\eqref{eq_p_x}, \eqref{eq_y}), half-duplex operation (Eq.~\eqref{eq_x}), data flow constraints (Eqs.~\eqref{eq_f_r}-\eqref{eq_f_q_j}), and SNR requirements (Eq.~\eqref{eq_SNR_x_control}), among others.
    \end{itemize}

    Formally, the optimization problem can be stated as:
	\begin{equation}
		\label{eq_P1_prime}
		\begin{array}{rrl}
			\textbf{(\textbf{P1'})} &\max\limits_{\textbf{x,y,p,e}} & \bar{\mathcal{D}} \\
			& \text{s.t.}& \text{Data queue stability for all } q_{i,k}(t) \\
			         && \text{LTA battery energy (Eq.~\eqref{eq_lta_energy})} \\
			&& \text{Instantaneous constraints (Eqs}.~\eqref{eq_s}-\eqref{eq_y})
		\end{array}
	\end{equation}
    
    Problem P1' is inherently a stochastic network optimization problem aiming to maximize a long-term average utility subject to long-term constraints, making it suitable for Lyapunov optimization techniques. Despite its focus on long-term average performance, the complexity of the underlying scheduling decisions remains. Lemma~\ref{proposition_1} will show that the underlying scheduling subproblem associated with P1' is NP-Hard.
    
    Directly solving Problem P1' is highly challenging due to the LTA nature of its objective and constraints, as well as the complex coupling and randomness across time slots. To overcome this difficulty, this paper will leverage Lyapunov optimization techniques to transform the original problem P1' into a series of deterministic optimization problems that can be solved in each time slot.

	\begin{lemma}
		\label{proposition_1}
		The underlying scheduling decision associated with Problem P1' is NP-Hard.
	\end{lemma}
	
	\begin{proof}
    To prove the NP-Hardness of the underlying scheduling decision associated with Problem P1', we reduce a known NP-Complete problem, the Flexible Job Shop Scheduling (FJSP) problem~\cite{SDPJDLSKT2024}, to a specific instance of a scheduling subproblem. This subproblem, termed Data Collection Scheduling (DCS), is defined under a set of simplifying assumptions: all nodes have sufficient initial energy thus no energy cooperation is needed, there are no data buffer queues, and data transmissions use fixed power.
		
		\textbf{DCS Problem:} Given a network graph $\mathcal{G}(\mathcal{V},\mathcal{E})$ and a set of data packets $\mathcal{P}$. Each packet $p \in \mathcal{P}$ is generated at a source node $s_p \in \mathcal{V}$ and must be transmitted to its destination node $d_p \in \mathcal{V}$ via one of a predefined set of routes $\mathcal{R}_p$. The transmission (or processing) time of packet $p$ at an intermediate node $v \in \mathcal{V}$ is denoted by $\tau_p^v$. The objective of the DCS problem is to determine a link scheduling scheme that minimizes the makespan, i.e., the maximum completion time for all packets to reach their respective destinations.
		
		\textbf{FJSP Problem:} As per~\cite{SDPJDLSKT2024}, the FJSP problem is defined as follows: Given a set of jobs $\mathcal{J}=\{J_1, \dots, J_m\}$ and a set of machines $\mathcal{M}=\{M_1, \dots, M_w\}$. Each job $J_j \in \mathcal{J}$ consists of a sequence of $n_j$ operations $(O_{j1}, O_{j2}, \dots, O_{jn_j})$ that must be performed in the specified order. For each operation $O_{ji}$, there is a set of alternative machines $\mathcal{M}_{ji} \subseteq \mathcal{M}$ on which it can be processed. The processing time of operation $O_{ji}$ on machine $M_k \in \mathcal{M}_{ji}$ is $p_{jik}$. The goal of FJSP is to select a machine for each operation and to determine the start times for all operations to minimize the makespan ($C_{\max}$), subject to: (1) an operation, once started, must be completed without interruption; (2) each machine can process at most one operation at any time; (3) the precedence constraints among operations of the same job must be satisfied.
		
		\textbf{Reduction from FJSP instance to DCS instance:} Consider an arbitrary instance of FJSP. We construct a corresponding instance of DCS with the following mapping:
		\begin{itemize}
			\item \textbf{Node Mapping ($\mathcal{M} \cup \mathcal{J} \rightarrow \mathcal{V}$):} 
			For each machine $M_k \in \mathcal{M}$ in FJSP, create a corresponding machine node $v_{Mk}$ in DCS. For each job $J_j \in \mathcal{J}$ in FJSP, create two unique nodes in DCS: a source node $v^s_{Jj}$ and a destination node $v^d_{Jj}$. Thus, the set of nodes in the DCS instance is $\mathcal{V} = \{v_{Mk} | M_k \in \mathcal{M}\} \cup \{v^s_{Jj}, v^d_{Jj} | J_j \in \mathcal{J}\}$.
			
			\item \textbf{Packet Mapping ($\mathcal{J} \rightarrow \mathcal{P}$):} 
			For each job $J_j \in \mathcal{J}$ in FJSP, create a corresponding data packet $P_j \in \mathcal{P}$ in DCS. The source node for packet $P_j$ is $s_{P_j} = v^s_{Jj}$, and its destination node is $d_{P_j} = v^d_{Jj}$.
			
			\item \textbf{Route and Operation Mapping ($\mathcal{O}_{ji}, \mathcal{M}_{ji} \rightarrow \mathcal{R}_{P_j}$):} 
			For the sequence of operations $(O_{j1}, O_{j2}, \dots, O_{jn_j})$ of job $J_j$, a possible route $R \in \mathcal{R}_{P_j}$ for packet $P_j$ is constructed as follows: The route starts from $v^s_{Jj}$, passes sequentially through $n_j$ machine nodes, and ends at $v^d_{Jj}$. Specifically, if the $l$-th operation $O_{jl}$ of job $J_j$ is assigned to be processed on machine $M_k \in \mathcal{M}_{jl}$, then the intermediate node corresponding to the $l$-th processing step in the route for packet $P_j$ is $v_{Mk}$. Thus, a complete route takes the form $(v^s_{Jj}, v_{M_{k_1}}, v_{M_{k_2}}, \dots, v_{M_{k_{n_j}}}, v^d_{Jj})$, where $M_{k_l}$ is the machine on which operation $O_{jl}$ is processed.
			
			\item \textbf{Processing Time Mapping ($p_{jik} \rightarrow \tau_{P_j}^{v_{Mk}}$):} 
			The processing time $p_{jik}$ of operation $O_{ji}$ on machine $M_k \in \mathcal{M}_{ji}$ in FJSP is mapped to the transmission time $\tau_{P_j}^{v_{Mk}} = p_{jik}$ of packet $P_j$ at the corresponding machine node $v_{Mk}$ in DCS.
		\end{itemize}
		
		This reduction can clearly be performed in polynomial time. It can be shown that an FJSP instance has a feasible schedule with a makespan of at most $T_{makespan}$ if and only if the constructed DCS instance has a feasible schedule with a makespan of at most $T_{makespan}$. Since FJSP is NP-Complete, this implies that the defined DCS problem is NP-Hard. Given that the DCS problem is a special case of the underlying scheduling decision associated with Problem P1' under specific simplifying conditions, the joint energy cooperation and data collection scheduling decision involved in Problem P1' is also NP-Hard.
	\end{proof}
	
\section{Lyapunov Optimization Framework}
\label{sec_lyapunov}
To solve the stochastic network optimization problem P1' defined in Eq.~\eqref{eq_P1_prime}, this paper adopts the Lyapunov optimization theory~\cite{neely2010stochastic}. The core idea of this theoretical framework is to transform a stochastic optimization problem with long-term average (LTA) objectives and constraints into a series of deterministic optimization problems that can be solved independently in each time slot.

\subsection{Virtual Queues}
One of the core mechanisms of the Lyapunov optimization framework is the use of virtual queues to handle LTA constraints. In this paper, in addition to the actual data queues $q_{i,k}(t)$ (defined in Eq.~\eqref{eq_q}) used to track data backlogs, we introduce a virtual energy deficit queue $B_i(t)$ for each sensor node $i \in \mathcal{N}$ to address the LTA battery energy constraint (Eq.~\eqref{eq_lta_energy}). This virtual queue $B_i(t)$ measures the cumulative energy deficit of node $i$'s instantaneous battery energy level $b_i(t)$ relative to the preset threshold $\delta_i$. Its update rule is defined as follows:
\begin{equation}
    \label{eq_virtual_energy_queue}
    B_i(t+1) = \max\{ B_i(t) + \delta_i - b_i(t+1), 0 \}
\end{equation}
where $b_i(t+1)$ represents the actual battery energy at the beginning of time slot $t+1$ after the decisions in time slot $t$ have been executed, calculated according to Eq.~\eqref{eq_b}. Intuitively, if $b_i(t+1)$ is below the preset threshold $\delta_i$, the energy deficit queue $B_i(t)$ will tend to increase, reflecting that the system state is deviating from the desired LTA energy maintenance target. According to Lyapunov optimization theory, the stability of the virtual queue $B_i(t)$ (i.e., its long-term average value is bounded) is a key condition for ensuring that the LTA constraint Eq.~\eqref{eq_lta_energy} is met~\cite{neely2010stochastic}.

Let $\mathbf{\Theta}(t) = (\mathbf{q}(t), \mathbf{B}(t))$ represent the combined state vector of all actual data queues $\mathbf{q}(t) = [q_{i,k}(t)]_{\forall i,k}$ and all virtual energy deficit queues $\mathbf{B}(t) = [B_i(t)]_{\forall i}$ in the network at the beginning of time slot $t$.

\subsection{Lyapunov Function and Drift}
To assess the overall queue backlog in the network, we define the following quadratic Lyapunov function $L(\mathbf{\Theta}(t))$:
\begin{equation}
    \label{eq_lyapunov_function}
    L(\mathbf{\Theta}(t)) = \frac{1}{2} \sum_{i \in \mathcal{N}} \sum_{k \in \mathcal{K}} q_{i,k}(t)^2 + \frac{1}{2} \sum_{i \in \mathcal{N}} B_i(t)^2
\end{equation}
It is worth noting that non-uniform weighting factors can be introduced for different data queues or energy deficit queues to achieve differentiated quality of service or priority control, but for simplicity of presentation, this paper uses uniform weights here.

The one-slot conditional Lyapunov drift, denoted as $\Delta L(\mathbf{\Theta}(t))$, is defined as the expected change in the Lyapunov function over one time slot, given the current system state $\mathbf{\Theta}(t)$:
\begin{equation}
    \label{eq_lyapunov_drift}
    \Delta L(\mathbf{\Theta}(t)) = E[ L(\mathbf{\Theta}(t+1)) - L(\mathbf{\Theta}(t)) | \mathbf{\Theta}(t) ]
\end{equation}
A core objective of Lyapunov optimization is to minimize this drift through control decisions, thereby pushing the system towards a state with lower queue backlogs and thus achieving queue stability.

\subsection{Drift-Plus-Penalty and Problem Transformation}
To simultaneously optimize the LTA throughput (Eq.~\eqref{eq_lta_throughput}) and indirectly satisfy LTA constraints through queue stability, this paper employs the drift-plus-penalty method~\cite{neely2010stochastic}. This method aims to minimize a combined objective in each time slot, which includes the Lyapunov drift and the negative weighted expected throughput, thereby decomposing the original LTA optimization problem P1' into a series of per-slot optimization subproblems.

Specifically, the drift-plus-penalty method seeks to minimize the following expression $Y(t)$ in each time slot $t$:
\begin{equation}
    \label{eq_drift_plus_penalty_func}
    Y(t) := \Delta L(\mathbf{\Theta}(t)) - V \cdot E[\text{DeliveredData}(t) | \mathbf{\Theta}(t)]
\end{equation}
where $\text{DeliveredData}(t) = \sum_{k \in \mathcal{K}} \sum_{l=(i,k) \in \mathcal{I}_k} f_{l,k}^t$ represents the total amount of data successfully delivered to all sink nodes in time slot $t$. The parameter $V \ge 0$ is an important non-negative control factor that tunes the trade-off between minimizing Lyapunov drift (i.e., pursuing queue stability) and maximizing the expected throughput $E[\text{DeliveredData}(t)]$. Choosing a larger value of $V$ implies a greater emphasis on improving throughput. By minimizing $Y(t)$, the control policy attempts to guide the system towards states with low queue backlogs while pursuing the maximization of the target utility (throughput).

By minimizing $Y(t)$ in each time slot, the data queue stability constraint and the LTA battery energy constraint (manifested through the stability of the virtual energy deficit queue $B_i(t)$) in the original problem P1' can be effectively addressed. Therefore, the original problem P1' can be approximately transformed into solving the following optimization problem P2 in each time slot $t$:
\begin{equation}
    \label{eq_P2}
    \begin{array}{rrl}
        \textbf{(P2)} &\min\limits_{\textbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t} & Y(t) \\
        & \text{s.t.}& \text{Instantaneous constraints (Eqs}.~\eqref{eq_s}-\eqref{eq_y})
    \end{array}
\end{equation}
However, the drift term $\Delta L(\mathbf{\Theta}(t))$ in the objective function $Y(t)$ of problem P2 depends on the system state in the next time slot $\mathbf{\Theta}(t+1)$ (via $L(\mathbf{\Theta}(t+1))$), which makes it difficult to solve P2 directly for online decision-making. To overcome this challenge, an upper bound for $Y(t)$ will be derived subsequently, which depends only on variables and decisions of the current time slot.

First, we derive an upper bound for the one-slot conditional Lyapunov drift $\Delta L(\mathbf{\Theta}(t))$. According to the definition of the Lyapunov function (Eq.~\eqref{eq_lyapunov_function}), this drift can be naturally decomposed into a drift component from the actual data queues, $\Delta L_Q(t)$, and a drift component from the virtual energy deficit queues, $\Delta L_B(t)$:
\begin{equation}
    \Delta L(\mathbf{\Theta}(t)) = \Delta L_Q(t) + \Delta L_B(t)
\end{equation}
\begin{align}
    \Delta L_Q(t) &= E\left[\frac{1}{2} \sum_{i,k} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) \bigg| \mathbf{\Theta}(t) \right] \\
    \Delta L_B(t) &= E\left[\frac{1}{2} \sum_i (B_i(t+1)^2 - B_i(t)^2) \bigg| \mathbf{\Theta}(t) \right]
\end{align}

\subsubsection{Data Queue Drift Upper Bound \texorpdfstring{$\Delta L_Q(t)$}{Delta LQ(t)}}
Consider the drift of a single data queue $q_{i,k}(t)$. Its queue dynamics follow Eq.~\eqref{eq_q}. To apply subsequent Lyapunov analysis tools and consider the non-negativity of queue lengths, its equivalent dynamics can be expressed as $q_{i,k}(t+1) = \max\{0, q_{i,k}(t) - \text{Outflow}_{i,k}^t \} + A_{i,k}(t)$, where $A_{i,k}(t) = a_{i,k}^t + \sum_{l'=(j,i) \in \mathcal{I}_i} f_{l',k}^t$ represents the total amount of data arriving at node $i$ for sink $k$ in time slot $t$ (including newly generated and relayed data from other nodes), and $\text{Outflow}_{i,k}^t = \sum_{l=(i,j) \in \mathcal{O}_i} f_{l,k}^t$ represents the total amount of data sent from node $i$ for sink $k$.
According to a standard result in Lyapunov optimization theory (see~\cite[Lemma~4.2]{neely2010stochastic}), for any non-negative real numbers $q, A, D$, the following inequality holds:
\begin{equation*}
    \frac{1}{2} [(\max\{0, q-D\} + A)^2 - q^2] \le \frac{1}{2} (A^2 + D^2) + q(A-D)
\end{equation*}
Applying this inequality to the data queue $q_{i,k}(t)$, let $q=q_{i,k}(t)$, $A=A_{i,k}(t)$, and $D=\text{Outflow}_{i,k}^t$, we get:
\begin{align}
    \frac{1}{2} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) &\le \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \nonumber \\
    &\quad + q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t)
\end{align}
Summing over all data queues $(i,k)$ and taking the conditional expectation with respect to the current state $\mathbf{\Theta}(t)$, we obtain an upper bound for $\Delta L_Q(t)$:
\begin{align}
    \Delta L_Q(t) &= E\left[\frac{1}{2} \sum_{i,k} (q_{i,k}(t+1)^2 - q_{i,k}(t)^2) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
    &\le E\left[ \sum_{i,k} \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
    &\quad + E\left[ \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \bigg| \mathbf{\Theta}(t) \right] \label{eq_delta_lq_step1_en}
\end{align}
Assume that, given the current state $\mathbf{\Theta}(t)$, the conditional second moments of the total inflow $A_{i,k}(t)$ and total outflow $\text{Outflow}_{i,k}^t$ are uniformly bounded, i.e., there exist finite constants $C_{A^2}$ and $C_{D^2}$ such that for all $i,k,t$, $E[A_{i,k}(t)^2 | \mathbf{\Theta}(t)] \le C_{A^2}$ and $E[(\text{Outflow}_{i,k}^t)^2 | \mathbf{\Theta}(t)] \le C_{D^2}$. This assumption is generally reasonable in practical networks, as data arrival rates, link capacities, and node transmission powers are subject to physical or protocol limitations. Based on this assumption, the first term on the right-hand side of Eq.~\eqref{eq_delta_lq_step1_en} (i.e., the sum of expected squared terms) can be bounded by a positive constant $C_q$:
\begin{multline*}
     E\left[ \sum_{i,k} \frac{1}{2} (A_{i,k}(t)^2 + (\text{Outflow}_{i,k}^t)^2) \bigg| \mathbf{\Theta}(t) \right] \\
     \le \sum_{i,k} \frac{1}{2} (C_{A^2} + C_{D^2}) =: C_q
\end{multline*}
Expanding and reorganizing the summation term in $E\left[ \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \bigg| \mathbf{\Theta}(t) \right]$, and considering that queues at sink nodes $k'$ are $q_{k',k}(t)=0$ (as data has reached its destination), we get:
\begin{align}
    & \sum_{i,k} q_{i,k}(t) (A_{i,k}(t) - \text{Outflow}_{i,k}^t) \nonumber \\
    &= \sum_{i,k} q_{i,k}(t) a_{i,k}^t + \sum_{i,k} q_{i,k}(t) \left( \sum_{l'=(j,i)} f_{l',k}^t - \sum_{l=(i,j)} f_{l,k}^t \right) \nonumber \\
    &= \sum_{i,k} q_{i,k}(t) a_{i,k}^t + \sum_{l=(i,j) \in \mathcal{E}} \sum_{k \in \mathcal{K}} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t
\end{align}
Therefore, the upper bound for the data queue drift component $\Delta L_Q(t)$ can be expressed as:
\begin{align}
    \Delta L_Q(t) \le C_q &+ \sum_{i,k} q_{i,k}(t) E[a_{i,k}^t | \mathbf{\Theta}(t)] \nonumber \\
    &+ E\left[ \sum_{l=(i,j) \in \mathcal{E}} \sum_{k \in \mathcal{K}} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \bigg| \mathbf{\Theta}(t) \right] \label{eq_delta_lq_bound_en}
\end{align}

\subsubsection{Energy Deficit Queue Drift Upper Bound \texorpdfstring{$\Delta L_B(t)$}{Delta LB(t)}}
Next, we analyze the drift of the virtual energy deficit queue $B_i(t)$. Its update rule is given by Eq.~\eqref{eq_virtual_energy_queue}: $B_i(t+1) = \max\{ B_i(t) + \delta_i - b_i(t+1), 0 \}$.
To derive the drift of its quadratic term, we use the property that $(\max\{0, z\})^2 \le z^2$ holds for any real number $z$. Let $z = B_i(t) + \delta_i - b_i(t+1)$, then:
\begin{align}
    B_i(t+1)^2 &= (\max\{0, B_i(t) + \delta_i - b_i(t+1)\})^2 \nonumber \\
               &\le (B_i(t) + \delta_i - b_i(t+1))^2 \nonumber \\
               &= B_i(t)^2 + (\delta_i - b_i(t+1))^2 + 2 B_i(t) (\delta_i - b_i(t+1))
\end{align}
Rearranging and dividing by 2, the quadratic drift term for a single energy deficit queue satisfies:
\begin{equation}
    \label{eq_energy_drift_base_en}
    \frac{1}{2} (B_i(t+1)^2 - B_i(t)^2) \le \frac{1}{2} (\delta_i - b_i(t+1))^2 + B_i(t) (\delta_i - b_i(t+1))
\end{equation}
Considering that the actual battery energy $b_i(t+1)$ ranges between $[0, b_{\max}]$ and the energy threshold $\delta_i$ is a constant, $(\delta_i - b_i(t+1))^2$ is bounded. Summing over all nodes $i$ and taking the conditional expectation with respect to the current state $\mathbf{\Theta}(t)$, the first term on the right-hand side of Eq.~\eqref{eq_energy_drift_base_en} can be bounded by a positive constant $C_b$: $E\left[\sum_i \frac{1}{2} (\delta_i - b_i(t+1))^2 \bigg| \mathbf{\Theta}(t)\right] \le C_b$. Thus, the total energy deficit queue drift $\Delta L_B(t)$ satisfies:
\begin{equation}
    \label{eq_delta_lb_step1_en}
    \Delta L_B(t) \le C_b + E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right]
\end{equation}
Next, we focus on the second term in Eq.~\eqref{eq_delta_lb_step1_en}, i.e., $E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right]$. Substituting the battery energy dynamics from Eq.~\eqref{eq_b} (i.e., $b_i(t+1) = b_i(t) + \bar{e}_i^t - \text{Cons}_i^t$), where $\text{Cons}_i^t = s_i^t + \hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t$ is the total energy consumption of node $i$ in time slot $t$, and $\bar{e}_i^t$ is the net energy gain of node $i$ in time slot $t$ (limited by environmental harvesting $h_i^t$, RF energy harvesting $\check{e}_i^t$, and battery capacity $b_{\max}$, i.e., $\bar{e}_i^t = \min(\check{e}_i^t + h_i^t, b_{\max} - (b_i(t) - \text{Cons}_i^t))$).
Thus, we have $\delta_i - b_i(t+1) = \delta_i - (b_i(t) + \bar{e}_i^t - \text{Cons}_i^t) = \delta_i - b_i(t) - \bar{e}_i^t + \text{Cons}_i^t$.

Directly handling the $\min$ function in $\bar{e}_i^t = \min(\check{e}_i^t + h_i^t, b_{\max} - (b_i(t) - \text{Cons}_i^t))$ in the drift analysis leads to an overly complex expression. To simplify the derivation and obtain an objective function that is structurally more amenable to online optimization, we adopt a common approximation used in Lyapunov optimization literature when dealing with finite-capacity resources like batteries: **we temporarily ignore the saturation effect of the battery capacity upper bound $b_{\max}$ on the energy storage process when deriving the drift upper bound.** Specifically, we assume that the actual energy gain $\bar{e}_i^t$ is approximately equal to the total potential energy harvested, i.e., $\bar{e}_i^t \approx \check{e}_i^t + h_i^t$. This approximation is reasonable in scenarios where the battery does not frequently reach its capacity limit (i.e., energy overflow is infrequent), and it preserves the core weighting relationship of energy consumption and harvesting on the energy deficit queue.
Based on this approximation, we continue to derive $E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right]$:
$\delta_i - b_i(t+1) \approx \delta_i - b_i(t) - (\check{e}_i^t + h_i^t) + \text{Cons}_i^t$.
Since $B_i(t) \ge 0$, multiplying it by $(\delta_i - b_i(t+1))$, summing over all nodes, and taking the conditional expectation yields:
\begin{align}
    & E\left[ \sum_i B_i(t) (\delta_i - b_i(t+1)) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
    &\approx E\left[ \sum_i B_i(t) (\delta_i - b_i(t) - \check{e}_i^t - h_i^t + \text{Cons}_i^t) \bigg| \mathbf{\Theta}(t) \right] \nonumber \\
    &= \sum_i B_i(t) (\delta_i - b_i(t)) - E\left[\sum_i B_i(t) \check{e}_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
    &- E\left[\sum_i B_i(t) h_i^t \bigg| \mathbf{\Theta}(t)\right] + E\left[\sum_i B_i(t) \text{Cons}_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
    &= \sum_i B_i(t) (\delta_i - b_i(t)) - E\left[\sum_i B_i(t) \check{e}_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
    &- E\left[\sum_i B_i(t) h_i^t \bigg| \mathbf{\Theta}(t)\right] + E\left[\sum_i B_i(t) s_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
    & + E\left[ \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t) \bigg| \mathbf{\Theta}(t) \right] \label{eq_energy_term_approx_en}
\end{align}
Substituting the result of Eq.~\eqref{eq_energy_term_approx_en} back into Eq.~\eqref{eq_delta_lb_step1_en}, we obtain an approximate upper bound for the energy deficit queue drift $\Delta L_B(t)$:
\begin{align}
    \Delta L_B(t) \lesssim{}& C_b + \sum_i B_i(t) (\delta_i - b_i(t)) \nonumber \\
    & + E\left[\sum_i B_i(t) s_i^t \bigg| \mathbf{\Theta}(t)\right] - E\left[\sum_i B_i(t) h_i^t \bigg| \mathbf{\Theta}(t)\right] \nonumber \\
    & + E\left[ \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \bigg| \mathbf{\Theta}(t) \right] \label{eq_delta_lb_bound_en}
\end{align}
where the symbol $\lesssim$ emphasizes that this upper bound is obtained based on the aforementioned approximation of ignoring the battery capacity saturation effect.
\begin{remark}[Impact of Battery Saturation Effect Approximation]
The derivation of the energy deficit queue drift upper bound in Eq.~\eqref{eq_delta_lb_bound_en} relies on the approximation of ignoring the saturation effect of the battery capacity upper bound $b_{\max}$. While this approximation significantly simplifies the theoretical analysis and preserves the key structure of costs and benefits weighted by the energy deficit queue $B_i(t)$ in the optimization objective $W^*(t)$ (Eq.~\eqref{eq_W_star_final_en}), this upper bound may not be tight in scenarios where the battery frequently saturates. This imprecision might have some impact on the actual performance of algorithms designed based on this bound under these extreme conditions. However, the Lyapunov optimization framework typically exhibits good robustness and can tolerate such model mismatches to some extent, especially when the control parameter $V$ is large, as the overall system performance will be more dominated by the throughput maximization term driven by $V$.
\end{remark}

\subsubsection{Total Drift Upper Bound and Per-Slot Optimization}
Combining the upper bounds Eq.~\eqref{eq_delta_lq_bound_en} and Eq.~\eqref{eq_delta_lb_bound_en}, the upper bound for the total drift $\Delta L(\Theta(t)) = \Delta L_Q(t) + \Delta L_B(t)$ is:
\begin{align}
    \Delta L(\Theta(t)) \le C &+ \sum_{i,k} q_{i,k}(t) E[a_{i,k}^t | \Theta(t)] + \sum_i B_i(t) (\delta_i - b_i(t)) \nonumber \\
    &+ E\left[\sum_i B_i(t) s_i^t \bigg| \Theta(t)\right] - E\left[\sum_i B_i(t) h_i^t \bigg| \Theta(t)\right] \nonumber \\
    &+ E \bigg[ \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
    & \quad + \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \bigg| \Theta(t) \bigg] \label{eq_drift_upper_bound_detailed_en}
\end{align}
where $C = C_q + C_b$ is a constant.

Substituting the drift upper bound Eq.~\eqref{eq_drift_upper_bound_detailed_en} into the drift-plus-penalty function Eq.~\eqref{eq_drift_plus_penalty_func}, we obtain an upper bound for $Y(t)$:
\begin{align}
    Y(t) \le C'(\Theta(t)) &+ E \bigg[ \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
    & \quad + \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \nonumber \\
    & \quad - V \sum_{k, l=(i,k)} f_{l,k}^t \bigg| \Theta(t) \bigg] \label{eq_Y_upper_bound_en}
\end{align}
where $C'(\Theta(t))$ includes the constant $C = C_q + C_b$ and terms involving $q_{i,k}(t)$ and $B_i(t)$, which are weighted by the expected arrival $E[a_{i,k}^t | \Theta(t)]$, sensing cost $E[s_i^t | \Theta(t)]$, environmental energy harvesting $E[h_i^t | \Theta(t)]$, and current battery level $b_i(t)$. Crucially, $C'(\Theta(t))$ does not depend on the control decisions $(\mathbf{x}^t, \mathbf{y}^t, \mathbf{p}^t, \mathbf{e}^t)$ made in the current time slot $t$.

The Lyapunov optimization approach aims to minimize the upper bound of $Y(t)$ given in Eq.~\eqref{eq_Y_upper_bound_en}, instead of directly minimizing the exact $Y(t)$ (Problem P2), which is difficult to solve directly due to its dependence on expectations and future states. Since $C'(\Theta(t))$ is independent of the current control actions, minimizing the upper bound is equivalent to minimizing the expectation term in the square brackets. Minimizing this term is equivalent to maximizing its negative. Thus, we arrive at the per-slot optimization problem P3:
\begin{equation}
    \label{eq_P3_en}
    \begin{array}{rrl}
        \textbf{(P3)} &\max\limits_{\textbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t} & W^*(t) \\
        & \text{s.t.}& \text{Instantaneous constraints (Eqs}.~\eqref{eq_s}-\eqref{eq_y})
    \end{array}
\end{equation}
where the objective function $W^*(t)$ is defined as:
\begin{align}
    W^*(t) := & V \sum_{k, l=(i,k)} f_{l,k}^t - \sum_{l=(i,j)} \sum_{k} (q_{j,k}(t) - q_{i,k}(t)) f_{l,k}^t \nonumber \\
    & - \sum_i B_i(t) (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t - \check{e}_i^t) \label{eq_maximize_target_W_detailed_en}
\end{align}
Rearranging the terms gives the form used in the algorithm design:
\begin{align}
    W^*(t) = & \underbrace{\sum_{l=(i,j) \in E} \sum_{k \in K} (q_{i,k}(t) - q_{j,k}(t)) f_{l,k}^t + V \sum_{k, l=(i,k)} f_{l,k}^t}_{\text{Term 1: Weighted Data Transmission Benefit}} \nonumber \\
    & + \underbrace{\sum_{i \in N} B_i(t) (\check{e}_i^t - (\hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t))}_{\text{Term 2: Weighted Net Energy Gain (excluding environmental)}} \label{eq_W_star_final_en}
\end{align}
Problem P3 is a deterministic optimization problem that needs to be solved in each time slot $t$ based only on the observed current state $\Theta(t)$, current battery levels $b(t)$, and current channel conditions $g^t$. The online algorithm Lyapunov-MEC, proposed in the next section, aims to solve (or approximately solve) P3 in each time slot.

\section{Lyapunov-Based MEC Algorithm (Lyapunov-MEC)}
\label{sec_lyapunov_algo}
Directly solving Problem P3 in each time slot, i.e., maximizing the objective function $W^*(t)$ in Eq.~\eqref{eq_W_star_final_en}, is a highly challenging mixed-integer nonlinear programming problem. Its complexity primarily stems from: (1) the tight coupling between discrete link activation decisions ($\mathbf{x}^t$) and energy cooperation mode decisions ($\mathbf{y}^t$) with continuous power allocation decisions ($\mathbf{p}^t, \mathbf{e}^t$); and (2) the nonlinear dependence of the energy harvesting process ($\check{e}_i^t$) and data transmission rates ($r_l^t$) on control decisions. Considering the computational feasibility requirements for online implementation, this paper proposes a low-complexity heuristic online algorithm, termed Lyapunov-MEC. This algorithm aims to approximately maximize the objective function in each time slot through a series of greedy decisions based on the structure of $W^*(t)$, while strictly satisfying all instantaneous constraints.

The Lyapunov-MEC algorithm operates independently in each time slot $t$, and its core decision process is divided into two main phases: first determining energy cooperation-related actions, and then determining data transmission-related actions.

\subsection{Algorithm Overview}
At the beginning of each time slot $t$, the Lyapunov-MEC algorithm first observes the current system state, including the combined queue vector $\mathbf{\Theta}(t) = (\mathbf{q}(t), \mathbf{B}(t))$, the battery energy levels of all nodes $\mathbf{b}(t)$, and the instantaneous channel gains of all links $\mathbf{g}^t$. Based on these observations, the algorithm executes decisions sequentially in the following two main phases:

\subsubsection{Phase 1: Energy Cooperation Decision \texorpdfstring{$(\mathbf{y}^t, \mathbf{e}^t)$}{(y\textasciicircum t, e\textasciicircum t)}}
This phase aims to determine the nodes participating in energy transmission ($y_i^t=1$) and their corresponding transmission powers ($\hat{e}_i^t$). These decisions primarily affect the weighted net energy gain term in the objective function $W^*(t)$ (Eq.~\eqref{eq_W_star_final_en}) and are made heuristically:
\begin{enumerate}
    \item \textbf{Identify Potential Senders:} A node $i$ is identified as a potential energy sender if it satisfies the energy condition $b_i(t) > \alpha \cdot b_{\max}$ and the energy deficit condition $B_i(t) < \beta'$. Here, $\alpha \in (0, 1]$ and $\beta' \ge 0$ are input parameters to the algorithm.
    \item \textbf{Identify Potential Receivers:} A node $j \in \mathcal{N}_i$ is identified as a potential energy receiver if it satisfies the energy condition $b_j(t) < \gamma \cdot b_{\max}$ and the energy deficit condition $B_j(t) > \zeta'$. Here, $\gamma \in [0, 1)$ and $\zeta' \ge 0$ are input parameters.
    \item \textbf{Estimate Gain:} For each potential sender $i$, estimate the marginal gain in $W^*(t)$ from transmitting energy at a trial power $e_{\text{trial}} \in [e_{\min}, e_{\max}]$. This gain is calculated as:
    \begin{equation}
        \Delta W_i^{\text{EC}}(e_{\text{trial}}) = \sum_{j \in \mathcal{N}_i, y_j^t=0} B_j(t) \Delta \check{e}_j^t(e_{\text{trial}}) - B_i(t) e_{\text{trial}}
        \label{eq_ec_gain_est_en}
    \end{equation}
    where $\Delta \check{e}_j^t(e_{\text{trial}})$ represents the expected additional RF energy harvested by neighbor node $j$ due to node $i$'s energy transmission at trial power $e_{\text{trial}}$. This increment is estimated based on a simplified model that ignores interference from concurrent transmissions: assume only node $i$ transmits energy at power $e_{\text{trial}}$, thereby calculating the RF power $Rf_j^{\text{est}} = e_{\text{trial}} g_{i,j}^t$ received by node $j$; the corresponding harvested energy $\check{e}_j^{\text{est}} = \text{EH\_Model}(Rf_j^{\text{est}})$ is then calculated using the nonlinear RF energy harvesting model (Eq.~\eqref{eq_EH1}). Thus, the energy increment is set to $\Delta \check{e}_j^t(e_{\text{trial}}) = \check{e}_j^{\text{est}}$, which implicitly assumes a baseline harvested amount (i.e., without this specific transmission) of zero. This estimation method does not account for interference effects from other potential concurrent energy or data transmissions in the network.
    \item \textbf{Iterative Greedy Selection:} An iterative approach is used to select energy senders. In each iteration, select the node $i^*$ from the unselected potential senders that provides the maximum positive gain $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*)$ and satisfies the energy causality $e_{\text{trial}}^* \le b_{i^*}^t$. If such an $i^*$ exists and $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*) > 0$, set $y_{i^*}^t = 1$ and $\hat{e}_{i^*}^t = e_{\text{trial}}^*$, and remove this node from the set of potential senders for this phase. This phase only determines the energy transmission decisions ($\mathbf{y}^t, \mathbf{e}^t$) and does not restrict participation in subsequent data transmission decisions. This iterative process is repeated until no potential sender can provide a positive gain.
    \item Nodes not selected as senders are potential receivers ($y_i^t=0$).
\end{enumerate}

\subsubsection{Phase 2: Data Transmission Decision \texorpdfstring{$(\mathbf{x}^t, \mathbf{p}^t, \mathbf{f}^t)$}{(x\textasciicircum t, p\textasciicircum t, f\textasciicircum t)}}
After determining the energy cooperation decisions $(\mathbf{y}^t, \mathbf{e}^t)$, this phase aims to select active data transmission links ($x_{l,k}^t=1$), allocate transmission powers ($p_l^t$), and determine data flows ($f_{l,k}^t$) to approximately maximize $W^*(t)$ (Eq.~\eqref{eq_W_star_final_en}). This phase allows all nodes (including energy senders from Phase 1) to participate in data transmission decisions, subject to half-duplex and combined energy constraints.
\begin{enumerate}
    \item \textbf{Calculate Link Weights:} For each potential link $l=(i,j) \in \mathcal{E}$ with data to transmit (i.e., $\exists k, q_{i,k}(t)>0$), calculate its basic data transmission benefit weight $W_{l,k}$, which reflects the contribution of transmitting unit data to reducing queue backlog and increasing throughput:
    \begin{equation}
        W_{l,k} = q_{i,k}(t) - q_{j,k}(t) + V \cdot \mathbb{I}(j=k)
        \label{eq_link_weight_en}
    \end{equation}
    where $\mathbb{I}(j=k)$ is an indicator function that is 1 if the receiving node $j$ is the final sink $k$, and 0 otherwise.
    \item \textbf{Calculate Net Weights:} Activating link $l=(i,j)$ not only brings data transmission benefits quantified by $W_{l,k}$ but also incurs energy costs, including the energy consumed for data transmission itself $\hat{p}_i^t = p_l^t$ and energy consumed for data reception $\check{p}_j^t = p_{\text{rcv}} f_{l,k}^t$. These costs are weighted by the energy deficit queues $B_i(t)$ and $B_j(t)$ of the sender and receiver. To comprehensively evaluate the value of activating a link, we calculate its net weight $\text{NetWeight}_{l,k}(p_l^t)$:
\begin{equation}
\label{eq_net_link_weight_en}
\begin{split}
    \text{NetWeight}_{l,k}(p_l^t) ={}& (W_{l,k}) f_{l,k}^t(p_l^t) \\
                                    & - B_i(t) p_l^t - B_j(t) p_{\text{rcv}} f_{l,k}^t(p_l^t)
\end{split}
\end{equation}
    where $f_{l,k}^t(p_l^t)$ is the actual data flow from node $i$ to sink $k$ over link $l$ given transmission power $p_l^t$. This data flow is limited by the link capacity $r_l^t(p_l^t)$ (Eq.~\eqref{eq_r}), sender's queue $q_{i,k}^t$, receiver $j$'s remaining available reception energy (after deducting its own energy transmission cost $\hat{e}_j^t$), and available buffer space. It is calculated as follows (assuming $p_l^t$ is determined):
\begin{equation}
\label{eq_f_l_k_calc_en}
\begin{split}
    f_{l,k}^t(p_l^t) = \min\biggl(& r_l^t(p_l^t), q_{i,k}^t, \\
                                 & \lfloor (b_j^t - \hat{e}_j^t) / p_{\text{rcv}} \rfloor, q_{\max} - \sum_{k' \in \mathcal{K}} q_{j,k'}^t \biggr)
\end{split}
\end{equation}
    Note that when calculating $f_{l,k}^t$, it must be ensured that $\lfloor (b_j^t - \hat{e}_j^t) / p_{\text{rcv}} \rfloor \ge 0$.
    \item \textbf{Iterative Greedy Link Selection:} An iterative approach is used to select active data links.
        a. Initialize a set $\mathcal{N}_{\text{data\_busy}} = \emptyset$ to record nodes that have participated in data transmission (sending or receiving) in this time slot. Identify the set of all potential links with data to transmit $\mathcal{L}_{\text{pot}} = \{l=(i,j) \in \mathcal{E} | \exists k, q_{i,k}(t)>0\}$.
        b. Perform a preliminary evaluation for each potential link $l=(i,j) \in \mathcal{L}_{\text{pot}}$ and associated sink $k$. Obtain the energy transmission powers $\hat{e}_i^t, \hat{e}_j^t$ for nodes $i, j$ determined in Phase 1. Calculate the minimum transmission power $p_{\text{required\_min\_W}}$ required to meet the minimum SNR threshold $\gamma_{\min}$ (based on Eq.~\eqref{eq_p_f} and considering $p_{\min}, p_{\max}$ constraints). If $p_{\text{required\_min\_W}}$ is infeasible (e.g., out of range or channel is too poor), ignore this link. Verify the sender $i$'s combined energy constraint: $p_{\text{required\_min\_W}} + \hat{e}_i^t \le b_i^t$. If satisfied, calculate the potential data flow $f_{\text{potential}}$ using this power $p_{\text{trial}} = p_{\text{required\_min\_W}}$ (according to Eq.~\eqref{eq_f_l_k_calc_en}). Further verify the receiver $j$'s combined energy constraint: $p_{\text{rcv}} f_{\text{potential}} + \hat{e}_j^t \le b_j^t$. If all checks pass and $f_{\text{potential}}>0$, calculate the corresponding net weight $\text{NetWeight}_{l,k}(p_{\text{required\_min\_W}})$ (Eq.~\eqref{eq_net_link_weight_en}), and add this (link, sink) pair, its net weight, potential flow, and required power to a candidate list $Pairs$.
        c. Sort the $Pairs$ list in descending order of net weight $\text{NetWeight}_{l,k}(p_{\text{required\_min\_W}})$.
        d. Iterate through each candidate pair $((l^*, k^*), \text{Weight}, f_{\text{potential}}, p_{\text{req}})$ in the sorted $Pairs$ list, where $l^*=(i^*, j^*)$:
        e. \textbf{Check Data Half-Duplex Constraint:} If neither sender $i^*$ nor receiver $j^*$ is in $\mathcal{N}_{\text{data\_busy}}$.
        f. \textbf{Determine Final Power and Flow:} Use the required power $p_{l^*}^t = p_{\text{req}}$ and potential flow $f_{l^*,k^*}^t = f_{\text{potential}}$ stored in the candidate pair. Obtain $\hat{e}_{i^*}^t$ and $\hat{e}_{j^*}^t$.
        g. \textbf{Final Combined Energy Check:} Reconfirm that sender energy $p_{l^*}^t + \hat{e}_{i^*}^t \le b_{i^*}^t$ and receiver energy $p_{\text{rcv}} f_{l^*,k^*}^t + \hat{e}_{j^*}^t \le b_{j^*}^t$ are satisfied.
        h. \textbf{Activate Link:} If both half-duplex and energy constraints are met, and the pre-calculated net weight $\text{Weight} > 0$, activate the link: set $x_{l^*,k^*}^t = 1$, record $p_{l^*}^t = p_{\text{req}}$ and actual flow $f_{l^*,k^*}^t = f_{\text{potential}}$.
        i. \textbf{Mark Nodes Data Busy:} If the link is activated, add nodes $i^*$ and $j^*$ to the $\mathcal{N}_{\text{data\_busy}}$ set.
        j. Continue to the next candidate pair in the sorted list until the list is exhausted.
\end{enumerate}

\subsection{Algorithm Pseudocode}
The entire process is divided into two phases, summarized in Algorithm~\ref{alg_lyapunov_mec_phase1_en} and Algorithm~\ref{alg_lyapunov_mec_phase2_en}, respectively.

% Algorithm 1: Phase 1
\begin{algorithm}
\linespread{1.1}\selectfont
    \caption{Lyapunov-MEC Algorithm: Phase 1 (Energy Cooperation)}
    \label{alg_lyapunov_mec_phase1_en}
    \KwIn{Current state $\Theta(t)=(\mathbf{q}(t), \mathbf{B}(t))$, $\mathbf{b}(t)$, $\mathbf{g}^t$; Energy thresholds $\alpha, \beta'$; Power bounds $e_{\min}, e_{\max}$;}
    \KwOut{Energy decisions $\mathbf{y}^t, \mathbf{e}^t$}
    Initialize $\mathbf{y}^t=\mathbf{0}, \mathbf{e}^t=\mathbf{0}$ \\
    Initialize $\mathbb{S}_E^t = \emptyset$ \\
    Initialize $\mathcal{N}_{\text{avail}}^{\text{energy}} = \mathcal{N}$ \\

    \tcp{Iterative Energy Cooperation Decision}
    \While{true}{
        Find potential sender $i^*$ and power $e_{\text{trial}}^* \in [e_{\min}, \min(e_{\max}, b_{i^*}^t)]$ that maximizes $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*)$ among $i^* \in \mathcal{N}_{\text{avail}}^{\text{energy}}$ such that: \\
        \quad a) $i^*$ satisfies sender conditions: $b_{i^*}(t) > \alpha \cdot b_{\max}$ and $B_{i^*}(t) < \beta'$ \\
        \quad b) Gain is positive: $\Delta W_{i^*}^{\text{EC}}(e_{\text{trial}}^*) > 0$, calculated using Eq.~\eqref{eq_ec_gain_est_en} (simplified $\Delta \check{e}_j^t$) \\
        
        \If{such an $i^*$ exists}{
            Set $y_{i^*}^t = 1$, $\hat{e}_{i^*}^t = e_{\text{trial}}^*$ \\
            $\mathbb{S}_E^t = \mathbb{S}_E^t \cup \{i^*\}$ \\
            $\mathcal{N}_{\text{avail}}^{\text{energy}} = \mathcal{N}_{\text{avail}}^{\text{energy}} \setminus \{i^*\}$
        }
        \Else{
            Break
        }
    }
\end{algorithm}

% Algorithm 2: Phase 2 and State Update 
\begin{algorithm}
\linespread{1.1}\selectfont
    \caption{Lyapunov-MEC Algorithm: Phase 2 (Data Transmission) and State Update}
    \label{alg_lyapunov_mec_phase2_en}
    \KwIn{Current state $\Theta(t)=(\mathbf{q}(t), \mathbf{B}(t))$, $\mathbf{b}(t)$, $\mathbf{g}^t$; Phase 1 outputs $\mathbf{y}^t, \mathbf{e}^t$; Parameters $V, p_{\min}, p_{\max}, \gamma_{\min}, \sigma^2, W, p_{\text{rcv}}, q_{\max}, b_{\max}$; Thresholds $\delta_i$}
    \KwOut{Updated state $\Theta(t+1), \mathbf{b}(t+1)$; Data decisions $\mathbf{x}^t, \mathbf{p}^t, \mathbf{f}^t$}
    Initialize $\mathbf{x}^t=\mathbf{0}, \mathbf{p}^t=\mathbf{0}, \mathbf{f}^t = \mathbf{0}, \mathbb{S}_D^t = \emptyset, \mathcal{N}_{\text{data\_busy}} = \emptyset$ \\

    Initialize list $CandidateLinks = []$ \\
    \For{each link $l=(i,j) \in \mathcal{E}$ and destination $k \in \mathcal{K}$ with $q_{i,k}(t)>0$}{
        Let $\hat{e}_i^t = \mathbf{e}^t[i]$ and $\hat{e}_j^t = \mathbf{e}^t[j]$ \\
        Calculate $p_{\text{required\_min\_W}}$ based on $g_l^t, \gamma_{\min}, \sigma^2, p_{\min}, p_{\max}$ \\
        \If{$p_{\text{required\_min\_W}} \neq \infty$ and $p_{\text{required\_min\_W}} + \hat{e}_i^t \le b_i^t$}{
             Calculate potential flow $f_{\text{potential}}$ using Eq.~\eqref{eq_f_l_k_calc_en} with $p_l^t = p_{\text{required\_min\_W}}$ and considering $\hat{e}_j^t$ \\
             \If{$p_{\text{rcv}} f_{\text{potential}} + \hat{e}_j^t \le b_j^t$ and $f_{\text{potential}} > 0$}{
                Calculate link weight $W_{l,k}$ using Eq.~\eqref{eq_link_weight_en} \\
                Calculate net weight $\text{NetWeight} = (W_{l,k}) f_{\text{potential}} - B_i(t) p_{\text{required\_min\_W}} - B_j(t) p_{\text{rcv}} f_{\text{potential}}$ \\
                Add tuple $((l, k), \text{NetWeight}, f_{\text{potential}}, p_{\text{required\_min\_W}})$ to $CandidateLinks$
             }
        }
    }

    Sort $CandidateLinks$ by $\text{NetWeight}$ in descending order \\
    \For{each ranked tuple $((l^*, k^*), \text{Weight}, f_{\text{potential}}, p_{\text{req}})$ in $CandidateLinks$ where $l^*=(i^*, j^*)$}{
        \If{$i^* \notin \mathcal{N}_{\text{data\_busy}}$ and $j^* \notin \mathcal{N}_{\text{data\_busy}}$}{
            \If{$\text{Weight} > 0$}{
                Set $x_{l^*,k^*}^t = 1$, $p_{l^*}^t = p_{\text{req}}$ \\
                Store $f_{l^*,k^*}^t = f_{\text{potential}}$ in the flow matrix $\mathbf{f}^t$ \\
                $\mathbb{S}_D^t = \mathbb{S}_D^t \cup \{l^*\}$ \\
                $\mathcal{N}_{\text{data\_busy}} = \mathcal{N}_{\text{data\_busy}} \cup \{i^*, j^*\}$
            }
        }
    }
    Calculate final $\hat{p}_i^t = \sum_{l \in \mathcal{O}_i \cap \mathbb{S}_D^t} p_l^t$ and $\check{p}_i^t = \sum_{l \in \mathcal{I}_i} \sum_{k' \in \mathcal{K}} p_{\text{rcv}} \cdot \mathbf{f}^t[l, k']$

    Calculate actual RF received power $Rf_i^{\text{eff},t}$ using Eq.~\eqref{eq_Rf_eff} based on final $\mathbf{y}^t, \mathbf{e}^t, \mathbf{p}^t$ \\
    Calculate actual energy harvested $\check{e}_i^t$ using Eq.~\eqref{eq_EH1} with $Rf_i^{\text{eff},t}$ \\
    Calculate total consumption $\text{Cons}_i^t = s_i^t + \hat{p}_i^t + \check{p}_i^t + \hat{e}_i^t$ \\
    Update $b_i(t+1) = \min(b_{\max}, b_i(t) - \text{Cons}_i^t + \check{e}_i^t + h_i^t)$ \\
    Update $q_{i,k}(t+1)$ using Eq.~\eqref{eq_q} with final $\mathbf{f}^t$ \\
    Update $B_i(t+1)$ using Eq.~\eqref{eq_virtual_energy_queue} with $b_i(t+1)$ \\
\end{algorithm}

\subsection{Complexity Analysis}
We analyze the computational complexity of executing the Lyapunov-MEC algorithm (Algorithms~\ref{alg_lyapunov_mec_phase1_en} and~\ref{alg_lyapunov_mec_phase2_en}) in each time slot. Let $d_{max}$ be the maximum degree of a node in the network.
\textbf{Algorithm~\ref{alg_lyapunov_mec_phase1_en} (Phase 1):} This phase involves an iterative loop that runs at most $N$ times. In each iteration, the algorithm searches for a potential sender that provides the maximum positive energy cooperation gain $\Delta W_i^{\text{EC}}$. This involves checking conditions for up to $N$ candidate nodes and calculating their gains. The calculation of a single gain requires iterating through the neighbors of the node (up to $d_{max}$), so its complexity is $O(d_{max})$. Therefore, the complexity of finding the best sender in a single iteration is $O(N \cdot d_{max})$, leading to a total complexity for Phase 1 of $O(N^2 \cdot d_{max})$.
\textbf{Algorithm~\ref{alg_lyapunov_mec_phase2_en} (Phase 2 + Update):} This phase first calculates initial net weights for all potential (link, sink) pairs (numbering $O(EK)$), which has a complexity of $O(EK)$. Subsequently, these candidate pairs are sorted, with a complexity of $O(EK \log(EK))$. The following iterative selection process runs at most $EK$ times, and the operations within each iteration (such as checking node availability, calculating data flow, updating state sets, etc.) have low complexity (generally considered $O(1)$). In the final state update step, calculating the actual RF energy harvested $\check{e}_i^t$ for each node takes $O(N \cdot d_{max})$; updating all data queues $q_{i,k}$ (according to Eq.~\eqref{eq_q}) involves iterating through incoming and outgoing links for each node, with a total complexity of $O(NK d_{\max})$; updating energy deficit queues $B_i$ has a complexity of $O(N)$. Thus, the total complexity of Phase 2 and state update is dominated by sorting and queue updates, and can be expressed as $O(EK \log(EK) + NK d_{\max})$.
\textbf{Overall Complexity:} The total computational complexity of the Lyapunov-MEC algorithm per time slot is the sum of the complexities of these two phases, i.e., $O(N^2 \cdot d_{max} + EK \log(EK) + NK d_{\max})$.

The Lyapunov-MEC algorithm provides an online control policy that dynamically adapts to network conditions (queue backlogs, energy levels, channel states), aiming to optimize LTA throughput while managing energy constraints. As a heuristic approximation algorithm designed to balance performance and computational complexity, its actual performance relative to the theoretical optimal policy (analyzed in the next section) needs to be evaluated through simulations.

\section{Theoretical Analysis of Lyapunov-MEC}
\label{sec_analysis_en}
This section is dedicated to establishing theoretical performance guarantees for the proposed Lyapunov-MEC algorithm (whose specific procedure is jointly defined by Algorithm~\ref{alg_lyapunov_mec_phase1_en} and Algorithm~\ref{alg_lyapunov_mec_phase2_en}). Specifically, we will prove that the algorithm can ensure the stability of all network queues (including actual data queues and virtual energy deficit queues) and analyze the relationship between its achieved long-term average (LTA) throughput and the theoretical optimum. The analysis follows standard methods in Lyapunov optimization~\cite{neely2010stochastic}.

\subsection{Queue Stability Guarantee}
This subsection aims to establish the stability of all relevant queues under the Lyapunov-MEC algorithm. Queue stability is a core prerequisite for ensuring bounded data latency and satisfying LTA energy constraints.

\begin{lemma}[Queue Stability]
    \label{lemma_queue_stability_en}
    Assume the average data arrival rate vector $\lambda = (E[a_{i,k}^t])$ is strictly within the stability region $\Lambda$ of the network. The stability region $\Lambda$ is the set of all arrival rate vectors for which there exists some stationary randomized policy $\pi^*$ that can satisfy all network constraints and stabilize all queues. For any control policy (executed with parameter $V > 0$) that makes decisions $(\mathbf{x}^t,\textbf{y}^t,\textbf{p}^t,\textbf{e}^t)$ in each time slot $t$ to minimize the drift-plus-penalty upper bound in Eq.~\eqref{eq_Y_upper_bound_en}, all data queues $q_{i,k}(t)$ and energy deficit queues $B_i(t)$ are strongly stable. That is, their long-term time averages are bounded:
    \begin{align}
        \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} \sum_{i,k} E[q_{i,k}(t)] &< \infty \label{eq_data_queue_stable_en}\\
        \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} \sum_{i} E[B_i(t)] &< \infty \label{eq_energy_queue_stable_en}
    \end{align}
\end{lemma}
\begin{proof}
    The proof relies on analyzing the Lyapunov drift-plus-penalty expression. According to Section V.C, the algorithm minimizes the drift-plus-penalty upper bound in each time slot. Let the minimized value be $Y_{min}(t)$ and the value under an optimal stationary policy $\pi^*$ be $Y^*(t)$. We have:
    \begin{equation}
        \Delta L(\Theta(t)) - V E[\text{DeliveredData}(t) | \Theta(t)] \le Y_{min}(t)
    \end{equation}
    Since Lyapunov-MEC minimizes this expression over all feasible policies in the current slot, its performance is at least as good as that of the stationary policy $\pi^*$ applied to the current state:
    \begin{equation}
        Y_{min}(t) \le Y^*(t) = E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
    \end{equation}
    where $\Delta L^*(\Theta(t))$ is the drift under policy $\pi^*$.
    Combining these, we get:
    \begin{align}
        \Delta L(\Theta(t)) &- V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
        &\le E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
    \end{align}
    Since $\lambda$ is strictly within the stability region $\Lambda$, there exists an $\epsilon > 0$ such that policy $\pi^*$ satisfies the stability conditions:
    \begin{align}
        E[A_{i,k}^*(t) | \Theta(t)] &\le E[\text{Outflow}_{i,k}^*(t) | \Theta(t)] + E[a_{i,k}^t] - \epsilon \\
        E[b_i^*(t+1) | \Theta(t)] &\ge \delta_i + \epsilon
    \end{align}
    Using these conditions, it can be shown (following standard Lyapunov analysis~\cite{neely2010stochastic}) that the drift under $\pi^*$ satisfies:
    \begin{equation}
        E[\Delta L^*(\Theta(t)) | \Theta(t)] \le C^* - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t))
    \end{equation}
    for some constants $C^*, \epsilon' > 0$.
    Substituting this back into the drift-plus-penalty inequality:
    \begin{align}
        & \Delta L(\Theta(t)) - V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
        &\le C^* - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t)) - V E[\text{DeliveredData}^*(t) | \Theta(t)]
    \end{align}
    Since $E[\text{DeliveredData}(t)]$ and $E[\text{DeliveredData}^*(t)]$ are non-negative, we can bound the expression:
    \begin{equation}
        \Delta L(\Theta(t)) \le C^{**} - \epsilon' (\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t))
    \end{equation}
    where $C^{**} = C^* + V \cdot \text{MaxPossibleThroughput}$ is a constant (assuming bounded throughput).
    This inequality shows that as long as the weighted sum of queue lengths $\sum_{i,k} q_{i,k}(t) + \sum_i B_i(t)$ is large enough (greater than $C^{**}/\epsilon'$), the drift is negative. By the Foster-Lyapunov stability criterion~\cite{meyn2012markov},
    this negative drift condition implies that the system state (represented by the queues) is positive recurrent and therefore stable. Strong stability (bounded average queue sizes) follows under mild conditions on the second moments of arrival and service processes.
\end{proof}

\subsection{LTA Energy Constraint Satisfaction}
The stability of the virtual energy deficit queue $B_i(t)$ is key to ensuring that the LTA energy constraint is met. The following corollary proves this, with an analysis approach similar to that of Lemma 2 in~\cite{Gao2024SWIPT}.

\begin{corollary}[LTA Energy Constraint Satisfaction]
    \label{corollary_energy_constraint_en}
    Under the conditions of Lemma~\ref{lemma_queue_stability_en}, a control policy that minimizes the drift-plus-penalty upper bound ensures that for all nodes $i \in \mathcal{N}$, the LTA battery energy constraint (Eq.~\eqref{eq_lta_energy}) is satisfied:
    $$ \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge \delta_i $$
\end{corollary}
\begin{proof}
    From the update rule of the virtual queue $B_i(t)$ (Eq.~\eqref{eq_virtual_energy_queue}), we have $B_i(t+1) \ge B_i(t) + \delta_i - b_i(t+1)$.
    Rearranging gives:
    \begin{equation}
        b_i(t+1) \ge B_i(t) - B_i(t+1) + \delta_i
    \end{equation}
    Summing this inequality from $t=0$ to $T-1$:
    \begin{align}
        \sum_{t=0}^{T-1} b_i(t+1) &\ge \sum_{t=0}^{T-1} (B_i(t) - B_i(t+1)) + \sum_{t=0}^{T-1} \delta_i \nonumber \\
        &= (B_i(0) - B_i(1)) + (B_i(1) - B_i(2)) \nonumber \\
        & \qquad + \dots + (B_i(T-1) - B_i(T)) + T \delta_i \nonumber \\
        &= B_i(0) - B_i(T) + T \delta_i
    \end{align}
    Taking expectations on both sides:
    \begin{equation}
        \sum_{t=0}^{T-1} E[b_i(t+1)] \ge E[B_i(0)] - E[B_i(T)] + T \delta_i
    \end{equation}
    Dividing by $T$:
    \begin{equation}
        \frac{1}{T} \sum_{t=1}^{T} E[b_i(t)] \ge \frac{E[B_i(0)]}{T} - \frac{E[B_i(T)]}{T} + \delta_i
    \end{equation}
    Taking the limit inferior as $T \to \infty$:
    \begin{equation}
        \liminf_{T \to \infty} \frac{1}{T} \sum_{t=1}^{T} E[b_i(t)] \ge \liminf_{T \to \infty} \left( \frac{E[B_i(0)]}{T} - \frac{E[B_i(T)]}{T} + \delta_i \right)
    \end{equation}
    Since $B_i(0)$ is a finite initial value, $\lim_{T \to \infty} E[B_i(0)]/T = 0$. From Lemma~\ref{lemma_queue_stability_en}, $B_i(t)$ is strongly stable, which implies that the average queue size is bounded, $ \limsup_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[B_i(t)] < \infty$. This further implies that $\lim_{T \to \infty} E[B_i(T)]/T = 0$ (otherwise, the average would grow unbounded).
    Thus, we have:
    \begin{equation}
        \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[b_i(t)] \ge 0 - 0 + \delta_i = \delta_i
    \end{equation}
    This confirms that the LTA battery energy constraint is satisfied.
\end{proof}

\subsection{LTA Throughput Performance Bound}
Finally, this subsection aims to derive a performance lower bound for the proposed algorithm in terms of LTA throughput and compare it with the theoretical optimum. The analysis method is similar to the proof of the relevant lemma in~\cite{Gao2024SWIPT}.

\begin{lemma}[LTA Throughput Bound]
    \label{lemma_throughput_bound_en}
    Let $\mathcal{D}^*$ be the maximum achievable LTA throughput subject to network constraints (possibly under an optimal, potentially clairvoyant policy $\pi^*$). For a control policy that aims to minimize the drift-plus-penalty upper bound, the achieved LTA throughput $\bar{\mathcal{D}}$ satisfies:
    \begin{equation}
        \label{eq_throughput_lower_bound_en}
        \bar{\mathcal{D}} = \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \ge \mathcal{D}^* - \frac{C}{V}
    \end{equation}
    where $C$ is a constant from the drift bound (Eq.~\eqref{eq_drift_upper_bound_detailed_en}) related to bounds on squared arrival and service rates, and $V$ is the control parameter.
\end{lemma}
\begin{proof}
    Based on the core principle that the Lyapunov-MEC algorithm aims to minimize the drift-plus-penalty upper bound (Eq.~\eqref{eq_Y_upper_bound_en}), and referring to the key inequality in the proof of Lemma~\ref{lemma_queue_stability_en}, we have:
    \begin{align}
                 \Delta L(\Theta(t)) &- V E[\text{DeliveredData}(t) | \Theta(t)] \nonumber \\
                 &\le E[\Delta L^*(\Theta(t)) - V \cdot \text{DeliveredData}^*(t) | \Theta(t)]
            \end{align}
    Taking expectation over $\Theta(t)$:
    \begin{align}
                 E[\Delta L(\Theta(t))] &- V E[\text{DeliveredData}(t)] \nonumber \\
                 &\le E[\Delta L^*(\Theta(t))] - V E[\text{DeliveredData}^*(t)]
            \end{align}
    The term $E[\Delta L^*(\Theta(t))]$ represents the expected one-slot Lyapunov drift under the optimal stationary policy $\pi^*$. For any stationary policy that stabilizes the system, its expected drift must have a constant upper bound, denoted as $C_{drift}^*$ (i.e., $E[\Delta L^*(\Theta(t))] \le C_{drift}^*$). Also, by definition, $E[\text{DeliveredData}^*(t)] = \mathcal{D}^*$.
    Substituting these into the inequality:
    \begin{equation}
         E[L(\Theta(t+1)) - L(\Theta(t))] - V E[\text{DeliveredData}(t)] \le C_{drift}^* - V \mathcal{D}^*
    \end{equation}
    Summing from $t=0$ to $T-1$:
    \begin{align}
        & \sum_{t=0}^{T-1} (E[L(\Theta(t+1))] - E[L(\Theta(t))]) - V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
        &\le T C_{drift}^* - T V \mathcal{D}^*
    \end{align}
    The first sum is a telescoping sum: $E[L(\Theta(T))] - E[L(\Theta(0))]$.
    \begin{align}
                E[L(\Theta(T))] &- E[L(\Theta(0))] \nonumber \\
                &- V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
                &\le T C_{drift}^* - T V \mathcal{D}^*
            \end{align}
    Since $L(\Theta(t)) \ge 0$, we have $- E[L(\Theta(0))] - V \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \le T C_{drift}^* - T V \mathcal{D}^*$.
    Rearranging and dividing by $VT$ (assuming $V>0$):
    \begin{equation}
        \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \ge \mathcal{D}^* - \frac{C_{drift}^*}{V} - \frac{E[L(\Theta(0))]}{VT}
    \end{equation}
    Taking the limit inferior as $T \to \infty$:
    \begin{align}
        \bar{\mathcal{D}} &= \liminf_{T \to \infty} \frac{1}{T} \sum_{t=0}^{T-1} E[\text{DeliveredData}(t)] \nonumber \\
        &\ge \mathcal{D}^* - \frac{C_{drift}^*}{V} + \liminf_{T \to \infty} \left( - \frac{E[L(\Theta(0))]}{VT} \right)
    \end{align}
    Since $L(\Theta(0))$ is finite, $\lim_{T \to \infty} E[L(\Theta(0))]/(VT) = 0$.
    Thus, we obtain the desired result:
    \begin{equation}
        \bar{\mathcal{D}} \ge \mathcal{D}^* - \frac{C_{drift}^*}{V}
    \end{equation}
    We can replace the constant $C_{drift}^*$ with the constant $C$ from the general drift bound (Eq.~\eqref{eq_drift_upper_bound_detailed_en}), as they represent bounds on similar system dynamics, yielding $\bar{\mathcal{D}} \ge \mathcal{D}^* - C/V$.
\end{proof}

Lemma~\ref{lemma_throughput_bound_en} quantifies the performance guarantee of the ideal control policy. It shows that by choosing a sufficiently large value for the control parameter $V$, the LTA throughput achieved by the ideal policy can be arbitrarily close to the optimal throughput $\mathcal{D}^*$. The term $C/V$ represents the optimality gap. However, increasing $V$ typically leads to larger average queue sizes (as implied by the structure of the stability proof, often scaling as $O(V)$), which represents the fundamental $[O(1/V), O(V)]$ trade-off between average performance optimality and queue backlog/delay in stochastic network optimization.

\subsection{Discussion on the Heuristic Algorithm Lyapunov-MEC}
The theoretical analysis above applies to an ideal control policy that can exactly minimize the drift-plus-penalty upper bound. The proposed Lyapunov-MEC algorithm (Algorithms~\ref{alg_lyapunov_mec_phase1_en} and~\ref{alg_lyapunov_mec_phase2_en}) is a heuristic implementation of this ideal policy, which employs several simplifications to reduce computational complexity:
\begin{itemize}
    \item \textbf{Approximation in Energy Cooperation Decision:} In Phase 1, the calculation of energy cooperation gain $\Delta W_i^{\text{EC}}$ (Eq.~\eqref{eq_ec_gain_est_en}) uses a simplified estimation of energy harvesting, neglecting potential interference or contributions from other concurrent transmissions.
    \item \textbf{Greedy Link Selection:} In Phase 2, a greedy approach is used by sorting links by their net weights and selecting them sequentially, which may not achieve the globally optimal combination of links.
    \item \textbf{Power Control Strategy:} In Phase 2, a dynamic power control strategy is employed, i.e., calculating and using the minimum transmission power $p_{\text{required\_min\_W}}$ (subject to $[p_{\min}, p_{\max}]$) needed to satisfy the minimum SNR threshold $\gamma_{\min}$, rather than performing a more complex joint power optimization.
\end{itemize}
While these approximations enable the algorithm to run efficiently online, they also mean that Lyapunov-MEC may not fully minimize the drift-plus-penalty upper bound in each time slot. Consequently, its true performance (e.g., the actual throughput-delay trade-off) might deviate from the theoretical $[O(1/V), O(V)]$ bounds. Furthermore, if the heuristic choices significantly deviate from optimal decisions in certain scenarios, the stability of the algorithm might require stronger conditions (e.g., smaller arrival rates or more stringent channel/energy conditions) than theoretically assumed.

Therefore, comprehensive simulation experiments are crucial for evaluating the practical performance of the proposed Lyapunov-MEC heuristic algorithm, validating its effectiveness under various network scenarios, and quantifying the gap between its performance and the theoretical performance bounds.

\section{Simulation Results and Analysis}
\label{sec_simulation}
In this section, we evaluate the performance of the proposed Lyapunov-MEC algorithm through simulation experiments. The simulations are conducted using Python on a Windows 10 computer equipped with an Intel Core i7 CPU and 16GB of RAM. We first introduce the simulation setup, including performance metrics, algorithms for comparison, and parameter configurations, and then present and analyze the simulation results.

\subsection{Simulation Setup}
\label{subsec_sim_setup}

\subsubsection{Performance Metrics}
\label{subsubsec_metrics}
We adopt the following performance metrics to evaluate the algorithm performance:
\begin{itemize}
    \item \textbf{Long-Term Average (LTA) Throughput:} Defined as the ratio of the total amount of data successfully received by all sink nodes to the total simulation duration (in Mbps). This is the primary metric for evaluating the system's data processing capability.
    \item \textbf{Average Queue Length:} This includes the average data queue length ($\frac{1}{T}\sum_{t=0}^{T-1} E[\sum_{i,k} q_{i,k}(t)]$) and the average energy deficit queue length ($\frac{1}{T}\sum_{t=0}^{T-1} E[\sum_{i} B_{i}(t)]$). These metrics are used to assess queue stability and resource backlog.
    \item \textbf{Average Battery Level:} Defined as $\frac{1}{T}\sum_{t=0}^{T-1} E[\sum_{i} b_{i}(t)] / N$, used to observe energy sustainability.
\end{itemize}
The LTA throughput is the main performance measure, while queue lengths and battery levels serve as auxiliary indicators for a deeper understanding of algorithm behavior.

\subsubsection{Algorithms for Comparison}
\label{subsubsec_benchmarks_list}
To comprehensively evaluate the proposed Lyapunov-MEC algorithm, we compare it with the following algorithms:
\begin{itemize}
    \item \textbf{Lyapunov-MEC:} The proposed Lyapunov optimization-based online heuristic algorithm, which integrates a multicast energy cooperation (M-EC) mechanism (detailed in Algorithm~\ref{alg_lyapunov_mec_phase1_en} and Algorithm~\ref{alg_lyapunov_mec_phase2_en}). Its data transmission power $p_l^t$ is dynamically adjusted to meet the preset minimum SNR threshold $\gamma_{\min}$, i.e., $p_l^t = p_{\text{required\_min\_W}}$, and its power range is limited by parameters $p_{\min}$ and $p_{\max}$ (see Table~\ref{tab:sim_params}).
    \item \textbf{Lyapunov-NoEC:} A variant of the Lyapunov-MEC algorithm where the energy cooperation function is completely disabled. Its data transmission power control strategy is consistent with Lyapunov-MEC.
    \item \textbf{Lyapunov-UEC:} Another variant of the Lyapunov-MEC algorithm that employs unicast energy cooperation (U-EC) instead of M-EC. Its data transmission power control strategy is also the same as Lyapunov-MEC.
    \item \textbf{Greedy-MaxWeight (GMW):} A classic greedy scheduling algorithm that selects links based on queue differences, without explicitly considering node energy states or implementing energy cooperation. This algorithm uses a fixed data transmission power of 5 dBm.
    \item \textbf{Energy-Aware Greedy (EAG):} An improved version of the GMW algorithm that introduces basic energy awareness, prioritizing nodes with more abundant energy for data transmission. This algorithm also uses a fixed data transmission power of 5 dBm.
    \item \textbf{Randomized Scheduling (RAND):} A random scheduling strategy that activates links randomly while satisfying basic link constraints, usually serving as a lower bound for performance benchmarks. This algorithm also employs a fixed data transmission power of 5 dBm.
\end{itemize}
The power parameters ($p_{\min}, p_{\max}$) for the Lyapunov-based algorithms are detailed in Table~\ref{tab:sim_params}.

\subsubsection{Simulation Parameters}
\label{subsubsec_sim_params_desc}
We consider a WSN deployed in a 100m $\times$ 100m square area. The network comprises $N$ sensor nodes and $K$ sink nodes, whose specific locations are randomly generated while ensuring network connectivity. Unless otherwise specified, we set $N=20$ and $K=2$ by default. The maximum communication range of sensor nodes is set to 30m.

The main simulation parameters, based on related literature, are summarized in Table~\ref{tab:sim_params}. Data communication parameters include a channel bandwidth $W = 1$ MHz and a noise power spectral density of -174 dBm/Hz ($\sigma^2 = -114$ dBm). We adopt the Rician fading channel model (Eq.~\eqref{eq_g_ij_rician}) with a K-factor $K_{i,j}=10$~\cite{Gao2015}, and an adjusted Friis path loss model (Eq.~\eqref{eq_g_ij}) with parameters $\lambda=0.33$ m, $G_i=8$ dBi, $G_j=2$ dBi, $\beta=0.2$, and $L_p=1$ dB~\cite{He2013}. The minimum SNR threshold is $\gamma_{\min} = 5$ dB. For the Lyapunov-based algorithms, their data transmission power ranges between $[p_{\min}, p_{\max}] = [10, 20]$ dBm (see Table~\ref{tab:sim_params}); in contrast, the GMW, EAG, and RAND algorithms use a fixed transmission power of 5 dBm (see Section~\ref{subsubsec_benchmarks_list}). The energy consumption for reception and sensing are $p_{\text{rcv}} = 50$ $\mu$J/Mbit and $p_{\text{sense}} = 60$ $\mu$J/Mbit, respectively. The data queue capacity is $q_{\max} = 50$ Mbit.

Energy-related parameters include a maximum battery capacity $b_{\max} = 10$ J, with initial energy levels randomly distributed in $[0.2 b_{\max}, 0.8 b_{\max}]$. Environmental energy harvesting follows the four-state Markov model from~\cite{Ku2015}, with average harvested power levels of [10, 5, 2, 0.5] mW. RF energy harvesting employs the nonlinear model (Eq.~\eqref{eq_EH1}) with parameters $\mu = 1500$, $\nu = 0.002$, $e_{\text{mp}} = 0.024$ W~\cite{Boshkovska2015}, and an RF harvesting efficiency from data signals $\eta_{\text{data}}=0.1$. The energy transmission power ranges between $[e_{\min}, e_{\max}] = [10, 100]$ mW. The LTA energy threshold is $\delta_i = 0.3 b_{\max}$.

Data packets are generated at each node with a probability $p_{\text{arrival}} = 0.2$, with a randomly chosen destination, and packet sizes follow an exponential distribution with a mean of 10 Mbit. The control parameter $V$ for Lyapunov-MEC (and its variants) is set to 100 by default, and the energy cooperation thresholds are $\alpha=0.3, \beta'=10, \gamma=0.4, \zeta'=10.0$.

The total simulation duration is $T = 10000$ time slots. To obtain reliable results, each simulation configuration is run independently 30 times, and the final results are averaged. In the figures, we present the mean values along with their corresponding 95\% confidence intervals to reflect statistical significance.


\begin{table}[htbp]
\centering
\caption{Simulation Parameter Settings}
\label{tab:sim_params}
\resizebox{0.48\textwidth}{!}{
\begin{tabular}{|l|l|c|}
\hline
\textbf{Category} & \textbf{Parameter Description} & \textbf{Default Value} \\
\hline
\multirow{4}{*}{Network Setup} 
& Deployment Area & 100m $\times$ 100m \\
& Number of Sensor Nodes ($N$) & 20 \\
& Number of Sink Nodes ($K$) & 2 \\
& Maximum Communication Range & 30 m \\
\hline
\multirow{11}{*}{Communication Parameters}
& Channel Bandwidth ($W$) & 1 MHz \\
& Noise Power Spectral Density (PSD) & -174 dBm/Hz \\
& Noise Power ($\sigma^2$) & -114 dBm \\
& Rician K-factor ($K_{i,j}$) & 10 \cite{Gao2015} \\
& Signal Wavelength ($\lambda$) & 0.33 m \cite{He2013} \\
& Antenna Gains ($G_i, G_j$) & 8 dBi, 2 dBi \cite{He2013} \\
& Path Loss Parameters ($\beta, L_p$) & 0.2, 1 dB \cite{He2013} \\
& Minimum SNR Threshold ($\gamma_{\min}$) & 5 dB \\
& Data Transmission Power ($[p_{\min}, p_{\max}]$) & [10, 20] dBm \\
& Reception Energy Consumption ($p_{\text{rcv}}$) & 50 $\mu$J/Mbit \\
& Sensing Energy Consumption ($p_{\text{sense}}$) & 60 $\mu$J/Mbit \\
& Data Queue Capacity ($q_{\max}$) & 50 Mbit \\
\hline
\multirow{8}{*}{Energy Parameters}
& Maximum Battery Capacity ($b_{\max}$) & 10 J \\
& Initial Energy Range & $[0.2 b_{\max}, 0.8 b_{\max}]$ \\
& Environmental EH Model & 4-state Markov \cite{Ku2015} \\
& Average EH Power (mW) & [10, 5, 2, 0.5] \cite{Ku2015} \\
& RF Harvesting Parameters ($\mu, \nu, e_{\text{mp}}$) & 1500, 0.002, 0.024 W \cite{Boshkovska2015} \\
& RF Data Signal Harvesting Efficiency ($\eta_{\text{data}}$) & 0.1 \\
& Energy Transmission Power ($[e_{\min}, e_{\max}]$) & [10, 100] mW \\
& LTA Energy Threshold ($\delta_i$) & $0.3 b_{\max}$ \\
\hline
\multirow{2}{*}{Data Arrival} 
& Arrival Probability ($p_{\text{arrival}}$) & 0.2 \\
& Packet Size Distribution & Exp(mean=10 Mbit) \\
\hline
\multirow{5}{*}{Lyapunov Parameters}
& Control Parameter ($V$) & 100 (variable) \\
& EC Threshold ($\alpha$) & 0.3 \\
& EC Threshold ($\beta'$) & 10.0 \\
& EC Threshold ($\gamma$) & 0.4 \\
& EC Threshold ($\zeta'$) & 10.0 \\
\hline
\multirow{2}{*}{Simulation Control} 
& Total Simulation Duration ($T$) & 10000 time slots \\
& Independent Runs & 30 \\
\hline
\end{tabular}
}
\end{table}

\subsection{Performance Comparison}
\label{subsec_perf_comp}
In this subsection, we compare the performance of the proposed Lyapunov-MEC algorithm with the benchmark algorithms defined in Section~\ref{subsubsec_benchmarks_list}, based on the performance metrics defined in Section~\ref{subsubsec_metrics}. We first analyze the impact of the Lyapunov control parameter $V$ on throughput and delay, and then focus on comparing the performance differences under various energy cooperation modes.

\subsubsection{Impact of Lyapunov Parameter V}
\label{subsubsec_impact_V}
The control parameter $V$ in the Lyapunov optimization framework tunes the trade-off between throughput maximization and queue stability. Fig.~\ref{fig_throughput_vs_V} illustrates the LTA throughput of different algorithms as a function of parameter $V$.

As observed from Fig.~\ref{fig_throughput_vs_V}, for all Lyapunov-based algorithms (Lyapunov-MEC, Lyapunov-NoEC, and Lyapunov-UEC), the LTA throughput tends to improve and eventually saturate as the control parameter $V$ increases. Specifically, the throughput of Lyapunov-MEC rapidly increases from approximately 32.1 Mbps at $V=0$ to about 33 Mbps at $V=2$, and then stabilizes around this peak level for $V \ge 2$. The throughput of Lyapunov-NoEC grows from about 30.1 Mbps at $V=0$ to approximately 30.8 Mbps at $V=2$, subsequently fluctuating slightly between 30.5 Mbps and 31 Mbps. Similarly, the Lyapunov-UEC algorithm, employing the new heuristic unicast logic, shows a gradual increase in throughput from about 28.3 Mbps at $V=0$, reaching a peak of approximately 28.8 Mbps around $V=4$ before stabilizing. This general trend aligns with the theoretical analysis in Lemma~\ref{lemma_throughput_bound_en}, which suggests that increasing $V$ prompts the algorithm to prioritize instantaneous throughput optimization, leading the system to tolerate larger queue backlogs in exchange for higher transmission efficiency. Typically, a very large $V$ is not required to approach the inherent throughput potential of each algorithm.

Across all tested $V$ values, the throughput of Lyapunov-MEC is consistently and significantly higher than that of Lyapunov-NoEC and Lyapunov-UEC, clearly demonstrating the inherent advantage of multicast energy cooperation (M-EC) in enhancing system data transmission capability. Over the entire range of the control parameter $V$, the performance of Lyapunov-NoEC is consistently superior to the new heuristic Lyapunov-UEC, indicating that, in this parameter dimension, a strategy of no energy cooperation at all yields higher throughput than the currently designed unicast heuristic. The confidence intervals for all Lyapunov-based algorithms are relatively narrow, indicating consistent performance. In contrast, the throughputs of GMW and EAG algorithms (stabilizing at approximately 28.1 Mbps and 28.3 Mbps, respectively), which are not based on Lyapunov optimization, are insensitive to $V$ (as they do not use this control parameter) and are significantly lower than those of Lyapunov-MEC and Lyapunov-NoEC. This is primarily due to their lack of systematic mechanisms for managing queue stability and long-term energy constraints. The RAND algorithm exhibits the lowest throughput (around 20.5 Mbps), as expected for a random scheduling baseline.


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig_parallel/par_sweep_throughput_vs_V_CONTROL.png}
    \caption{LTA throughput vs. Lyapunov control parameter V.}
    \label{fig_throughput_vs_V}
\end{figure}

\subsubsection{Comparison of Energy Cooperation Modes}
\label{subsubsec_ec_mode_comparison}

To further elucidate the contribution of energy cooperation mechanisms to overall network performance, we conduct a detailed examination of the dynamic evolution and steady-state performance of Lyapunov-MEC (employing M-EC) versus Lyapunov-NoEC (no energy cooperation) under default parameter settings (e.g., $V=100$). Fig.~\ref{fig_comparison_mec_uec_noec} visually presents a comparison of these two strategies along with other benchmark algorithms (GMW, EAG, RAND) concerning energy stability, data queue backlog, and instantaneous throughput. A detailed parameter impact analysis of the LTA throughput performance for Lyapunov-UEC (employing U-EC) is provided in Section~\ref{subsec_param_impact}.

The following key observations can be made from Fig.~\ref{fig_comparison_mec_uec_noec}:
\begin{itemize}
    \item \textbf{Energy Stability and Sustainability:} As shown in Fig.~\ref{fig_comparison_mec_uec_noec} (top-left and bottom-left), energy cooperation is crucial for maintaining network energy balance. Under the Lyapunov-NoEC strategy, the average energy deficit queue exhibits unbounded growth over time, clearly indicating its inability to meet the predefined LTA energy sustainability constraint. In stark contrast, Lyapunov-MEC, with its efficient M-EC mechanism, successfully stabilizes the energy deficit queue near zero. While the average battery level of Lyapunov-NoEC stabilizes at a relatively high level, even surpassing that of Lyapunov-MEC, this is primarily a consequence of lower overall data transmission activity (and thus reduced energy consumption) rather than effective energy management. The battery level of Lyapunov-MEC shows a slight decrease in the later stages of the simulation, reflecting that energy is actively used to support higher data rates and necessary energy cooperation activities, while still successfully maintaining the average battery energy above the predefined threshold $\delta_i$. In comparison, algorithms like GMW, EAG, and RAND, which lack sophisticated energy management capabilities, show node battery levels quickly saturating near $b_{max}$, further evidencing their lower energy consumption rates, typically associated with poorer network throughput.
    \item \textbf{Data Queues and Congestion Control:} As depicted in Fig.~\ref{fig_comparison_mec_uec_noec} (top-right), energy cooperation mechanisms significantly alleviate network congestion. The Lyapunov-MEC strategy achieves the lowest and most stable average network data queue length, directly reflecting its superior efficiency in data transmission and congestion control. In the initial phase of the simulation, when nodes generally have ample energy, the data queue length under Lyapunov-NoEC is comparable to that of Lyapunov-MEC. However, as the simulation progresses, the lack of effective energy replenishment leads to energy depletion in critical relay nodes, causing its queue length to increase rapidly and eventually stabilize at a higher level, similar to traditional algorithms like GMW and EAG. This phenomenon clearly reveals that while Lyapunov-NoEC might perform adequately initially, the absence of energy cooperation makes it difficult to sustain low-congestion operation in the long term. The persistently high queue lengths of GMW, EAG, and RAND algorithms generally reflect their inherent limitations in scheduling decisions and overall resource utilization efficiency.
    \item \textbf{Instantaneous Throughput:} As illustrated in Fig.~\ref{fig_comparison_mec_uec_noec} (bottom-right), the Lyapunov-MEC strategy achieves the highest and relatively most stable average instantaneous throughput, significantly outperforming all other compared algorithms. The instantaneous throughput of Lyapunov-NoEC can approach that of Lyapunov-MEC in the initial phase but rapidly declines as node energy is consumed, eventually stabilizing at a level far below Lyapunov-MEC (though still slightly higher than GMW and EAG). This again strongly demonstrates the critical role of energy cooperation in maintaining long-term, efficient data transmission. GMW and EAG exhibit lower instantaneous throughput, while RAND performs the worst, consistent with its role as a random scheduling baseline.
\end{itemize}
In summary, these comparative results quantitatively demonstrate, from multiple perspectives, the significant and multifaceted advantages of energy cooperation—particularly the M-EC employed in the Lyapunov-MEC algorithm—in improving network performance. Energy cooperation is not only a core mechanism for ensuring long-term network energy stability but also effectively alleviates data congestion and ultimately substantially enhances overall system throughput. The Lyapunov-MEC algorithm, through its intelligent joint scheduling of energy cooperation and data transmission, exhibits optimal performance across all key evaluated metrics.


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig/fig_comparison_mec_noec_GMW_EAG_RAND.png}
    \caption{Performance comparison: Lyapunov-MEC (M-EC), Lyapunov-NoEC (No-EC), GMW, EAG, and RAND. (Top-left) Avg. Battery Level. (Top-right) Total Network Queue Size. (Bottom-left) Avg. Energy Deficit Queue. (Bottom-right) Avg. Instantaneous Throughput (100-slot smoothed).}
    \label{fig_comparison_mec_uec_noec}
\end{figure}

\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig/fig_convergence_dynamics.png}
    \caption{Convergence dynamics of average instantaneous throughput for different algorithms during the initial 100 time slots (smoothed with a 5-slot moving average).}
    \label{fig_convergence_dynamics}
\end{figure}

To further elucidate the transient dynamics, Figure~\ref{fig_convergence_dynamics} depicts the evolution of the average instantaneous throughput for all evaluated algorithms over the initial 100 time slots. The throughput data are smoothed using a 5-slot moving average to enhance trend visibility. The extended temporal window in this figure facilitates a more detailed examination of the algorithms' performance trajectories as they approach their respective steady-state operational points. Notably, the Lyapunov-based algorithms (Lyapunov-MEC, Lyapunov-NoEC, and Lyapunov-UEC) exhibit a pronounced and sustained ramp-up phase. Among these, Lyapunov-MEC consistently achieves and maintains superior throughput. While Lyapunov-UEC and Lyapunov-NoEC also demonstrate progressive performance improvements, their throughput levels generally remain subordinate to that of Lyapunov-MEC. In contrast, the baseline algorithms—GMW, EAG, and RAND—are characterized by more modest and comparatively volatile throughput profiles throughout this extended initial observation period. Consequently, this graphical representation offers a comprehensive qualitative insight into both the transient characteristics and the early steady-state differentiation of the considered scheduling strategies.


\subsection{Parameter Impact Analysis}
\label{subsec_param_impact}
In this subsection, we further investigate the impact of key system parameters on the performance of the proposed Lyapunov-MEC algorithm and related benchmark algorithms. We primarily focus on the LTA throughput metric to assess the robustness and adaptability of the algorithms under different network conditions.

\subsubsection{Impact of Battery Capacity}
\label{subsubsec_impact_battery}
Fig.~\ref{fig_throughput_vs_battery} shows the trend of LTA throughput as the maximum node battery capacity $b_{\max}$ varies from 1 J to 25 J. Analysis of this figure yields the following key observations:

The LTA throughput of the Lyapunov-MEC algorithm exhibits excellent and highly stable performance across the entire tested range of battery capacities. Its throughput slightly increases from approximately 32.6 Mbps at $b_{\max}=1$ J to about 32.9 Mbps at $b_{\max}=5$ J, after which it remains almost constant around 32.9 Mbps with further increases in battery capacity. This phenomenon fully demonstrates the robustness and efficiency of the proposed M-EC mechanism, indicating that even when individual node storage capacity is extremely limited, Lyapunov-MEC can still maintain near-optimal high throughput through effective energy sharing strategies.

In contrast to Lyapunov-MEC, the throughputs of both Lyapunov-NoEC and the new heuristic Lyapunov-UEC algorithm significantly benefit from increased battery capacity. The performance of Lyapunov-NoEC steadily improves from about 28.2 Mbps at $b_{\max}=1$ J to approximately 32.5 Mbps at $b_{\max}=25$ J. Similarly, the throughput of Lyapunov-UEC also increases from about 26.4 Mbps at $b_{\max}=1$ J to approximately 31.0 Mbps at $b_{\max}=25$ J. The upward trend in performance for these two algorithms clearly indicates that when energy cooperation capability is limited (unicast mode in UEC) or absent (NoEC), the node's own storage capacity becomes a critical factor affecting network throughput; larger battery capacities can better buffer the randomness of environmental energy harvesting, thereby supporting more sustained data transmission.

Further comparing these three Lyapunov-based strategies: when battery capacity is extremely small (e.g., $b_{\max}=1$ J), the performance advantage of Lyapunov-MEC is most prominent, with its throughput being significantly higher than Lyapunov-NoEC (by about 4.4 Mbps) and Lyapunov-UEC (by about 6.2 Mbps). This highlights the critical value of M-EC under extreme energy constraints. As battery capacity increases, the performance of Lyapunov-NoEC and Lyapunov-UEC gradually improves, narrowing the gap with Lyapunov-MEC. However, even at $b_{\max}=25$ J, Lyapunov-MEC still maintains the highest throughput. When battery capacity is large (e.g., $b_{\max} \ge 15$ J), the performance of Lyapunov-NoEC slightly surpasses that of Lyapunov-UEC. This might suggest that when nodes have ample self-storage, the gains from the unicast energy transfer employed by the current heuristic UEC algorithm may not be sufficient to compensate for its potential energy transfer overhead or opportunity costs, making the NoEC strategy (where nodes operate independently) perform better.

    For the GMW and EAG algorithms, their throughputs (around 28.1-28.4 Mbps), as well as that of the RAND algorithm (around 20.5 Mbps), are largely unaffected by changes in battery capacity and are significantly lower than all Lyapunov-based optimization algorithms. This is consistent with their design characteristics, which do not fully consider dynamic energy states and cooperation opportunities.

In summary, these results clearly demonstrate that the M-EC mechanism in Lyapunov-MEC not only achieves the highest LTA throughput but also greatly enhances the network's adaptability to variations in battery capacity. Systems lacking efficient energy cooperation rely heavily on larger battery capacities to improve performance, and even then, their performance ceiling can hardly match that of the Lyapunov-MEC algorithm, which implements efficient energy sharing.


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig_parallel/par_sweep_throughput_vs_BATTERY_MAX_J.png}
    \caption{LTA throughput vs. maximum battery capacity $b_{max}$.}
    \label{fig_throughput_vs_battery}
\end{figure}

\subsubsection{Impact of Number of Nodes}
\label{subsubsec_impact_nodes}
Fig.~\ref{fig_throughput_vs_nodes} investigates the impact of the number of sensor nodes $N$ in the network on LTA throughput. In this study, $N$ ranges from 10 to 50, with a step of 10. All nodes are deployed within a fixed 100m $\times$ 100m square area, meaning that as $N$ increases, the node density of the network also increases. An increase in the number of nodes typically leads to a higher potential for total data generation in the network (assuming the average data arrival rate per node remains constant) and may also introduce richer routing options and energy cooperation opportunities.

Fig.~\ref{fig_throughput_vs_nodes} illustrates the specific trends of LTA throughput for each algorithm as the number of sensor nodes $N$ varies (from 20 to 50) under this setup. We observe the following main trends:

The Lyapunov-MEC algorithm demonstrates optimal performance and good scalability. Its LTA throughput consistently increases with the number of nodes $N$, growing steadily from approximately 32.9 Mbps at $N=20$ to about 36.5 Mbps at $N=50$. This indicates that Lyapunov-MEC can effectively leverage the richer routing diversity and energy cooperation opportunities afforded by network expansion to support higher network loads and enhance overall data transmission capability.

The Lyapunov-UEC algorithm, employing the new heuristic logic, also shows a significant performance improvement trend. Its throughput substantially increases from about 28.5 Mbps at $N=20$ to approximately 35.6 Mbps at $N=50$. As the number of nodes increases, the performance of Lyapunov-UEC gradually approaches that of Lyapunov-MEC, with a relatively small gap at $N=50$. This may be attributed to the UEC algorithm finding suitable unicast energy sharing pairs more easily in denser networks, thereby improving its energy cooperation effectiveness.

In contrast, the Lyapunov-NoEC algorithm exhibits a different performance trend. Its throughput increases from about 30.4 Mbps at $N=20$ to a peak of approximately 33.9 Mbps at $N=40$, but then decreases to about 33.3 Mbps when the number of nodes further increases to $N=50$. This reveals that in the absence of an energy cooperation mechanism, although an initial increase in node numbers can bring some performance gains through increased potential routing paths, the bottleneck issue of energy depletion (especially at critical relay nodes) becomes more pronounced as the network scale continues to expand, thereby limiting its scalability. When the number of nodes is large, increased network complexity and potentially longer routes might exacerbate the energy bottleneck effect, leading to performance degradation. Consequently, in scenarios with a larger number of nodes (e.g., $N \ge 40$), Lyapunov-UEC outperforms Lyapunov-NoEC.

The throughputs of the GMW and EAG algorithms show an increase as $N$ goes from 20 to 40 (reaching peaks of about 32.6 Mbps and 32.3 Mbps, respectively), but both exhibit a clear performance drop at $N=50$, further highlighting their limited scalability in larger networks. The RAND algorithm's throughput slowly increases with $N$, from about 20.6 Mbps at $N=20$ to approximately 23.2 Mbps at $N=50$, consistently remaining at the lowest level.

These results collectively indicate that the proposed Lyapunov-MEC algorithm not only maintains a performance advantage across various network scales but also its good scalability demonstrates the importance of multicast energy cooperation in supporting the efficient operation of large-scale EH-WSNs. Meanwhile, heuristic unicast energy cooperation (Lyapunov-UEC) can also provide competitive performance in denser networks, outperforming schemes without energy cooperation.


\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig_parallel/par_sweep_throughput_vs_NUM_NODES.png}
    \caption{LTA throughput vs. number of network nodes N.}
    \label{fig_throughput_vs_nodes}
\end{figure}

\subsubsection{Statistical Significance Analysis}
\label{subsubsec_anova}
To conduct a rigorous statistical evaluation of the performance of different energy cooperation strategies, we performed a one-way Analysis of Variance (ANOVA) on the LTA throughput data obtained under specific parameter configurations (i.e., network size $N=20$, number of sink nodes $K=2$, Lyapunov control parameter $V=100$, and maximum battery capacity $b_{\max}=10$ J). This analysis compared three algorithms: Lyapunov-MEC (M-EC), Lyapunov-UEC (U-EC, employing the new heuristic unicast logic), and Lyapunov-NoEC (No-EC). The significance level for all statistical tests was set at $\alpha = 0.05$.

The ANOVA results (detailed in Table~\ref{tbl_anova_analysis}) revealed a highly significant statistical difference in LTA throughput among the three compared algorithms (F-statistic = 70.0765, $p$-value = $7.4 \times 10^{-19}$). Given that the $p$-value is far below the preset significance level $\alpha$, we reject the null hypothesis that the mean throughputs of the algorithms are equal.

To further investigate the specific differences between pairs of algorithms, we conducted Tukey's HSD (Honestly Significant Difference) post-hoc test. The results, also summarized in Table~\ref{tbl_anova_analysis}, indicate the following main findings:
\begin{itemize}
    \item The average LTA throughput of the Lyapunov-MEC algorithm is significantly superior to that of the Lyapunov-NoEC algorithm (mean difference approx. 2.35 Mbps, $p < 0.001$).
    \item The average LTA throughput of the Lyapunov-MEC algorithm is also significantly superior to that of the Lyapunov-UEC algorithm employing the new heuristic logic (mean difference approx. 4.02 Mbps, $p < 0.001$).
    \item The average LTA throughput of the Lyapunov-NoEC algorithm is significantly superior to that of the new heuristic Lyapunov-UEC algorithm (mean difference approx. 1.67 Mbps, $p < 0.001$).
\end{itemize}
These statistical analysis results provide strong evidence for the following conclusions: under the investigated parameter conditions ($b_{\max}=10$ J), Lyapunov-MEC, with its M-EC mechanism, exhibits the optimal performance in terms of LTA throughput. The Lyapunov-NoEC scheme (no energy cooperation) ranks second. The Lyapunov-UEC scheme, after modifications to its heuristic logic, performs relatively the weakest in this specific configuration. This performance ranking differs from preliminary analyses based on earlier UEC versions, highlighting the critical impact of the specific design of energy cooperation strategies on overall algorithm performance and further confirming the inherent advantages of the M-EC strategy in enhancing network throughput.


\begin{table}[htbp]	
\centering
\caption{ANOVA Test and Tukey HSD Post-Hoc Analysis for LTA Throughput. MD: Mean Difference; Lb: Lower Bound; Ub: Upper Bound (95\% CI for difference).}
\label{tbl_anova_analysis}
\resizebox{0.48\textwidth}{!}{
    \begin{tabular}{|c|c|c|c|c|c|c|}
        \hline
        \multicolumn{3}{|c|}{\textbf{ANOVA Test}} & \multicolumn{4}{c|}{\textbf{Tukey HSD Post-Hoc Analysis}} \\
        \hline
        F-statistic  & $p$-value & F-critical & Comparison & Lb & MD & Ub \\
        \hline
        \multirow{3}{*}{70.0765} & \multirow{3}{*}{$7.4 \times 10^{-19}$} & \multirow{3}{*}{3.1013} & MEC vs. UEC & 3.2088 & 4.0228 & 4.8369  \\
        \cline{4-7}
        & & & MEC vs. NoEC & 1.5356 & 2.3497 & 3.1637  \\
        \cline{4-7}
        & & & NoEC vs. UEC & 0.8591 & 1.6732 & 2.4872  \\
        \hline
    \end{tabular}
}
\end{table}


\section{Conclusion}
\label{sec_conclusion}
This paper investigated the problem of energy cooperation-assisted data collection in energy harvesting wireless sensor networks, aiming to maximize the long-term average throughput of the network while ensuring data queue stability and satisfying the long-term average energy sustainability requirements of the nodes. We modeled this problem as a stochastic network optimization problem and utilized Lyapunov optimization theory to transform it into a series of per-slot deterministic optimization problems.

To address the computational complexity in solving the per-slot optimization problems, we proposed a low-complexity online heuristic algorithm, Lyapunov-MEC. This algorithm approximates the maximization of the Lyapunov drift-plus-penalty objective by decomposing the decision-making process into two phases—energy cooperation and data transmission—and employing greedy strategies based on queue and energy states while satisfying instantaneous constraints. Theoretical analysis indicated that an ideal Lyapunov control policy can guarantee queue stability and LTA energy constraint satisfaction, and achieve near-optimal throughput performance, with the gap to optimality controlled by the parameter $V$.

Simulation results validated the effectiveness of the Lyapunov-MEC algorithm. Compared to the Lyapunov-NoEC algorithm (which disables energy cooperation) and other benchmark algorithms (such as GMW and EAG), Lyapunov-MEC demonstrated significant advantages in terms of LTA throughput, especially under high network loads or limited node energy conditions. The simulation results also clearly illustrated the trade-off between throughput and delay in Lyapunov optimization, as well as the critical role of energy cooperation in enhancing system performance. Furthermore, parameter impact analysis showed that the Lyapunov-MEC algorithm exhibits good adaptability and scalability with respect to key parameters such as battery capacity and network size.

Future work could explore more sophisticated power control strategies to further improve upon the dynamic power adjustment strategy based on meeting the minimum SNR threshold currently employed in the algorithm, potentially leading to further performance enhancements.

\bibliographystyle{IEEEtran}
\bibliography{mybibfile}
	
\end{document}
