#!/usr/bin/env python3
"""
Test script for GDTS algorithm implementation.
Verifies that GDTS algorithm works correctly and follows the pseudocode from <PERSON>'s paper.
"""

import sys
import numpy as np
from comparison_algorithms import (
    gdts_scheduling, gdts_no_ec_scheduling, 
    calculate_hop_distance, gdts_violation_check, gdts_calculate_flow,
    GDTS_P_MIN_W, GDTS_P_MAX_W, GDTS_E_MIN_W, GDTS_E_MAX_W, 
    GDTS_B_MIN_J, GDTS_GAMMA_MIN_dB, GDTS_GAMMA_MIN
)

def create_test_network():
    """Create a simple test network for GDTS testing."""
    # Create 3 nodes and 1 sink
    nodes = [
        {
            'id': 1, 'pos': (0, 0), 'is_sink': False,
            'battery': 5.0,  # Above b_min threshold
            'queue_bits': {4: 1000.0},  # Has data for sink 4
            'energy_deficit_queue': 0.0,
            'env_eh_state': 0
        },
        {
            'id': 2, 'pos': (1, 0), 'is_sink': False,
            'battery': 2.0,  # Below b_min threshold (needs energy)
            'queue_bits': {4: 500.0},   # Has data for sink 4
            'energy_deficit_queue': 0.0,
            'env_eh_state': 0
        },
        {
            'id': 3, 'pos': (2, 0), 'is_sink': False,
            'battery': 4.0,  # Above b_min threshold
            'queue_bits': {4: 800.0},   # Has data for sink 4
            'energy_deficit_queue': 0.0,
            'env_eh_state': 0
        }
    ]
    
    sinks = [
        {'id': 4, 'pos': (3, 0), 'is_sink': True}
    ]
    
    # Create potential links (linear topology: 1->2->3->4)
    potential_links = {
        1: [2],
        2: [3],
        3: [4]
    }
    
    # Create channel gains (good channels)
    current_channel_gains = {
        (1, 2): 0.1,  # Good channel
        (2, 3): 0.1,  # Good channel
        (3, 4): 0.1   # Good channel
    }
    
    return nodes, sinks, potential_links, current_channel_gains

def test_hop_distance_calculation():
    """Test the hop distance calculation function."""
    print("Testing hop distance calculation...")
    
    nodes, sinks, potential_links, _ = create_test_network()
    
    # Test hop distances
    hop_1_to_4 = calculate_hop_distance(1, 4, potential_links, nodes, sinks)
    hop_2_to_4 = calculate_hop_distance(2, 4, potential_links, nodes, sinks)
    hop_3_to_4 = calculate_hop_distance(3, 4, potential_links, nodes, sinks)
    
    print(f"  Hop distance from node 1 to sink 4: {hop_1_to_4}")
    print(f"  Hop distance from node 2 to sink 4: {hop_2_to_4}")
    print(f"  Hop distance from node 3 to sink 4: {hop_3_to_4}")
    
    # Verify expected distances
    assert hop_1_to_4 == 3, f"Expected hop distance 3, got {hop_1_to_4}"
    assert hop_2_to_4 == 2, f"Expected hop distance 2, got {hop_2_to_4}"
    assert hop_3_to_4 == 1, f"Expected hop distance 1, got {hop_3_to_4}"
    
    print("  ✓ Hop distance calculation test passed!")

def test_gdts_parameters():
    """Test that GDTS parameters are correctly set."""
    print("Testing GDTS parameters...")
    
    print(f"  p_min = {GDTS_P_MIN_W} W")
    print(f"  p_max = {GDTS_P_MAX_W} W")
    print(f"  e_min = {GDTS_E_MIN_W} W")
    print(f"  e_max = {GDTS_E_MAX_W} W")
    print(f"  b_min = {GDTS_B_MIN_J} J")
    print(f"  γ_min = {GDTS_GAMMA_MIN_dB} dB = {GDTS_GAMMA_MIN:.2f} (linear)")
    
    # Verify parameters match Jiang's paper Table 2
    assert GDTS_P_MIN_W == 0.5, f"Expected p_min = 0.5W, got {GDTS_P_MIN_W}"
    assert GDTS_P_MAX_W == 1.5, f"Expected p_max = 1.5W, got {GDTS_P_MAX_W}"
    assert GDTS_E_MIN_W == 0.5, f"Expected e_min = 0.5W, got {GDTS_E_MIN_W}"
    assert GDTS_E_MAX_W == 1.0, f"Expected e_max = 1.0W, got {GDTS_E_MAX_W}"
    assert GDTS_B_MIN_J == 3.0, f"Expected b_min = 3.0J, got {GDTS_B_MIN_J}"
    assert GDTS_GAMMA_MIN_dB == 10.0, f"Expected γ_min = 10dB, got {GDTS_GAMMA_MIN_dB}"
    
    print("  ✓ GDTS parameters test passed!")

def test_gdts_algorithm_basic():
    """Test basic GDTS algorithm functionality."""
    print("Testing basic GDTS algorithm...")
    
    nodes, sinks, potential_links, current_channel_gains = create_test_network()
    
    # Test parameters
    current_slot = 0
    noise_power_W = 1e-12  # Low noise
    bandwidth = 1e6  # 1 MHz
    
    # Run GDTS algorithm
    actions = gdts_scheduling(current_slot, nodes, sinks, potential_links, 
                             current_channel_gains, noise_power_W, bandwidth)
    
    print("  GDTS algorithm results:")
    print(f"    Energy Tx states (y): {actions['y']}")
    print(f"    Energy Tx powers (e_hat): {actions['e_hat']}")
    print(f"    Link activations (x): {actions['x']}")
    print(f"    Data Tx powers (p): {actions['p']}")
    print(f"    Data flows (f): {actions['f']}")
    
    # Verify basic structure
    assert 'y' in actions, "Missing 'y' in actions"
    assert 'e_hat' in actions, "Missing 'e_hat' in actions"
    assert 'x' in actions, "Missing 'x' in actions"
    assert 'p' in actions, "Missing 'p' in actions"
    assert 'f' in actions, "Missing 'f' in actions"
    
    # Verify all nodes have energy decisions
    for node in nodes:
        node_id = node['id']
        assert node_id in actions['y'], f"Missing y decision for node {node_id}"
        assert node_id in actions['e_hat'], f"Missing e_hat decision for node {node_id}"
    
    # Check that energy cooperation is activated for node 1 (has surplus energy)
    # and node 2 needs energy
    assert actions['y'][1] == 1, "Node 1 should transmit energy (has surplus, neighbor needs energy)"
    
    print("  ✓ Basic GDTS algorithm test passed!")

def test_gdts_no_ec_algorithm():
    """Test GDTS without energy cooperation."""
    print("Testing GDTS without energy cooperation...")
    
    nodes, sinks, potential_links, current_channel_gains = create_test_network()
    
    # Test parameters
    current_slot = 0
    noise_power_W = 1e-12  # Low noise
    bandwidth = 1e6  # 1 MHz
    
    # Run GDTS-NoEC algorithm
    actions = gdts_no_ec_scheduling(current_slot, nodes, sinks, potential_links, 
                                   current_channel_gains, noise_power_W, bandwidth)
    
    print("  GDTS-NoEC algorithm results:")
    print(f"    Energy Tx states (y): {actions['y']}")
    print(f"    Energy Tx powers (e_hat): {actions['e_hat']}")
    print(f"    Link activations (x): {actions['x']}")
    print(f"    Data Tx powers (p): {actions['p']}")
    print(f"    Data flows (f): {actions['f']}")
    
    # Verify no energy cooperation
    for node in nodes:
        node_id = node['id']
        assert actions['y'][node_id] == 0, f"Node {node_id} should not transmit energy in NoEC"
        assert actions['e_hat'][node_id] == 0.0, f"Node {node_id} should have zero energy power in NoEC"
    
    print("  ✓ GDTS-NoEC algorithm test passed!")

def run_all_tests():
    """Run all GDTS tests."""
    print("=" * 60)
    print("GDTS Algorithm Test Suite")
    print("=" * 60)
    
    try:
        test_gdts_parameters()
        print()
        test_hop_distance_calculation()
        print()
        test_gdts_algorithm_basic()
        print()
        test_gdts_no_ec_algorithm()
        print()
        print("=" * 60)
        print("✓ All GDTS tests passed successfully!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
